<?php
/**
 * Xun Framework 文档系统 - 快速开始页面
 *
 * 提供详细的安装指南、基础配置和第一个选项页面的创建教程。
 * 采用与首页一致的设计风格和交互模式。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

// 页面配置
$header_config = [
    'site_title' => 'Xun Framework',
    'site_description' => '现代化的WordPress选项框架',
    'page_title' => '快速开始',
    'page_description' => '5分钟快速上手Xun Framework，创建你的第一个WordPress选项页面',
    'keywords' => 'WordPress, Framework, Options, PHP, TailwindCSS, 快速开始, 安装指南, 教程',
    'current_page' => 'getting-started'
];

$footer_config = [
    'site_title' => 'Xun Framework',
    'site_description' => '现代化的WordPress选项框架',
    'version' => '1.0.0',
    'total_fields' => 23,
    'show_stats' => true
];

// 环境要求
$requirements = [
    [
        'title' => 'PHP 版本',
        'requirement' => 'PHP 8.1+',
        'description' => '支持最新的PHP特性和性能优化',
        'status' => 'required'
    ],
    [
        'title' => 'WordPress 版本',
        'requirement' => 'WordPress 6.8.1+',
        'description' => '兼容最新的WordPress核心功能',
        'status' => 'required'
    ],
    [
        'title' => 'Node.js',
        'requirement' => 'Node.js 16+',
        'description' => '用于构建和开发工具（可选）',
        'status' => 'optional'
    ],
    [
        'title' => '浏览器支持',
        'requirement' => '现代浏览器',
        'description' => 'Chrome 90+, Firefox 88+, Safari 14+',
        'status' => 'required'
    ]
];

// 安装步骤
$installation_steps = [
    [
        'step' => '1',
        'title' => '下载框架',
        'description' => '从GitHub下载最新版本或使用Composer安装',
        'methods' => [
            [
                'type' => 'composer',
                'title' => 'Composer 安装（推荐）',
                'code' => 'composer require xuntheme/xun-framework',
                'description' => '使用Composer包管理器自动处理依赖关系'
            ],
            [
                'type' => 'github',
                'title' => 'GitHub 下载',
                'code' => 'git clone https://github.com/xuntheme/xun-framework.git',
                'description' => '直接从GitHub仓库克隆最新代码'
            ],
            [
                'type' => 'manual',
                'title' => '手动下载',
                'code' => 'https://github.com/xuntheme/xun-framework/releases',
                'description' => '下载ZIP文件并解压到项目目录'
            ]
        ]
    ],
    [
        'step' => '2',
        'title' => '集成到项目',
        'description' => '将框架集成到WordPress主题或插件中',
        'methods' => [
            [
                'type' => 'theme',
                'title' => '主题集成',
                'code' => "// 在主题的 functions.php 中添加\nrequire_once get_template_directory() . '/xun-framework/xun-framework.php';",
                'description' => '适合主题开发者，提供主题选项功能'
            ],
            [
                'type' => 'plugin',
                'title' => '插件集成',
                'code' => "// 在插件主文件中添加\nrequire_once plugin_dir_path(__FILE__) . 'xun-framework/xun-framework.php';",
                'description' => '适合插件开发者，创建插件设置页面'
            ]
        ]
    ]
];

// 第一个示例配置
$first_example = [
    'title' => '创建你的第一个选项页面',
    'description' => '以下是一个完整的示例，展示如何创建包含多种字段类型的选项页面',
    'code' => '<?php
// 1. 创建选项页面
XUN::createOptions( \'my_theme_options\', array(
    \'menu_title\'      => \'主题设置\',
    \'menu_slug\'       => \'my-theme-options\',
    \'menu_capability\' => \'manage_options\',
    \'framework_title\' => \'我的主题设置\',
    \'framework_class\' => \'wrap\',
    \'theme\'           => \'dark\',
    \'ajax_save\'       => true,
    \'show_reset\'      => true,
) );

// 2. 添加基本设置区块
XUN::createSection( \'my_theme_options\', array(
    \'title\'  => \'基本设置\',
    \'icon\'   => \'dashicons-admin-generic\',
    \'fields\' => array(
        array(
            \'id\'          => \'site_logo\',
            \'type\'        => \'media\',
            \'title\'       => \'网站Logo\',
            \'desc\'        => \'上传您的网站Logo图片\',
            \'library\'     => \'image\',
            \'preview\'     => true,
        ),
        array(
            \'id\'          => \'theme_color\',
            \'type\'        => \'color\',
            \'title\'       => \'主题颜色\',
            \'desc\'        => \'选择网站的主要颜色\',
            \'default\'     => \'#2563eb\',
            \'alpha\'       => true,
        ),
        array(
            \'id\'          => \'site_layout\',
            \'type\'        => \'select\',
            \'title\'       => \'网站布局\',
            \'desc\'        => \'选择网站的整体布局样式\',
            \'options\'     => array(
                \'boxed\'     => \'盒式布局\',
                \'wide\'      => \'宽屏布局\',
                \'full\'      => \'全屏布局\',
            ),
            \'default\'     => \'wide\',
        ),
    ),
) );

// 3. 获取选项值
$logo = XUN::get_option( \'my_theme_options\', \'site_logo\' );
$color = XUN::get_option( \'my_theme_options\', \'theme_color\' );
$layout = XUN::get_option( \'my_theme_options\', \'site_layout\' );'
];

// 常见问题
$faqs = [
    [
        'question' => '框架是否与我的主题兼容？',
        'answer' => 'Xun Framework设计为通用框架，与任何WordPress主题兼容。它不会影响主题的前端样式，只提供后台选项管理功能。'
    ],
    [
        'question' => '是否需要编程经验？',
        'answer' => '基础使用只需要复制粘贴代码即可。对于高级定制，建议具备基础的PHP和WordPress开发知识。'
    ],
    [
        'question' => '框架会影响网站性能吗？',
        'answer' => '不会。框架采用懒加载和优化的代码结构，只在需要时加载资源，对前端性能影响微乎其微。'
    ],
    [
        'question' => '如何获得技术支持？',
        'answer' => '您可以通过GitHub Issues提交问题，或访问我们的文档网站获取详细教程和API参考。'
    ]
];

// 引入通用头部
include 'includes/header.php';
?>

<body class="bg-gray-50 text-gray-900 antialiased">
    <!-- 页面容器 -->
    <div class="min-h-screen">

        <!-- 主要内容区域 -->
        <main class="flex-1">
            <!-- Hero区域 -->
            <section class="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-20">
                <!-- 背景装饰 -->
                <div class="absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
                <div class="absolute bottom-0 left-0 -mb-20 -ml-20 w-80 h-80 bg-gradient-to-tr from-green-400/20 to-blue-400/20 rounded-full blur-3xl"></div>

                <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center">
                        <!-- 面包屑导航 -->
                        <nav class="flex justify-center mb-8" aria-label="Breadcrumb">
                            <ol class="flex items-center space-x-2 text-sm">
                                <li>
                                    <a href="index.php" class="text-gray-500 hover:text-blue-600 transition-colors">首页</a>
                                </li>
                                <li class="text-gray-400">/</li>
                                <li class="text-blue-600 font-medium">快速开始</li>
                            </ol>
                        </nav>

                        <h1 class="text-3xl lg:text-5xl font-bold text-gray-900 mb-6">
                            <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                快速开始
                            </span>
                        </h1>

                        <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                            5分钟快速上手Xun Framework，从安装到创建第一个选项页面，
                            让你的WordPress项目拥有专业的后台管理界面。
                        </p>

                        <!-- 快速导航 -->
                        <div class="flex flex-wrap justify-center gap-4 mb-12">
                            <a href="#requirements" class="inline-flex items-center px-6 py-3 bg-white border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-50 hover:border-gray-300 transition-all duration-300 shadow-sm">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                环境要求
                            </a>
                            <a href="#installation" class="inline-flex items-center px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 shadow-sm">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                开始安装
                            </a>
                            <a href="#first-example" class="inline-flex items-center px-6 py-3 bg-white border border-gray-200 rounded-lg text-gray-700 hover:bg-gray-50 hover:border-gray-300 transition-all duration-300 shadow-sm">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4"></path>
                                </svg>
                                第一个示例
                            </a>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 环境要求区域 -->
            <section id="requirements" class="py-20 bg-white">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-16">
                        <h2 class="text-3xl font-bold text-gray-900 mb-4">环境要求</h2>
                        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                            在开始使用Xun Framework之前，请确保您的开发环境满足以下要求
                        </p>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <?php foreach ($requirements as $req): ?>
                        <div class="group p-6 bg-gray-50 rounded-2xl border border-gray-200 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0">
                                    <?php if ($req['status'] === 'required'): ?>
                                    <div class="w-10 h-10 bg-red-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-5 h-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                        </svg>
                                    </div>
                                    <?php else: ?>
                                    <div class="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center">
                                        <svg class="w-5 h-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                        </svg>
                                    </div>
                                    <?php endif; ?>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                        <?php echo htmlspecialchars($req['title']); ?>
                                    </h3>
                                    <p class="text-blue-600 font-medium mb-2">
                                        <?php echo htmlspecialchars($req['requirement']); ?>
                                    </p>
                                    <p class="text-gray-600 text-sm">
                                        <?php echo htmlspecialchars($req['description']); ?>
                                    </p>
                                    <?php if ($req['status'] === 'required'): ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800 mt-2">
                                        必需
                                    </span>
                                    <?php else: ?>
                                    <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800 mt-2">
                                        可选
                                    </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </section>

            <!-- 安装步骤区域 -->
            <section id="installation" class="py-20 bg-gray-50">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-16">
                        <h2 class="text-3xl font-bold text-gray-900 mb-4">安装步骤</h2>
                        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                            选择最适合您项目的安装方式，我们提供多种集成方法
                        </p>
                    </div>

                    <?php foreach ($installation_steps as $step): ?>
                    <div class="mb-16 last:mb-0">
                        <!-- 步骤标题 -->
                        <div class="flex items-center mb-8">
                            <div class="flex-shrink-0 w-12 h-12 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold text-lg">
                                <?php echo $step['step']; ?>
                            </div>
                            <div class="ml-4">
                                <h3 class="text-2xl font-bold text-gray-900">
                                    <?php echo htmlspecialchars($step['title']); ?>
                                </h3>
                                <p class="text-gray-600 mt-1">
                                    <?php echo htmlspecialchars($step['description']); ?>
                                </p>
                            </div>
                        </div>

                        <!-- 安装方法选项卡 -->
                        <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                            <!-- 选项卡头部 -->
                            <div class="border-b border-gray-200">
                                <nav class="flex space-x-8 px-6" aria-label="Tabs">
                                    <?php foreach ($step['methods'] as $index => $method): ?>
                                    <button
                                        class="tab-button py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-300 <?php echo $index === 0 ? 'border-blue-500 text-blue-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'; ?>"
                                        data-tab="<?php echo $step['step'] . '-' . $method['type']; ?>"
                                    >
                                        <?php echo htmlspecialchars($method['title']); ?>
                                    </button>
                                    <?php endforeach; ?>
                                </nav>
                            </div>

                            <!-- 选项卡内容 -->
                            <div class="p-6">
                                <?php foreach ($step['methods'] as $index => $method): ?>
                                <div
                                    class="tab-content <?php echo $index === 0 ? 'block' : 'hidden'; ?>"
                                    data-tab="<?php echo $step['step'] . '-' . $method['type']; ?>"
                                >
                                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                                        <!-- 说明文字 -->
                                        <div>
                                            <h4 class="text-lg font-semibold text-gray-900 mb-3">
                                                <?php echo htmlspecialchars($method['title']); ?>
                                            </h4>
                                            <p class="text-gray-600 mb-4">
                                                <?php echo htmlspecialchars($method['description']); ?>
                                            </p>

                                            <?php if ($method['type'] === 'composer'): ?>
                                            <div class="space-y-2">
                                                <div class="flex items-center text-sm text-green-600">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                    </svg>
                                                    自动处理依赖关系
                                                </div>
                                                <div class="flex items-center text-sm text-green-600">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                    </svg>
                                                    版本管理和更新
                                                </div>
                                                <div class="flex items-center text-sm text-green-600">
                                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                    </svg>
                                                    推荐的安装方式
                                                </div>
                                            </div>
                                            <?php endif; ?>
                                        </div>

                                        <!-- 代码示例 -->
                                        <div class="bg-gray-900 rounded-xl p-6 overflow-hidden">
                                            <div class="flex items-center justify-between mb-4">
                                                <div class="flex space-x-2">
                                                    <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                                    <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                                    <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                                </div>
                                                <span class="text-gray-400 text-sm">Terminal</span>
                                            </div>
                                            <pre class="text-sm overflow-x-auto"><code class="language-bash"><?php echo htmlspecialchars($method['code']); ?></code></pre>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </section>

            <!-- 第一个示例区域 -->
            <section id="first-example" class="py-20 bg-white">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-16">
                        <h2 class="text-3xl font-bold text-gray-900 mb-4">第一个示例</h2>
                        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                            <?php echo htmlspecialchars($first_example['description']); ?>
                        </p>
                    </div>

                    <div class="bg-gray-900 rounded-2xl p-8 overflow-hidden">
                        <div class="flex items-center justify-between mb-6">
                            <div class="flex space-x-2">
                                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            </div>
                            <div class="flex items-center space-x-4">
                                <span class="text-gray-400 text-sm">functions.php</span>
                                <button
                                    class="copy-button inline-flex items-center px-3 py-1 bg-gray-700 hover:bg-gray-600 text-gray-300 text-sm rounded-md transition-colors duration-300"
                                    data-copy-target="first-example-code"
                                >
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                    复制
                                </button>
                            </div>
                        </div>
                        <pre id="first-example-code" class="text-sm overflow-x-auto"><code class="language-php"><?php echo htmlspecialchars($first_example['code']); ?></code></pre>
                    </div>

                    <!-- 代码说明 -->
                    <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8">
                        <div class="text-center p-6 bg-blue-50 rounded-xl">
                            <div class="w-12 h-12 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <span class="text-white font-bold">1</span>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">创建选项页面</h3>
                            <p class="text-gray-600 text-sm">
                                使用 <code class="bg-white px-2 py-1 rounded text-blue-600">XUN::createOptions()</code>
                                创建一个新的选项页面，配置基本参数。
                            </p>
                        </div>
                        <div class="text-center p-6 bg-green-50 rounded-xl">
                            <div class="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <span class="text-white font-bold">2</span>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">添加字段区块</h3>
                            <p class="text-gray-600 text-sm">
                                使用 <code class="bg-white px-2 py-1 rounded text-green-600">XUN::createSection()</code>
                                添加字段区块，包含多种字段类型。
                            </p>
                        </div>
                        <div class="text-center p-6 bg-purple-50 rounded-xl">
                            <div class="w-12 h-12 bg-purple-600 rounded-lg flex items-center justify-center mx-auto mb-4">
                                <span class="text-white font-bold">3</span>
                            </div>
                            <h3 class="text-lg font-semibold text-gray-900 mb-2">获取选项值</h3>
                            <p class="text-gray-600 text-sm">
                                使用 <code class="bg-white px-2 py-1 rounded text-purple-600">XUN::get_option()</code>
                                在主题中获取和使用保存的选项值。
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 常见问题区域 -->
            <section id="faq" class="py-20 bg-gray-50">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-16">
                        <h2 class="text-3xl font-bold text-gray-900 mb-4">常见问题</h2>
                        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                            解答使用Xun Framework过程中的常见疑问
                        </p>
                    </div>

                    <div class="max-w-4xl mx-auto space-y-6">
                        <?php foreach ($faqs as $index => $faq): ?>
                        <div class="bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden">
                            <button
                                class="faq-button w-full px-6 py-4 text-left flex items-center justify-between hover:bg-gray-50 transition-colors duration-300"
                                data-faq="<?php echo $index; ?>"
                            >
                                <span class="text-lg font-medium text-gray-900">
                                    <?php echo htmlspecialchars($faq['question']); ?>
                                </span>
                                <svg class="faq-icon w-5 h-5 text-gray-500 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </button>
                            <div class="faq-content hidden px-6 pb-4">
                                <p class="text-gray-600 leading-relaxed">
                                    <?php echo htmlspecialchars($faq['answer']); ?>
                                </p>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </section>
        </main>

<?php
// 引入通用底部
include 'includes/footer.php';
?>
