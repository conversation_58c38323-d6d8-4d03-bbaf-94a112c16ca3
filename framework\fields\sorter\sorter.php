<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework Sorter 字段类型
 * 
 * 这个字段类型提供了一个现代化的拖拽排序界面，支持启用/禁用项目、
 * 实时预览、批量操作等高级功能。采用TailwindCSS设计，提供优秀的用户体验。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Field_sorter' ) ) {
    
    /**
     * XUN_Field_sorter 排序器字段类
     * 
     * 功能特性：
     * - 拖拽排序功能
     * - 启用/禁用项目管理
     * - 现代化TailwindCSS界面
     * - 移动端友好设计
     * - 实时预览功能
     * - 批量操作支持
     * - 完整的数据验证
     * 
     * @since 1.0
     */
    class XUN_Field_sorter extends XUN_Fields {
        
        /**
         * 构造函数
         * 
         * 初始化排序器字段实例。
         * 
         * @since 1.0
         * 
         * @param array  $field  字段配置
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field = array(), $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }
        
        /**
         * 渲染排序器字段
         * 
         * 生成现代化的拖拽排序界面，包括启用和禁用区域。
         * 
         * @since 1.0
         */
        public function render() {
            
            // 输出前置内容
            echo $this->field_before();
            
            // 获取字段配置
            $options = ! empty( $this->field['options'] ) ? $this->field['options'] : array();
            $enabled_title = ! empty( $this->field['enabled_title'] ) ? $this->field['enabled_title'] : '已启用项目';
            $disabled_title = ! empty( $this->field['disabled_title'] ) ? $this->field['disabled_title'] : '可用项目';
            $show_disabled = ! isset( $this->field['disabled'] ) || $this->field['disabled'] !== false;
            $allow_all_none = ! empty( $this->field['allow_all_none'] );
            
            // 处理保存的值
            $enabled_items = array();
            $disabled_items = array();
            
            if ( ! empty( $this->value ) && is_array( $this->value ) ) {
                $enabled_items = ! empty( $this->value['enabled'] ) ? $this->value['enabled'] : array();
                $disabled_items = ! empty( $this->value['disabled'] ) ? $this->value['disabled'] : array();
            }
            
            // 确保所有选项都被分类
            foreach ( $options as $key => $label ) {
                if ( ! isset( $enabled_items[ $key ] ) && ! isset( $disabled_items[ $key ] ) ) {
                    $disabled_items[ $key ] = $label;
                }
            }
            
            // 主容器 - 现代化设计
            echo '<div class="xun-sorter-field" data-field-id="' . esc_attr( $this->field['id'] ) . '">';
            
            // 批量操作工具栏（如果启用）
            if ( $allow_all_none ) {
                echo '<div class="mb-4 flex flex-wrap gap-2">';
                echo '<button type="button" class="xun-sorter-enable-all inline-flex items-center rounded-md bg-blue-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-blue-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-blue-600 transition-colors">';
                echo '<svg class="mr-1.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5" /></svg>';
                echo '全部启用</button>';
                echo '<button type="button" class="xun-sorter-disable-all inline-flex items-center rounded-md bg-gray-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-gray-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-gray-600 transition-colors">';
                echo '<svg class="mr-1.5 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12" /></svg>';
                echo '全部禁用</button>';
                echo '</div>';
            }
            
            // 响应式网格布局
            $grid_classes = $show_disabled ? 'grid grid-cols-1 lg:grid-cols-2 gap-6' : 'w-full';
            echo '<div class="' . $grid_classes . '">';
            
            // 已启用项目区域
            echo '<div class="xun-sorter-enabled-section">';
            echo '<div class="mb-3 flex items-center justify-between">';
            echo '<h4 class="text-sm font-medium text-gray-900">' . esc_html( $enabled_title ) . '</h4>';
            echo '<span class="xun-enabled-count inline-flex items-center rounded-full bg-blue-100 px-2.5 py-0.5 text-xs font-medium text-blue-800">' . count( $enabled_items ) . '</span>';
            echo '</div>';
            
            echo '<div class="xun-sorter-enabled min-h-[120px] rounded-lg border-2 border-dashed border-blue-300 bg-blue-50/50 p-4 transition-colors" data-type="enabled">';
            
            if ( ! empty( $enabled_items ) ) {
                foreach ( $enabled_items as $key => $label ) {
                    $this->render_sortable_item( $key, $label, 'enabled' );
                }
            } else {
                echo '<div class="xun-empty-placeholder text-center py-8 text-gray-500">';
                echo '<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />';
                echo '</svg>';
                echo '<p class="mt-2 text-sm">拖拽项目到此处启用</p>';
                echo '</div>';
            }
            
            echo '</div>';
            echo '</div>';
            
            // 禁用项目区域（如果显示）
            if ( $show_disabled ) {
                echo '<div class="xun-sorter-disabled-section">';
                echo '<div class="mb-3 flex items-center justify-between">';
                echo '<h4 class="text-sm font-medium text-gray-900">' . esc_html( $disabled_title ) . '</h4>';
                echo '<span class="xun-disabled-count inline-flex items-center rounded-full bg-gray-100 px-2.5 py-0.5 text-xs font-medium text-gray-800">' . count( $disabled_items ) . '</span>';
                echo '</div>';
                
                echo '<div class="xun-sorter-disabled min-h-[120px] rounded-lg border-2 border-dashed border-gray-300 bg-gray-50/50 p-4 transition-colors" data-type="disabled">';
                
                if ( ! empty( $disabled_items ) ) {
                    foreach ( $disabled_items as $key => $label ) {
                        $this->render_sortable_item( $key, $label, 'disabled' );
                    }
                } else {
                    echo '<div class="xun-empty-placeholder text-center py-8 text-gray-500">';
                    echo '<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                    echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />';
                    echo '</svg>';
                    echo '<p class="mt-2 text-sm">拖拽项目到此处禁用</p>';
                    echo '</div>';
                }
                
                echo '</div>';
                echo '</div>';
            }
            
            echo '</div>'; // 结束网格布局
            
            // 实时预览区域（如果启用）
            if ( ! empty( $this->field['show_preview'] ) ) {
                echo '<div class="mt-6 rounded-lg bg-gray-50 p-4">';
                echo '<h5 class="mb-3 text-sm font-medium text-gray-900">实时预览</h5>';
                echo '<div class="xun-sorter-preview text-sm text-gray-600"></div>';
                echo '</div>';
            }
            
            echo '</div>'; // 结束主容器
            
            // 输出后置内容
            echo $this->field_after();
        }
        
        /**
         * 渲染可排序项目
         * 
         * 生成单个可拖拽的项目元素。
         * 
         * @since 1.0
         * 
         * @param string $key   项目键值
         * @param string $label 项目标签
         * @param string $type  项目类型（enabled/disabled）
         */
        private function render_sortable_item( $key, $label, $type ) {
            $input_name = $this->field_name( '[' . $type . '][' . $key . ']' );
            $is_enabled = $type === 'enabled';
            
            // 项目容器样式
            $item_classes = 'xun-sortable-item group relative mb-2 cursor-move rounded-lg border bg-white p-3 shadow-sm transition-all hover:shadow-md';
            $item_classes .= $is_enabled ? ' border-blue-200 hover:border-blue-300' : ' border-gray-200 hover:border-gray-300';
            
            echo '<div class="' . $item_classes . '" data-key="' . esc_attr( $key ) . '">';
            
            // 隐藏输入字段
            echo '<input type="hidden" name="' . esc_attr( $input_name ) . '" value="' . esc_attr( $label ) . '" />';
            
            // 项目内容
            echo '<div class="flex items-center justify-between">';
            
            // 拖拽手柄和标签
            echo '<div class="flex items-center space-x-3">';
            echo '<div class="xun-drag-handle text-gray-400 group-hover:text-gray-600 transition-colors">';
            echo '<svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />';
            echo '</svg>';
            echo '</div>';
            echo '<span class="text-sm font-medium text-gray-900">' . esc_html( $label ) . '</span>';
            echo '</div>';
            
            // 状态指示器
            echo '<div class="flex items-center space-x-2">';
            if ( $is_enabled ) {
                echo '<span class="inline-flex items-center rounded-full bg-green-100 px-2 py-1 text-xs font-medium text-green-800">';
                echo '<svg class="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">';
                echo '<path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" />';
                echo '</svg>已启用</span>';
            } else {
                echo '<span class="inline-flex items-center rounded-full bg-gray-100 px-2 py-1 text-xs font-medium text-gray-800">';
                echo '<svg class="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20">';
                echo '<path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />';
                echo '</svg>已禁用</span>';
            }
            echo '</div>';
            
            echo '</div>';
            echo '</div>';
        }
        
        /**
         * 加载字段资源
         * 
         * 加载排序器字段所需的JavaScript和CSS资源。
         * 
         * @since 1.0
         */
        public function enqueue() {

            // 加载自定义JavaScript（使用原生拖拽API，无需jQuery UI）
            wp_enqueue_script(
                'xun-field-sorter',
                XUN_Setup::$url . '/assets/js/fields/sorter.js',
                array( 'jquery' ),
                XUN_Setup::$version,
                true
            );
            
            // 本地化脚本
            wp_localize_script( 'xun-field-sorter', 'xunSorter', array(
                'strings' => array(
                    'enableAll'  => '全部启用',
                    'disableAll' => '全部禁用',
                    'enabled'    => '已启用',
                    'disabled'   => '已禁用',
                    'dragHere'   => '拖拽项目到此处',
                    'empty'      => '暂无项目',
                ),
                'nonce' => wp_create_nonce( 'xun_sorter_nonce' ),
            ) );
        }
        
        /**
         * 验证和清理字段数据
         * 
         * 对排序器字段的数据进行验证和清理。
         * 
         * @since 1.0
         * 
         * @param mixed $value 要验证的值
         * 
         * @return array 清理后的数据
         */
        public function validate( $value ) {
            
            if ( ! is_array( $value ) ) {
                return array(
                    'enabled'  => array(),
                    'disabled' => array(),
                );
            }
            
            $enabled = ! empty( $value['enabled'] ) && is_array( $value['enabled'] ) ? $value['enabled'] : array();
            $disabled = ! empty( $value['disabled'] ) && is_array( $value['disabled'] ) ? $value['disabled'] : array();
            
            // 清理数据
            $enabled = array_map( 'sanitize_text_field', $enabled );
            $disabled = array_map( 'sanitize_text_field', $disabled );
            
            // 验证选项是否在允许的范围内
            $allowed_options = ! empty( $this->field['options'] ) ? $this->field['options'] : array();
            
            if ( ! empty( $allowed_options ) ) {
                $enabled = array_intersect_key( $enabled, $allowed_options );
                $disabled = array_intersect_key( $disabled, $allowed_options );
            }
            
            // 应用自定义验证过滤器
            $value = array(
                'enabled'  => $enabled,
                'disabled' => $disabled,
            );
            
            $value = apply_filters( 'xun_validate_sorter_field', $value, $this->field );
            $value = apply_filters( "xun_validate_sorter_field_{$this->field['id']}", $value, $this->field );
            
            return $value;
        }
    }
}
