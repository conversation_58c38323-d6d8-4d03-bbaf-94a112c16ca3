{"name": "xun-framework-docs", "version": "1.0.0", "description": "Xun Framework 文档系统", "private": true, "scripts": {"dev": "npx tailwindcss --input=assets/css/input.css --output=assets/css/style.css --watch", "build": "npx tailwindcss --input=assets/css/input.css --output=assets/css/style.css --minify", "build:watch": "npm run build -- --watch"}, "devDependencies": {"@tailwindcss/cli": "^4.1.11", "tailwindcss": "^4.1.11"}, "engines": {"node": ">=18.0.0"}, "keywords": ["wordpress", "framework", "documentation", "tailwindcss", "php"], "author": "June", "license": "MIT"}