<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework 图标选择字段类型
 * 
 * 这个类实现了图标选择字段功能，允许用户从WordPress Dashicons图标库中选择图标。
 * 支持图标预览、搜索、分类浏览等功能。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Field_icon' ) ) {
    
    /**
     * XUN_Field_icon 图标选择字段类
     * 
     * 提供图标选择字段的完整功能，包括：
     * - 图标库浏览
     * - 图标搜索
     * - 图标预览
     * - 尺寸选择
     * - 实时预览
     * 
     * @since 1.0
     */
    class XUN_Field_icon extends XUN_Fields {
        
        /**
         * 构造函数
         * 
         * 初始化图标选择字段实例。
         * 
         * @since 1.0
         * 
         * @param array  $field  字段配置数组
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }
        
        /**
         * 渲染图标选择字段
         * 
         * 输出图标选择字段的HTML代码，包括图标选择器和预览功能。
         * 
         * @since 1.0
         */
        public function render() {
            
            // 字段配置
            $size = ! empty( $this->field['size'] ) ? $this->field['size'] : '24';
            $show_search = ! empty( $this->field['show_search'] ) ? $this->field['show_search'] : true;
            $show_preview = ! empty( $this->field['show_preview'] ) ? $this->field['show_preview'] : true;
            $placeholder = ! empty( $this->field['placeholder'] ) ? $this->field['placeholder'] : '选择图标...';
            
            // 当前值
            $current_value = ! empty( $this->value ) ? $this->value : '';
            
            // 获取可用图标列表
            $available_icons = XUN_Icons::get_available_icons();
            
            // 字段前置内容
            echo $this->field_before();

            // 添加内联样式
            echo '<style>
                .xun-icon-field { position: relative; }
                .xun-icon-current {
                    display: flex; align-items: center; gap: 12px; padding: 12px;
                    border: 1px solid #d1d5db; border-radius: 6px; background: #ffffff;
                    cursor: pointer; transition: all 0.2s ease;
                }
                .xun-icon-current:hover { border-color: #3b82f6; box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }
                .xun-icon-preview { display: flex; align-items: center; gap: 8px; flex: 1; }
                .xun-icon-preview .dashicons { flex-shrink: 0; color: #374151; font-size: 24px; }
                .xun-icon-name { font-size: 14px; color: #374151; font-weight: 500; }
                .xun-icon-placeholder { flex: 1; color: #9ca3af; font-size: 14px; }
                .xun-icon-select-btn, .xun-icon-clear-btn {
                    padding: 6px 12px; font-size: 12px; border: 1px solid #d1d5db;
                    border-radius: 4px; background: #ffffff; color: #374151;
                    cursor: pointer; transition: all 0.2s ease;
                }
                .xun-icon-select-btn:hover, .xun-icon-clear-btn:hover { background: #f9fafb; border-color: #3b82f6; }
                .xun-icon-clear-btn { color: #dc2626; border-color: #fecaca; }
                .xun-icon-clear-btn:hover { background: #fef2f2; border-color: #dc2626; }
                .xun-icon-modal {
                    position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                    background: rgba(0, 0, 0, 0.5); z-index: 99999;
                    display: flex; align-items: center; justify-content: center;
                    backdrop-filter: blur(12px);
                    -webkit-backdrop-filter: blur(12px);
                }
                .xun-icon-modal-content {
                    background: #ffffff; border-radius: 8px;
                    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
                    max-width: 600px; width: 90%; max-height: 80vh;
                    display: flex; flex-direction: column;
                }
                .xun-icon-modal-header {
                    display: flex; align-items: center; justify-content: space-between;
                    padding: 20px; border-bottom: 1px solid #e5e7eb;
                }
                .xun-icon-modal-header h3 { margin: 0; font-size: 18px; font-weight: 600; color: #111827; }
                .xun-icon-modal-close {
                    background: none; border: none; font-size: 24px; color: #6b7280;
                    cursor: pointer; padding: 0; width: 32px; height: 32px;
                    display: flex; align-items: center; justify-content: center;
                    border-radius: 4px; transition: all 0.2s ease;
                }
                .xun-icon-modal-close:hover { background: #f3f4f6; color: #374151; }
                .xun-icon-search { padding: 20px; border-bottom: 1px solid #e5e7eb; }
                .xun-icon-search-input {
                    width: 100%; padding: 12px; border: 1px solid #d1d5db;
                    border-radius: 6px; font-size: 14px; outline: none;
                    transition: all 0.2s ease;
                }
                .xun-icon-search-input:focus { border-color: #3b82f6; box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1); }
                .xun-icon-grid {
                    padding: 20px; display: grid;
                    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
                    gap: 12px; max-height: 400px; overflow-y: auto;
                }
                .xun-icon-item {
                    display: flex; flex-direction: column; align-items: center; gap: 8px;
                    padding: 16px 8px; border: 1px solid #e5e7eb; border-radius: 6px;
                    cursor: pointer; transition: all 0.2s ease; background: #ffffff;
                }
                .xun-icon-item:hover {
                    border-color: #3b82f6; background: #f8fafc;
                    transform: translateY(-1px); box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                }
                .xun-icon-item.selected {
                    border-color: #3b82f6; background: #eff6ff;
                    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
                }
                .xun-icon-item .dashicons { color: #374151; transition: color 0.2s ease; font-size: 24px; }
                .xun-icon-item:hover .dashicons, .xun-icon-item.selected .dashicons { color: #3b82f6; }
                .xun-icon-item-name {
                    font-size: 11px; color: #6b7280; text-align: center;
                    line-height: 1.3; word-break: break-all;
                }
                .xun-icon-item.selected .xun-icon-item-name { color: #3b82f6; font-weight: 500; }
                .xun-icon-modal-footer {
                    display: flex; justify-content: flex-end; gap: 12px;
                    padding: 20px; border-top: 1px solid #e5e7eb;
                }
                .xun-icon-modal-cancel, .xun-icon-modal-confirm {
                    padding: 8px 16px; border: 1px solid #d1d5db; border-radius: 4px;
                    font-size: 14px; cursor: pointer; transition: all 0.2s ease;
                }
                .xun-icon-modal-cancel { background: #ffffff; color: #374151; }
                .xun-icon-modal-cancel:hover { background: #f9fafb; }
                .xun-icon-modal-confirm { background: #3b82f6; color: #ffffff; border-color: #3b82f6; }
                .xun-icon-modal-confirm:hover { background: #2563eb; border-color: #2563eb; }
                .xun-icon-live-preview {
                    margin-top: 16px; padding: 16px; background: #f9fafb;
                    border: 1px solid #e5e7eb; border-radius: 6px;
                }
                .xun-icon-live-preview h4 { margin: 0 0 12px 0; font-size: 14px; font-weight: 600; color: #374151; }
                .xun-icon-preview-sizes { display: flex; gap: 16px; align-items: center; }
                .xun-icon-preview-item { display: flex; align-items: center; gap: 8px; }
                .xun-icon-preview-label { font-size: 12px; color: #6b7280; font-weight: 500; }
                .xun-icon-no-results { text-align: center; padding: 40px 20px; color: #6b7280; font-size: 14px; }
                @media (max-width: 640px) {
                    .xun-icon-modal-content { width: 95%; max-height: 90vh; }
                    .xun-icon-grid { grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)); gap: 8px; }
                    .xun-icon-item { padding: 12px 6px; }
                    .xun-icon-preview-sizes { flex-direction: column; align-items: flex-start; gap: 8px; }
                }
            </style>';

            // 输出字段前置内容
            echo $this->field_before();

            // 开始字段容器 - 添加data-field-id属性以与color字段保持一致
            echo '<div class="xun-icon-field" data-field-id="' . esc_attr( $this->field['id'] ) . '" data-size="' . esc_attr( $size ) . '">';
            
            // 隐藏的输入字段
            echo '<input type="hidden" name="' . esc_attr( $this->field_name() ) . '" value="' . esc_attr( $current_value ) . '" class="xun-icon-input" />';
            
            // 图标选择器容器
            echo '<div class="xun-icon-selector">';
            
            // 当前选择的图标显示 - 修复按钮布局，确保按钮始终在正确位置
            echo '<div class="xun-icon-current" data-placeholder="' . esc_attr( $placeholder ) . '">';
            if ( ! empty( $current_value ) ) {
                // 有图标时：显示图标预览 + 选择按钮 + 清除按钮
                echo '<div class="xun-icon-preview">';
                echo XUN_Icons::get_icon( $current_value, array( 'size' => $size, 'class' => 'w-6 h-6' ) );
                echo '<span class="xun-icon-name">' . esc_html( $current_value ) . '</span>';
                echo '</div>';
                echo '<button type="button" class="xun-icon-select-btn">选择图标</button>';
                echo '<button type="button" class="xun-icon-clear-btn">清除</button>';
            } else {
                // 无图标时：显示占位符 + 选择按钮
                echo '<div class="xun-icon-placeholder">' . esc_html( $placeholder ) . '</div>';
                echo '<button type="button" class="xun-icon-select-btn">选择图标</button>';
            }
            echo '</div>';
            
            // 图标选择弹窗 - 与color字段保持一致的结构和样式
            // 注意：弹窗HTML将通过JavaScript动态创建，与color字段保持一致的实现方式
            echo '<div class="xun-icon-modal-data" style="display: none;">';
            echo '<script type="application/json" class="xun-icon-config">';
            echo wp_json_encode( array(
                'fieldId' => $this->field['id'],
                'currentValue' => $current_value,
                'placeholder' => $placeholder,
                'showSearch' => $show_search,
                'showPreview' => $show_preview,
                'size' => $size,
                'availableIcons' => $available_icons,
                'messages' => array(
                    'selectIcon' => __( '选择图标', 'xun' ),
                    'searchPlaceholder' => __( '搜索图标...', 'xun' ),
                    'cancel' => __( '取消', 'xun' ),
                    'confirm' => __( '确认', 'xun' ),
                    'noIconsFound' => __( '未找到匹配的图标', 'xun' ),
                )
            ) );
            echo '</script>';
            echo '</div>';
            
            echo '</div>'; // .xun-icon-selector
            
            // 实时预览
            if ( $show_preview && ! empty( $current_value ) ) {
                echo '<div class="xun-icon-live-preview">';
                echo '<h4>预览效果：</h4>';
                echo '<div class="xun-icon-preview-sizes">';
                
                $preview_sizes = array( '16', '20', '24' );
                foreach ( $preview_sizes as $preview_size ) {
                    if ( XUN_Icons::icon_exists( $current_value ) ) {
                        echo '<div class="xun-icon-preview-item">';
                        echo '<span class="xun-icon-preview-label">' . $preview_size . 'px:</span>';
                        echo XUN_Icons::get_icon( $current_value, array( 
                            'size' => $preview_size, 
                            'class' => 'inline-block' 
                        ) );
                        echo '</div>';
                    }
                }
                
                echo '</div>';
                echo '</div>';
            }
            
            echo '</div>'; // .xun-icon-field

            // 输出字段后置内容
            echo $this->field_after();
        }
        
        /**
         * 加载字段资源
         *
         * 加载图标选择字段所需的JavaScript文件。
         *
         * @since 1.0
         */
        public function enqueue() {

            // 确保加载WordPress的dashicons
            wp_enqueue_style( 'dashicons' );

            // 加载字段专用JavaScript
            wp_enqueue_script(
                'xun-field-icon',
                XUN_Setup::$url . '/assets/js/fields/icon.js',
                array( 'jquery' ),
                XUN_Setup::$version,
                true
            );

            // 本地化脚本
            wp_localize_script( 'xun-field-icon', 'xunIconField', array(
                'searchPlaceholder' => __( '搜索图标...', 'xun' ),
                'noResults'         => __( '未找到匹配的图标', 'xun' ),
                'selectIcon'        => __( '选择图标', 'xun' ),
                'clearIcon'         => __( '清除图标', 'xun' ),
                'confirm'           => __( '确认', 'xun' ),
                'cancel'            => __( '取消', 'xun' ),
            ) );
        }
        
        /**
         * 验证字段值
         * 
         * 验证图标字段的值是否有效。
         * 
         * @since 1.0
         * 
         * @param mixed $value 要验证的值
         * 
         * @return mixed 验证后的值
         */
        public function validate( $value ) {
            
            // 如果值为空，返回空字符串
            if ( empty( $value ) ) {
                return '';
            }
            
            // 验证图标名称格式
            if ( ! preg_match( '/^[a-z0-9\-]+$/', $value ) ) {
                return '';
            }
            
            // 检查图标是否存在
            if ( ! XUN_Icons::icon_exists( $value ) ) {
                return '';
            }
            
            return sanitize_text_field( $value );
        }
    }
}
