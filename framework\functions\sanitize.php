<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework 数据清理函数
 * 
 * 这个文件包含了框架中使用的各种数据清理和验证函数。
 * 这些函数确保用户输入的数据是安全和有效的。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

/**
 * 清理文本字段
 * 
 * 清理和验证文本输入字段的值。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * 
 * @return string 清理后的文本
 */
if ( ! function_exists( 'xun_sanitize_text' ) ) {
    function xun_sanitize_text( $value ) {
        return sanitize_text_field( $value );
    }
}

/**
 * 清理文本域字段
 * 
 * 清理和验证文本域输入字段的值。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * 
 * @return string 清理后的文本
 */
if ( ! function_exists( 'xun_sanitize_textarea' ) ) {
    function xun_sanitize_textarea( $value ) {
        return sanitize_textarea_field( $value );
    }
}

/**
 * 清理邮箱字段
 * 
 * 清理和验证邮箱输入字段的值。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * 
 * @return string 清理后的邮箱地址
 */
if ( ! function_exists( 'xun_sanitize_email' ) ) {
    function xun_sanitize_email( $value ) {
        return sanitize_email( $value );
    }
}

/**
 * 清理URL字段
 * 
 * 清理和验证URL输入字段的值。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * 
 * @return string 清理后的URL
 */
if ( ! function_exists( 'xun_sanitize_url' ) ) {
    function xun_sanitize_url( $value ) {
        return esc_url_raw( $value );
    }
}

/**
 * 清理数字字段
 * 
 * 清理和验证数字输入字段的值。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * 
 * @return int 清理后的整数
 */
if ( ! function_exists( 'xun_sanitize_number' ) ) {
    function xun_sanitize_number( $value ) {
        return intval( $value );
    }
}

/**
 * 清理浮点数字段
 * 
 * 清理和验证浮点数输入字段的值。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * 
 * @return float 清理后的浮点数
 */
if ( ! function_exists( 'xun_sanitize_float' ) ) {
    function xun_sanitize_float( $value ) {
        return floatval( $value );
    }
}

/**
 * 清理布尔值字段
 * 
 * 清理和验证布尔值输入字段的值。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * 
 * @return bool 清理后的布尔值
 */
if ( ! function_exists( 'xun_sanitize_boolean' ) ) {
    function xun_sanitize_boolean( $value ) {
        return (bool) $value;
    }
}

/**
 * 清理颜色值字段
 * 
 * 清理和验证颜色值输入字段的值。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * 
 * @return string 清理后的颜色值
 */
if ( ! function_exists( 'xun_sanitize_color' ) ) {
    function xun_sanitize_color( $value ) {
        
        // 移除空格
        $value = trim( $value );
        
        // 如果为空，返回空字符串
        if ( empty( $value ) ) {
            return '';
        }
        
        // 检查是否为有效的十六进制颜色值
        if ( preg_match( '/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/', $value ) ) {
            return $value;
        }
        
        // 检查是否为有效的RGB颜色值
        if ( preg_match( '/^rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/', $value ) ) {
            return $value;
        }
        
        // 检查是否为有效的RGBA颜色值
        if ( preg_match( '/^rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)$/', $value ) ) {
            return $value;
        }
        
        // 如果都不匹配，返回空字符串
        return '';
    }
}

/**
 * 清理数组字段
 * 
 * 清理和验证数组输入字段的值。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * 
 * @return array 清理后的数组
 */
if ( ! function_exists( 'xun_sanitize_array' ) ) {
    function xun_sanitize_array( $value ) {
        
        if ( ! is_array( $value ) ) {
            return array();
        }
        
        $sanitized = array();
        
        foreach ( $value as $key => $val ) {
            $key = sanitize_key( $key );
            
            if ( is_array( $val ) ) {
                $sanitized[ $key ] = xun_sanitize_array( $val );
            } else {
                $sanitized[ $key ] = sanitize_text_field( $val );
            }
        }
        
        return $sanitized;
    }
}

/**
 * 清理HTML内容
 * 
 * 清理HTML内容，允许安全的HTML标签。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * 
 * @return string 清理后的HTML内容
 */
if ( ! function_exists( 'xun_sanitize_html' ) ) {
    function xun_sanitize_html( $value ) {
        
        // 允许的HTML标签
        $allowed_tags = array(
            'a'      => array(
                'href'   => array(),
                'title'  => array(),
                'target' => array(),
            ),
            'br'     => array(),
            'em'     => array(),
            'strong' => array(),
            'p'      => array(),
            'ul'     => array(),
            'ol'     => array(),
            'li'     => array(),
            'h1'     => array(),
            'h2'     => array(),
            'h3'     => array(),
            'h4'     => array(),
            'h5'     => array(),
            'h6'     => array(),
        );
        
        return wp_kses( $value, $allowed_tags );
    }
}

/**
 * 清理CSS代码
 * 
 * 清理CSS代码，移除危险的内容。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * 
 * @return string 清理后的CSS代码
 */
if ( ! function_exists( 'xun_sanitize_css' ) ) {
    function xun_sanitize_css( $value ) {
        
        // 移除危险的CSS内容
        $value = preg_replace( '/javascript:/i', '', $value );
        $value = preg_replace( '/expression\s*\(/i', '', $value );
        $value = preg_replace( '/@import/i', '', $value );
        
        return strip_tags( $value );
    }
}

/**
 * 清理文件名
 * 
 * 清理文件名，确保安全性。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * 
 * @return string 清理后的文件名
 */
if ( ! function_exists( 'xun_sanitize_filename' ) ) {
    function xun_sanitize_filename( $value ) {
        return sanitize_file_name( $value );
    }
}

/**
 * 清理键名
 * 
 * 清理数组键名或选项名称。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * 
 * @return string 清理后的键名
 */
if ( ! function_exists( 'xun_sanitize_key' ) ) {
    function xun_sanitize_key( $value ) {
        return sanitize_key( $value );
    }
}

/**
 * 清理用户名
 * 
 * 清理用户名输入。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * 
 * @return string 清理后的用户名
 */
if ( ! function_exists( 'xun_sanitize_user' ) ) {
    function xun_sanitize_user( $value ) {
        return sanitize_user( $value );
    }
}

/**
 * 清理标题
 * 
 * 清理标题文本。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * 
 * @return string 清理后的标题
 */
if ( ! function_exists( 'xun_sanitize_title' ) ) {
    function xun_sanitize_title( $value ) {
        return sanitize_title( $value );
    }
}

/**
 * 通用字段清理函数
 * 
 * 根据字段类型自动选择合适的清理函数。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要清理的值
 * @param array $field 字段配置
 * 
 * @return mixed 清理后的值
 */
if ( ! function_exists( 'xun_sanitize_field' ) ) {
    function xun_sanitize_field( $value, $field ) {
        
        $field_type = isset( $field['type'] ) ? $field['type'] : 'text';
        
        switch ( $field_type ) {
            case 'text':
                return xun_sanitize_text( $value );
                
            case 'textarea':
                return xun_sanitize_textarea( $value );
                
            case 'email':
                return xun_sanitize_email( $value );
                
            case 'url':
                return xun_sanitize_url( $value );
                
            case 'number':
                return xun_sanitize_number( $value );
                
            case 'color':
                return xun_sanitize_color( $value );
                
            case 'checkbox':
            case 'switcher':
                return xun_sanitize_boolean( $value );
                
            case 'select':
            case 'radio':
                // 验证值是否在允许的选项中
                if ( isset( $field['options'] ) && is_array( $field['options'] ) ) {
                    if ( array_key_exists( $value, $field['options'] ) ) {
                        return $value;
                    }
                }
                return '';

            case 'media':
                return xun_sanitize_media( $value, $field );

            default:
                // 应用自定义清理过滤器
                return apply_filters( "xun_sanitize_{$field_type}", $value, $field );
        }
    }
}

/**
 * 清理媒体字段值
 *
 * 验证和清理媒体选择器字段的数据，确保所有附件ID有效且用户有权限访问。
 *
 * @since 1.1.0
 *
 * @param mixed $value 要清理的值
 * @param array $field 字段配置
 *
 * @return array 清理后的媒体数据数组
 */
if ( ! function_exists( 'xun_sanitize_media' ) ) {
    function xun_sanitize_media( $value, $field = array() ) {

        // 如果值为空，返回空数组
        if ( empty( $value ) ) {
            return array();
        }

        // 如果是JSON字符串，解码
        if ( is_string( $value ) ) {
            $decoded = json_decode( $value, true );
            if ( json_last_error() === JSON_ERROR_NONE && is_array( $decoded ) ) {
                $value = $decoded;
            } else {
                // 尝试作为逗号分隔的ID处理
                $ids = array_filter( array_map( 'intval', explode( ',', $value ) ) );
                $value = array();
                foreach ( $ids as $id ) {
                    $attachment_data = xun_get_attachment_data( $id );
                    if ( $attachment_data ) {
                        $value[] = $attachment_data;
                    }
                }
            }
        }

        // 验证数组中的每个附件
        if ( is_array( $value ) ) {
            $validated_attachments = array();
            foreach ( $value as $attachment ) {
                if ( is_array( $attachment ) && ! empty( $attachment['id'] ) ) {
                    $attachment_id = intval( $attachment['id'] );

                    // 验证附件是否存在
                    if ( ! get_post( $attachment_id ) ) {
                        continue;
                    }

                    // 验证用户权限
                    if ( ! current_user_can( 'read_post', $attachment_id ) ) {
                        continue;
                    }

                    // 验证文件类型限制
                    if ( ! empty( $field['library'] ) && is_array( $field['library'] ) ) {
                        $attachment_type = get_post_mime_type( $attachment_id );
                        $type_allowed = false;

                        foreach ( $field['library'] as $allowed_type ) {
                            if ( strpos( $attachment_type, $allowed_type ) === 0 ) {
                                $type_allowed = true;
                                break;
                            }
                        }

                        if ( ! $type_allowed ) {
                            continue;
                        }
                    }

                    // 获取完整的附件数据
                    $attachment_data = xun_get_attachment_data( $attachment_id );
                    if ( $attachment_data ) {
                        $validated_attachments[] = $attachment_data;
                    }
                }
            }

            // 检查文件数量限制
            if ( ! empty( $field['max_files'] ) && is_numeric( $field['max_files'] ) ) {
                $max_files = intval( $field['max_files'] );
                if ( $max_files > 0 && count( $validated_attachments ) > $max_files ) {
                    $validated_attachments = array_slice( $validated_attachments, 0, $max_files );
                }
            }

            return $validated_attachments;
        }

        return array();
    }
}

/**
 * 获取附件数据
 *
 * 根据附件ID获取完整的附件信息，用于媒体字段。
 *
 * @since 1.1.0
 *
 * @param int $attachment_id 附件ID
 *
 * @return array|false 附件数据数组或false
 */
if ( ! function_exists( 'xun_get_attachment_data' ) ) {
    function xun_get_attachment_data( $attachment_id ) {

        $attachment_id = intval( $attachment_id );

        if ( ! $attachment_id || ! get_post( $attachment_id ) ) {
            return false;
        }

        // 获取附件基本信息
        $attachment = get_post( $attachment_id );
        $metadata = wp_get_attachment_metadata( $attachment_id );

        // 构建附件数据
        $data = array(
            'id'          => $attachment_id,
            'title'       => get_the_title( $attachment_id ),
            'filename'    => basename( get_attached_file( $attachment_id ) ),
            'url'         => wp_get_attachment_url( $attachment_id ),
            'type'        => get_post_mime_type( $attachment_id ),
            'subtype'     => '',
            'filesize'    => 0,
            'width'       => 0,
            'height'      => 0,
            'thumbnail'   => '',
            'alt'         => get_post_meta( $attachment_id, '_wp_attachment_image_alt', true ),
            'description' => $attachment->post_content,
            'caption'     => $attachment->post_excerpt,
        );

        // 解析MIME类型
        if ( $data['type'] ) {
            $type_parts = explode( '/', $data['type'] );
            $data['subtype'] = isset( $type_parts[1] ) ? $type_parts[1] : '';
        }

        // 获取文件大小
        $file_path = get_attached_file( $attachment_id );
        if ( $file_path && file_exists( $file_path ) ) {
            $data['filesize'] = filesize( $file_path );
        }

        // 获取图片尺寸和缩略图
        if ( wp_attachment_is_image( $attachment_id ) ) {
            if ( ! empty( $metadata['width'] ) && ! empty( $metadata['height'] ) ) {
                $data['width'] = $metadata['width'];
                $data['height'] = $metadata['height'];
            }

            // 获取缩略图URL
            $thumbnail_url = wp_get_attachment_image_src( $attachment_id, 'thumbnail' );
            if ( $thumbnail_url ) {
                $data['thumbnail'] = $thumbnail_url[0];
            }
        }

        return $data;
    }
}
