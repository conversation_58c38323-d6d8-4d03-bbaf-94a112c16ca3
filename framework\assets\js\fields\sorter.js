/**
 * Xun Framework Sorter 字段 JavaScript
 * 
 * 这个文件包含了排序器字段的所有交互逻辑，包括拖拽排序、
 * 启用/禁用切换、批量操作、实时预览等功能。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

(function($) {
    'use strict';

    /**
     * Sorter 字段类
     */
    class XunSorter {
        
        /**
         * 构造函数
         * 
         * @param {jQuery} $container 字段容器元素
         */
        constructor($container) {
            this.$container = $container;
            this.$enabled = $container.find('.xun-sorter-enabled');
            this.$disabled = $container.find('.xun-sorter-disabled');
            this.$enabledCount = $container.find('.xun-enabled-count');
            this.$disabledCount = $container.find('.xun-disabled-count');
            this.$preview = $container.find('.xun-sorter-preview');
            this.fieldId = $container.data('field-id');

            // 拖拽状态变量
            this.draggedElement = null;
            this.draggedData = null;
            this.placeholder = null;

            this.init();
        }
        
        /**
         * 初始化排序器
         */
        init() {
            this.initSortable();
            this.initBatchOperations();
            this.initPreview();
            this.updateCounts();
            this.updateEmptyStates();
        }
        
        /**
         * 初始化拖拽排序功能
         */
        initSortable() {
            // 使用原生拖拽API替代jQuery UI，性能更好
            this.initNativeDragDrop();
        }

        /**
         * 初始化原生拖拽功能
         */
        initNativeDragDrop() {
            const self = this;

            // 为所有可拖拽项目添加拖拽属性
            this.$container.find('.xun-sortable-item').each(function() {
                this.draggable = true;
            });

            // 拖拽开始
            this.$container.on('dragstart', '.xun-sortable-item', function(e) {
                self.draggedElement = this;
                self.draggedData = {
                    key: $(this).data('key'),
                    html: $(this)[0].outerHTML,
                    sourceContainer: $(this).parent()
                };

                // 设置拖拽数据
                e.originalEvent.dataTransfer.effectAllowed = 'move';
                e.originalEvent.dataTransfer.setData('text/html', self.draggedData.html);

                // 添加拖拽样式
                $(this).addClass('xun-dragging');

                // 创建占位符
                self.placeholder = $('<div class="xun-drag-placeholder h-12 border-2 border-dashed border-blue-300 rounded-lg bg-blue-50 opacity-50 mb-2"></div>');

                // 高亮目标区域
                self.$enabled.addClass('xun-drop-target');
                self.$disabled.addClass('xun-drop-target');

                // 延迟隐藏原始元素，避免闪烁
                setTimeout(() => {
                    $(self.draggedElement).css('opacity', '0.3');
                }, 0);
            });

            // 拖拽进入目标区域
            this.$container.on('dragenter', '.xun-sorter-enabled, .xun-sorter-disabled', function(e) {
                e.preventDefault();
                $(this).addClass('xun-drop-active');
            });

            // 拖拽在目标区域移动
            this.$container.on('dragover', '.xun-sorter-enabled, .xun-sorter-disabled', function(e) {
                e.preventDefault();
                e.originalEvent.dataTransfer.dropEffect = 'move';

                // 计算插入位置
                const $container = $(this);
                const $items = $container.find('.xun-sortable-item:not(.xun-dragging)');
                const mouseY = e.originalEvent.clientY;
                let insertAfter = null;

                $items.each(function() {
                    const rect = this.getBoundingClientRect();
                    const itemMiddle = rect.top + rect.height / 2;

                    if (mouseY > itemMiddle) {
                        insertAfter = this;
                    }
                });

                // 移除现有占位符
                $container.find('.xun-drag-placeholder').remove();

                // 插入占位符
                if (insertAfter) {
                    $(insertAfter).after(self.placeholder);
                } else {
                    $container.prepend(self.placeholder);
                }
            });

            // 拖拽离开目标区域
            this.$container.on('dragleave', '.xun-sorter-enabled, .xun-sorter-disabled', function(e) {
                // 检查是否真的离开了容器
                const rect = this.getBoundingClientRect();
                const x = e.originalEvent.clientX;
                const y = e.originalEvent.clientY;

                if (x < rect.left || x > rect.right || y < rect.top || y > rect.bottom) {
                    $(this).removeClass('xun-drop-active');
                }
            });

            // 放置
            this.$container.on('drop', '.xun-sorter-enabled, .xun-sorter-disabled', function(e) {
                e.preventDefault();

                if (!self.draggedElement) return;

                const $dropContainer = $(this);
                const $placeholder = $dropContainer.find('.xun-drag-placeholder');

                // 移动元素到新位置
                if ($placeholder.length > 0) {
                    $placeholder.replaceWith(self.draggedElement);
                } else {
                    $dropContainer.append(self.draggedElement);
                }

                // 处理元素移动
                self.handleItemMove($(self.draggedElement));

                // 清理
                self.cleanupDrag();
            });

            // 拖拽结束
            this.$container.on('dragend', '.xun-sortable-item', function() {
                self.cleanupDrag();
            });

            // 添加CSS样式
            this.addDynamicStyles();
        }

        /**
         * 清理拖拽状态
         */
        cleanupDrag() {
            // 移除所有拖拽相关的样式和元素
            this.$container.find('.xun-dragging').removeClass('xun-dragging').css('opacity', '');
            this.$container.find('.xun-drop-active').removeClass('xun-drop-active');
            this.$container.find('.xun-drop-target').removeClass('xun-drop-target');
            this.$container.find('.xun-drag-placeholder').remove();

            // 重置变量
            this.draggedElement = null;
            this.draggedData = null;
            this.placeholder = null;
        }
        
        /**
         * 处理项目移动
         * 
         * @param {jQuery} $item 移动的项目元素
         */
        handleItemMove($item) {
            const $parent = $item.parent();
            const isEnabled = $parent.hasClass('xun-sorter-enabled');
            const key = $item.data('key');
            
            // 更新隐藏输入字段的name属性
            const $input = $item.find('input[type="hidden"]');
            const currentName = $input.attr('name');
            const newType = isEnabled ? 'enabled' : 'disabled';
            const newName = currentName.replace(/\[(enabled|disabled)\]/, `[${newType}]`);
            
            $input.attr('name', newName);
            
            // 更新状态指示器
            this.updateItemStatus($item, isEnabled);
            
            // 更新计数和空状态
            this.updateCounts();
            this.updateEmptyStates();
            
            // 更新预览
            this.updatePreview();
            
            // 触发变更事件
            this.$container.trigger('xun:sorter:change', {
                item: key,
                type: newType,
                order: this.getOrder()
            });
        }
        
        /**
         * 更新项目状态指示器
         * 
         * @param {jQuery} $item 项目元素
         * @param {boolean} isEnabled 是否启用
         */
        updateItemStatus($item, isEnabled) {
            const $status = $item.find('.inline-flex.items-center.rounded-full');
            
            if (isEnabled) {
                $status.removeClass('bg-gray-100 text-gray-800')
                       .addClass('bg-green-100 text-green-800')
                       .html('<svg class="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd" /></svg>已启用');
            } else {
                $status.removeClass('bg-green-100 text-green-800')
                       .addClass('bg-gray-100 text-gray-800')
                       .html('<svg class="mr-1 h-3 w-3" fill="currentColor" viewBox="0 0 20 20"><path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>已禁用');
            }
        }
        
        /**
         * 初始化批量操作
         */
        initBatchOperations() {
            const self = this;
            
            // 全部启用
            this.$container.on('click', '.xun-sorter-enable-all', function(e) {
                e.preventDefault();
                self.enableAll();
            });
            
            // 全部禁用
            this.$container.on('click', '.xun-sorter-disable-all', function(e) {
                e.preventDefault();
                self.disableAll();
            });
        }
        
        /**
         * 启用所有项目
         */
        enableAll() {
            const $items = this.$disabled.find('.xun-sortable-item');
            const self = this;

            $items.each(function() {
                const $item = $(this);
                self.$enabled.append($item);
                self.handleItemMove($item);
            });
        }
        
        /**
         * 禁用所有项目
         */
        disableAll() {
            const $items = this.$enabled.find('.xun-sortable-item');
            const self = this;

            $items.each(function() {
                const $item = $(this);
                self.$disabled.append($item);
                self.handleItemMove($item);
            });
        }
        
        /**
         * 更新计数显示
         */
        updateCounts() {
            const enabledCount = this.$enabled.find('.xun-sortable-item').length;
            const disabledCount = this.$disabled.find('.xun-sortable-item').length;
            
            this.$enabledCount.text(enabledCount);
            this.$disabledCount.text(disabledCount);
        }
        
        /**
         * 更新空状态显示
         */
        updateEmptyStates() {
            // 更新启用区域空状态
            const $enabledItems = this.$enabled.find('.xun-sortable-item');
            const $enabledPlaceholder = this.$enabled.find('.xun-empty-placeholder');
            
            if ($enabledItems.length === 0) {
                if ($enabledPlaceholder.length === 0) {
                    this.$enabled.append(this.createEmptyPlaceholder('enabled'));
                } else {
                    $enabledPlaceholder.show();
                }
            } else {
                $enabledPlaceholder.hide();
            }
            
            // 更新禁用区域空状态
            const $disabledItems = this.$disabled.find('.xun-sortable-item');
            const $disabledPlaceholder = this.$disabled.find('.xun-empty-placeholder');
            
            if ($disabledItems.length === 0) {
                if ($disabledPlaceholder.length === 0) {
                    this.$disabled.append(this.createEmptyPlaceholder('disabled'));
                } else {
                    $disabledPlaceholder.show();
                }
            } else {
                $disabledPlaceholder.hide();
            }
        }
        
        /**
         * 创建空状态占位符
         * 
         * @param {string} type 类型（enabled/disabled）
         * @returns {string} HTML字符串
         */
        createEmptyPlaceholder(type) {
            const isEnabled = type === 'enabled';
            const icon = isEnabled 
                ? '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4" />'
                : '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 12H4" />';
            const text = isEnabled ? '拖拽项目到此处启用' : '拖拽项目到此处禁用';
            
            return `<div class="xun-empty-placeholder text-center py-8 text-gray-500">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    ${icon}
                </svg>
                <p class="mt-2 text-sm">${text}</p>
            </div>`;
        }
        
        /**
         * 初始化实时预览
         */
        initPreview() {
            if (this.$preview.length > 0) {
                this.updatePreview();
            }
        }
        
        /**
         * 更新实时预览
         */
        updatePreview() {
            if (this.$preview.length === 0) return;
            
            const order = this.getOrder();
            const previewHtml = this.generatePreviewHtml(order);
            this.$preview.html(previewHtml);
        }
        
        /**
         * 获取当前排序
         * 
         * @returns {Object} 排序数据
         */
        getOrder() {
            const enabled = [];
            const disabled = [];
            
            this.$enabled.find('.xun-sortable-item').each(function() {
                const $item = $(this);
                enabled.push({
                    key: $item.data('key'),
                    label: $item.find('span').text().trim()
                });
            });
            
            this.$disabled.find('.xun-sortable-item').each(function() {
                const $item = $(this);
                disabled.push({
                    key: $item.data('key'),
                    label: $item.find('span').text().trim()
                });
            });
            
            return { enabled, disabled };
        }
        
        /**
         * 生成预览HTML
         * 
         * @param {Object} order 排序数据
         * @returns {string} HTML字符串
         */
        generatePreviewHtml(order) {
            let html = '<div class="space-y-2">';
            
            if (order.enabled.length > 0) {
                html += '<div><strong>已启用项目：</strong></div>';
                html += '<ol class="list-decimal list-inside space-y-1 ml-4">';
                order.enabled.forEach(item => {
                    html += `<li class="text-green-600">${item.label}</li>`;
                });
                html += '</ol>';
            }
            
            if (order.disabled.length > 0) {
                html += '<div class="mt-3"><strong>已禁用项目：</strong></div>';
                html += '<ul class="list-disc list-inside space-y-1 ml-4">';
                order.disabled.forEach(item => {
                    html += `<li class="text-gray-500">${item.label}</li>`;
                });
                html += '</ul>';
            }
            
            if (order.enabled.length === 0 && order.disabled.length === 0) {
                html += '<p class="text-gray-500 italic">暂无项目</p>';
            }
            
            html += '</div>';
            return html;
        }
        

        
        /**
         * 添加动态样式
         */
        addDynamicStyles() {
            if ($('#xun-sorter-styles').length > 0) return;
            
            const styles = `
                <style id="xun-sorter-styles">
                    .xun-sortable-item.xun-dragging {
                        opacity: 0.8;
                        cursor: grabbing !important;
                        z-index: 9999 !important;
                        /* 移除所有可能影响跟手性的变换 */
                    }

                    .xun-placeholder-active {
                        opacity: 0.6;
                        transition: opacity 0.2s ease;
                    }
                    
                    .xun-sorter-enabled.xun-drop-target,
                    .xun-sorter-disabled.xun-drop-target {
                        border-color: #3b82f6;
                        background-color: rgba(59, 130, 246, 0.05);
                    }
                    
                    .xun-sorter-enabled.xun-drop-active {
                        border-color: #10b981;
                        background-color: rgba(16, 185, 129, 0.1);
                    }
                    
                    .xun-sorter-disabled.xun-drop-active {
                        border-color: #6b7280;
                        background-color: rgba(107, 114, 128, 0.1);
                    }
                    
                    .xun-sortable-placeholder {
                        margin-bottom: 0.5rem;
                        border-radius: 0.5rem;
                    }
                    
                    .xun-drag-handle {
                        cursor: grab;
                    }
                    
                    .xun-drag-handle:active {
                        cursor: grabbing;
                    }
                </style>
            `;
            
            $('head').append(styles);
        }
    }

    /**
     * 初始化所有排序器字段
     */
    function initSorterFields() {
        $('.xun-sorter-field').each(function() {
            const $container = $(this);
            if (!$container.data('xun-sorter-initialized')) {
                new XunSorter($container);
                $container.data('xun-sorter-initialized', true);
            }
        });
    }

    // 文档就绪时初始化
    $(document).ready(function() {
        initSorterFields();
    });

    // 支持动态加载的字段
    $(document).on('xun:field:loaded', function() {
        initSorterFields();
    });

})(jQuery);
