{"name": "xun-framework-docs", "version": "1.0.0", "description": "Xun Framework 文档系统", "private": true, "scripts": {"dev": "tailwindcss --input=assets/css/input.css --output=assets/css/docs.css --watch", "build": "tailwindcss --input=assets/css/input.css --output=assets/css/docs.css --minify", "build:watch": "npm run build -- --watch"}, "devDependencies": {"tailwindcss": "^4.1.10", "@tailwindcss/cli": "^4.1.10"}, "engines": {"node": ">=18.0.0"}, "keywords": ["wordpress", "framework", "documentation", "tailwindcss", "php"], "author": "June", "license": "MIT"}