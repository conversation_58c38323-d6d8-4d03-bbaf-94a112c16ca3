/**
 * XUN Button Field JavaScript
 * 
 * 现代化的按钮选择字段交互逻辑
 * 支持单选/多选、搜索、动画效果和无障碍访问
 * 
 * @since 1.0.0
 * @version 1.0.0
 */

(function($) {
    'use strict';

    /**
     * Button Field 类
     */
    var XunButtonField = {
        
        /**
         * 初始化
         */
        init: function() {
            this.bindEvents();
            this.initializeFields();
        },

        /**
         * 绑定事件
         */
        bindEvents: function() {
            var self = this;

            // 按钮点击事件
            $(document).on('change', '.xun-button-field .xun-button-input', function() {
                self.handleButtonChange($(this));
            });

            // 搜索功能
            $(document).on('input', '.xun-button-search', function() {
                self.handleSearch($(this));
            });

            // 键盘导航
            $(document).on('keydown', '.xun-button-option', function(e) {
                self.handleKeyboardNavigation(e, $(this));
            });

            // 按钮点击事件 - 直接处理
            $(document).on('click', '.xun-button-option button', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var $button = $(this);
                var $option = $button.closest('.xun-button-option');
                var $input = $option.find('.xun-button-input');
                var $field = $option.closest('.xun-button-field');
                var $container = $option.closest('[data-multiple]');
                var isMultiple = $container.data('multiple') === true || $container.data('multiple') === 'true';

                // 直接处理按钮状态切换
                if (!isMultiple) {
                    // 单选模式：清除其他选中状态
                    $container.find('.xun-button-option').removeClass('selected');
                    $container.find('.xun-button-input').prop('checked', false);

                    // 设置当前选中
                    $input.prop('checked', true);
                    $option.addClass('selected');
                } else {
                    // 多选模式：切换当前状态
                    var isCurrentlyChecked = $input.is(':checked');
                    $input.prop('checked', !isCurrentlyChecked);

                    if (!isCurrentlyChecked) {
                        $option.addClass('selected');
                    } else {
                        $option.removeClass('selected');
                    }
                }

                // 更新字段状态
                XunButtonField.updateFieldState($field);
                XunButtonField.triggerChangeEvent($field);
            });

            // 键盘快捷键支持
            $(document).on('keydown', function(e) {
                // Cmd/Ctrl + K 聚焦搜索框
                if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
                    e.preventDefault();
                    $('.xun-button-search:visible').first().focus();
                }
            });

            // 搜索框增强
            $(document).on('focus', '.xun-button-search', function() {
                $(this).addClass('ring-2 ring-blue-500 border-blue-500');
            });

            $(document).on('blur', '.xun-button-search', function() {
                $(this).removeClass('ring-2 ring-blue-500 border-blue-500');
            });
        },

        /**
         * 初始化所有字段
         */
        initializeFields: function() {
            $('.xun-button-field').each(function() {
                var $field = $(this);
                XunButtonField.updateFieldState($field);
            });
        },

        /**
         * 处理按钮状态变化
         */
        handleButtonChange: function($input) {
            var $field = $input.closest('.xun-button-field');
            var $group = $input.closest('.xun-button-group');
            var isMultiple = $group.data('multiple') === true || $group.data('multiple') === 'true';
            var $option = $input.closest('.xun-button-option');

            if (!isMultiple) {
                // 单选模式：清除其他选中状态
                $group.find('.xun-button-option').removeClass('selected');
                $group.find('.xun-button-input').prop('checked', false);
                
                // 设置当前选中
                $input.prop('checked', true);
                $option.addClass('selected');
            } else {
                // 多选模式：切换当前状态
                if ($input.is(':checked')) {
                    $option.addClass('selected');
                } else {
                    $option.removeClass('selected');
                }
            }

            // 更新字段状态
            this.updateFieldState($field);
            this.triggerChangeEvent($field);
        },

        /**
         * 处理搜索
         */
        handleSearch: function($searchInput) {
            var searchTerm = $searchInput.val().toLowerCase();
            var $field = $searchInput.closest('.xun-button-field');
            var $options = $field.find('.xun-button-option');

            $options.each(function() {
                var $option = $(this);
                var text = $option.find('div').first().text().toLowerCase();
                var description = $option.find('.text-xs').text().toLowerCase();
                
                if (text.includes(searchTerm) || description.includes(searchTerm)) {
                    $option.removeClass('hidden').addClass('animate-fadeIn');
                } else {
                    $option.addClass('hidden').removeClass('animate-fadeIn');
                }
            });

            // 显示搜索结果统计
            this.updateSearchResults($field, searchTerm);
        },

        /**
         * 键盘导航
         */
        handleKeyboardNavigation: function(e, $option) {
            var $field = $option.closest('.xun-button-field');
            var $visibleOptions = $field.find('.xun-button-option:not(.hidden)');
            var currentIndex = $visibleOptions.index($option);
            var $target = null;

            switch (e.which) {
                case 37: // 左箭头
                case 38: // 上箭头
                    e.preventDefault();
                    $target = currentIndex > 0 ? $visibleOptions.eq(currentIndex - 1) : $visibleOptions.last();
                    break;
                case 39: // 右箭头
                case 40: // 下箭头
                    e.preventDefault();
                    $target = currentIndex < $visibleOptions.length - 1 ? $visibleOptions.eq(currentIndex + 1) : $visibleOptions.first();
                    break;
                case 32: // 空格
                case 13: // 回车
                    e.preventDefault();
                    $option.find('.xun-button-input').trigger('click');
                    break;
            }

            if ($target) {
                $target.find('.xun-button-input').focus();
            }
        },



        /**
         * 更新字段状态
         */
        updateFieldState: function($field) {
            $field.find('.xun-button-option').each(function() {
                var $option = $(this);
                var $input = $option.find('.xun-button-input');
                var $button = $option.find('button');

                if ($input.is(':checked')) {
                    $option.addClass('selected');
                    // 更新按钮的视觉状态为选中
                    XunButtonField.updateButtonVisualState($button, true);
                } else {
                    $option.removeClass('selected');
                    // 更新按钮的视觉状态为未选中
                    XunButtonField.updateButtonVisualState($button, false);
                }
            });
        },

        /**
         * 更新按钮的视觉状态
         */
        updateButtonVisualState: function($button, isChecked) {
            // 移除所有可能的状态类 - 包括bg-transparent
            var allStateClasses = [
                // 背景色类 - 包括透明背景
                'bg-transparent', 'bg-blue-600', 'bg-green-600', 'bg-purple-600', 'bg-red-600', 'bg-gray-600',
                'bg-white', 'bg-gray-50', 'bg-gray-100', 'bg-gray-200', 'bg-gray-300',
                'bg-blue-50', 'bg-green-50', 'bg-purple-50', 'bg-red-50',
                'bg-blue-100', 'bg-green-100', 'bg-purple-100', 'bg-red-100',
                'bg-blue-200', 'bg-green-200', 'bg-purple-200', 'bg-red-200',
                // 文字颜色类
                'text-white', 'text-gray-700', 'text-gray-600', 'text-gray-500', 'text-gray-900',
                'text-blue-600', 'text-green-600', 'text-purple-600', 'text-red-600',
                'text-blue-500', 'text-green-500', 'text-purple-500', 'text-red-500',
                'text-blue-700', 'text-green-700', 'text-purple-700', 'text-red-700',
                'text-blue-900', 'text-green-900', 'text-purple-900', 'text-red-900',
                // 边框颜色类
                'border-blue-600', 'border-green-600', 'border-purple-600',
                'border-red-600', 'border-gray-600', 'border-gray-300', 'border-gray-200',
                // 阴影和其他效果
                'shadow-md', 'shadow-sm', 'shadow-lg', 'shadow-xl', 'shadow-none', 'shadow-inner',
                // hover状态
                'hover:bg-gray-50', 'hover:bg-gray-100', 'hover:bg-blue-50', 'hover:bg-blue-100',
                'hover:bg-green-50', 'hover:bg-green-100', 'hover:bg-purple-50', 'hover:bg-purple-100',
                'hover:bg-red-50', 'hover:bg-red-100', 'hover:text-blue-700', 'hover:text-green-700',
                'hover:text-purple-700', 'hover:text-red-700'
            ];

            // 移除所有状态类
            $button.removeClass(allStateClasses.join(' '));

            // 获取颜色主题和样式
            var $container = $button.closest('.xun-button-group');
            var color = $container.data('color') || 'blue';
            var style = $container.data('style') || 'default';

            if (isChecked) {
                // 应用选中状态的样式 - 使用!important确保优先级
                $button.attr('style', '');  // 清除内联样式

                switch(color) {
                    case 'green':
                        $button.addClass('bg-green-600 text-white border-green-600 shadow-md');
                        $button.css({
                            'background-color': '#059669 !important',
                            'color': '#ffffff !important',
                            'border-color': '#059669 !important'
                        });
                        break;
                    case 'purple':
                        $button.addClass('bg-purple-600 text-white border-purple-600 shadow-md');
                        $button.css({
                            'background-color': '#9333ea !important',
                            'color': '#ffffff !important',
                            'border-color': '#9333ea !important'
                        });
                        break;
                    case 'red':
                        $button.addClass('bg-red-600 text-white border-red-600 shadow-md');
                        $button.css({
                            'background-color': '#dc2626 !important',
                            'color': '#ffffff !important',
                            'border-color': '#dc2626 !important'
                        });
                        break;
                    case 'gray':
                        $button.addClass('bg-gray-600 text-white border-gray-600 shadow-md');
                        $button.css({
                            'background-color': '#4b5563 !important',
                            'color': '#ffffff !important',
                            'border-color': '#4b5563 !important'
                        });
                        break;
                    default: // blue
                        $button.addClass('bg-blue-600 text-white border-blue-600 shadow-md');
                        $button.css({
                            'background-color': '#2563eb !important',
                            'color': '#ffffff !important',
                            'border-color': '#2563eb !important'
                        });
                        break;
                }
            } else {
                // 应用未选中状态的样式
                $button.addClass('bg-white text-gray-700 border-gray-300 hover:bg-gray-50');
                $button.css({
                    'background-color': '#ffffff !important',
                    'color': '#374151 !important',
                    'border-color': '#d1d5db !important'
                });
            }
        },



        /**
         * 更新搜索结果
         */
        updateSearchResults: function($field, searchTerm) {
            var $visibleOptions = $field.find('.xun-button-option:not(.hidden)');

            if (searchTerm && $visibleOptions.length === 0) {
                // 显示无结果提示
                if (!$field.find('.no-results').length) {
                    var noResultsHtml = '<div class="no-results flex flex-col items-center justify-center py-12 text-gray-500">' +
                        '<div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">' +
                        '<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                        '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>' +
                        '</svg>' +
                        '</div>' +
                        '<p class="text-sm font-medium text-gray-600 mb-1">未找到匹配的选项</p>' +
                        '<p class="text-xs text-gray-400">尝试使用其他关键词搜索</p>' +
                        '</div>';
                    $field.find('.xun-button-group').after(noResultsHtml);
                }
            } else {
                $field.find('.no-results').remove();
            }

            // 更新搜索结果统计
            if (searchTerm) {
                var totalCount = $field.find('.xun-button-option').length;
                var visibleCount = $visibleOptions.length;

                if (!$field.find('.search-stats').length && visibleCount > 0) {
                    var statsHtml = '<div class="search-stats text-center mb-4">' +
                        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">' +
                        '显示 ' + visibleCount + ' / ' + totalCount + ' 项' +
                        '</span>' +
                        '</div>';
                    $field.find('.xun-button-group').before(statsHtml);
                }
            } else {
                $field.find('.search-stats').remove();
            }
        },



        /**
         * 触发变化事件
         */
        triggerChangeEvent: function($field) {
            var fieldId = $field.data('field-id');
            var selectedValues = [];
            
            $field.find('.xun-button-input:checked').each(function() {
                selectedValues.push($(this).val());
            });

            // 触发自定义事件
            $field.trigger('xun:button:change', {
                fieldId: fieldId,
                values: selectedValues,
                count: selectedValues.length
            });
        },

        /**
         * 重新初始化字段（用于动态添加的字段）
         */
        reinit: function() {
            this.initializeFields();
        },

        /**
         * 获取字段值
         */
        getValue: function(fieldId) {
            var $field = $('.xun-button-field[data-field-id="' + fieldId + '"]');
            var values = [];
            
            $field.find('.xun-button-input:checked').each(function() {
                values.push($(this).val());
            });
            
            return values;
        },

        /**
         * 设置字段值
         */
        setValue: function(fieldId, values) {
            var $field = $('.xun-button-field[data-field-id="' + fieldId + '"]');
            values = Array.isArray(values) ? values : [values];
            
            // 清除所有选中状态
            $field.find('.xun-button-input').prop('checked', false);
            
            // 设置新的选中状态
            values.forEach(function(value) {
                $field.find('.xun-button-input[value="' + value + '"]').prop('checked', true);
            });
            
            // 更新显示状态
            this.updateFieldState($field);
        },

        /**
         * 禁用/启用选项
         */
        toggleOption: function(fieldId, optionValue, disabled) {
            var $field = $('.xun-button-field[data-field-id="' + fieldId + '"]');
            var $option = $field.find('.xun-button-option[data-value="' + optionValue + '"]');
            var $input = $option.find('.xun-button-input');
            
            if (disabled) {
                $input.prop('disabled', true);
                $option.addClass('disabled opacity-50 cursor-not-allowed');
            } else {
                $input.prop('disabled', false);
                $option.removeClass('disabled opacity-50 cursor-not-allowed');
            }
        }
    };

    // 文档就绪时初始化
    $(document).ready(function() {
        XunButtonField.init();
    });

    // 暴露到全局
    window.XunButtonField = XunButtonField;

})(jQuery);
