/**
 * Textarea Field JavaScript - 基础字段增强
 * 
 * 提供textarea字段的增强功能：自动调整高度、字符计数、键盘快捷键等
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.1.0
 */

(function($) {
    'use strict';

    /**
     * Textarea Field 类
     */
    var XunTextareaField = {

        /**
         * 初始化
         */
        init: function() {
            this.bindEvents();
            this.initAutoResize();
            this.initCharacterCount();
        },

        /**
         * 绑定事件
         */
        bindEvents: function() {
            var self = this;

            // 输入事件
            $(document).on('input', '.xun-textarea-input', function() {
                var $textarea = $(this);
                var $container = $textarea.closest('.xun-textarea-field');

                // 自动调整高度
                if ($textarea.data('auto-resize')) {
                    self.autoResize($textarea);
                }

                // 更新字符计数
                if ($textarea.data('show-count')) {
                    self.updateCharacterCount($textarea, $container);
                }

                // 验证长度
                self.validateLength($textarea);
            });

            // 键盘快捷键
            $(document).on('keydown', '.xun-textarea-input', function(e) {
                self.handleKeyboardShortcuts(e, $(this));
            });

            // 粘贴事件
            $(document).on('paste', '.xun-textarea-input', function(e) {
                var $textarea = $(this);
                setTimeout(function() {
                    if ($textarea.data('auto-resize')) {
                        self.autoResize($textarea);
                    }
                    if ($textarea.data('show-count')) {
                        self.updateCharacterCount($textarea, $textarea.closest('.xun-textarea-field'));
                    }
                    self.validateLength($textarea);
                }, 10);
            });

            // 拖拽调整大小事件（使用ResizeObserver或mouseup事件）
            if (window.ResizeObserver) {
                // 使用现代的ResizeObserver API
                var resizeObserver = new ResizeObserver(function(entries) {
                    entries.forEach(function(entry) {
                        var $textarea = $(entry.target);
                        if ($textarea.hasClass('xun-textarea-input')) {
                            // 拖拽调整大小后更新相关功能
                            if ($textarea.data('show-count')) {
                                self.updateCharacterCount($textarea, $textarea.closest('.xun-textarea-field'));
                            }
                            self.validateLength($textarea);
                        }
                    });
                });

                // 使用MutationObserver监听动态添加的textarea
                var mutationObserver = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList') {
                            mutation.addedNodes.forEach(function(node) {
                                if (node.nodeType === 1) { // Element node
                                    var $node = $(node);
                                    // 检查新添加的节点是否包含textarea
                                    $node.find('.xun-textarea-input').each(function() {
                                        resizeObserver.observe(this);
                                    });
                                    // 检查节点本身是否是textarea
                                    if ($node.hasClass('xun-textarea-input')) {
                                        resizeObserver.observe(node);
                                    }
                                }
                            });
                        }
                    });
                });

                // 开始观察DOM变化
                mutationObserver.observe(document.body, {
                    childList: true,
                    subtree: true
                });

                // 初始化已存在的textarea
                $('.xun-textarea-input').each(function() {
                    resizeObserver.observe(this);
                });
            } else {
                // 降级方案：使用mouseup事件
                $(document).on('mouseup', '.xun-textarea-input', function() {
                    var $textarea = $(this);
                    setTimeout(function() {
                        if ($textarea.data('show-count')) {
                            self.updateCharacterCount($textarea, $textarea.closest('.xun-textarea-field'));
                        }
                        self.validateLength($textarea);
                    }, 100);
                });
            }
        },

        /**
         * 初始化自动调整高度
         */
        initAutoResize: function() {
            var self = this;
            $('.xun-textarea-input[data-auto-resize="true"]').each(function() {
                self.autoResize($(this));
            });
        },

        /**
         * 自动调整高度
         */
        autoResize: function($textarea) {
            // 重置高度以获取正确的scrollHeight
            $textarea.css('height', 'auto');
            
            // 计算新高度
            var scrollHeight = $textarea[0].scrollHeight;
            var minHeight = parseInt($textarea.css('min-height')) || 80;
            var maxHeight = parseInt($textarea.css('max-height')) || 400;
            
            var newHeight = Math.max(minHeight, Math.min(scrollHeight, maxHeight));
            
            // 设置新高度
            $textarea.css('height', newHeight + 'px');
        },

        /**
         * 初始化字符计数
         */
        initCharacterCount: function() {
            var self = this;
            $('.xun-textarea-input[data-show-count="true"]').each(function() {
                var $textarea = $(this);
                var $container = $textarea.closest('.xun-textarea-field');
                self.updateCharacterCount($textarea, $container);
            });
        },

        /**
         * 更新字符计数
         */
        updateCharacterCount: function($textarea, $container) {
            var currentLength = $textarea.val().length;
            var maxLength = parseInt($textarea.attr('maxlength')) || 0;

            var $counter = $container.find('.xun-textarea-counter');
            var $currentCount = $counter.find('.current-count');
            var $maxCount = $counter.find('.max-count');

            // 更新当前字符数
            $currentCount.text(currentLength);

            // 更新样式和背景色
            if (maxLength > 0) {
                var percentage = (currentLength / maxLength) * 100;

                // 移除所有状态相关的类
                $counter.removeClass('text-gray-500 text-yellow-600 text-red-600 bg-white bg-yellow-50 bg-red-50 border-gray-200 border-yellow-300 border-red-300');

                if (percentage >= 100) {
                    // 超出限制：红色警告
                    $counter.addClass('text-red-600 bg-red-50 border-red-300');
                } else if (percentage >= 80) {
                    // 接近限制：黄色警告
                    $counter.addClass('text-yellow-600 bg-yellow-50 border-yellow-300');
                } else {
                    // 正常状态：灰色
                    $counter.addClass('text-gray-500 bg-white border-gray-200');
                }
            } else {
                // 没有限制时保持默认样式
                $counter.removeClass('text-yellow-600 text-red-600 bg-yellow-50 bg-red-50 border-yellow-300 border-red-300');
                $counter.addClass('text-gray-500 bg-white border-gray-200');
            }
        },

        /**
         * 验证长度
         */
        validateLength: function($textarea) {
            var value = $textarea.val();
            var currentLength = value.length;
            var maxLength = parseInt($textarea.attr('maxlength')) || 0;
            var minLength = parseInt($textarea.attr('minlength')) || 0;
            
            var isValid = true;
            var errorMessage = '';
            
            // 检查最大长度
            if (maxLength > 0 && currentLength > maxLength) {
                isValid = false;
                errorMessage = xunTextareaL10n.maxLengthExceeded;
            }
            
            // 检查最小长度
            if (minLength > 0 && currentLength < minLength) {
                isValid = false;
                errorMessage = xunTextareaL10n.minLengthRequired;
            }
            
            // 更新样式
            if (isValid) {
                $textarea.removeClass('outline-red-300 focus:outline-red-600')
                        .addClass('outline-gray-300 focus:outline-indigo-600');
            } else {
                $textarea.removeClass('outline-gray-300 focus:outline-indigo-600')
                        .addClass('outline-red-300 focus:outline-red-600');
            }
            
            return isValid;
        },

        /**
         * 处理键盘快捷键
         */
        handleKeyboardShortcuts: function(e, $textarea) {
            // Ctrl/Cmd + Enter: 提交表单
            if ((e.ctrlKey || e.metaKey) && e.keyCode === 13) {
                var $form = $textarea.closest('form');
                if ($form.length) {
                    e.preventDefault();
                    $form.submit();
                }
            }
            
            // Tab键处理（在textarea中插入制表符）
            if (e.keyCode === 9 && !e.shiftKey) {
                e.preventDefault();
                var start = $textarea[0].selectionStart;
                var end = $textarea[0].selectionEnd;
                var value = $textarea.val();
                
                $textarea.val(value.substring(0, start) + '\t' + value.substring(end));
                $textarea[0].selectionStart = $textarea[0].selectionEnd = start + 1;
                
                // 触发input事件以更新计数等
                $textarea.trigger('input');
            }
        }
    };

    // 文档就绪时初始化
    $(document).ready(function() {
        XunTextareaField.init();
    });

    // 导出到全局
    window.XunTextareaField = XunTextareaField;

})(jQuery);
