<?php
/**
 * Xun Framework 文档系统 - API参考页面
 *
 * 提供完整的API文档，包括核心类、方法、钩子和过滤器的详细说明。
 * 采用侧边栏导航和搜索功能，方便开发者快速查找API信息。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

// 页面配置
$header_config = [
    'site_title' => 'Xun Framework',
    'site_description' => '现代化的WordPress选项框架',
    'page_title' => 'API参考',
    'page_description' => '完整的API文档，包括核心类、方法、钩子和过滤器的详细说明和使用示例',
    'keywords' => 'WordPress, Framework, API, 文档, 方法, 类, 钩子, 过滤器',
    'current_page' => 'api-reference'
];

$footer_config = [
    'site_title' => 'Xun Framework',
    'site_description' => '现代化的WordPress选项框架',
    'version' => '1.0.0',
    'total_fields' => 23,
    'show_stats' => true
];

// API分类配置
$api_categories = [
    'core' => [
        'title' => '核心类',
        'description' => '框架的核心类和主要接口',
        'icon' => 'cube',
        'color' => 'blue',
        'items' => [
            'XUN' => [
                'title' => 'XUN 主类',
                'description' => '框架的主要API接口，提供创建选项页面的静态方法',
                'methods' => [
                    'createOptions' => [
                        'title' => 'createOptions()',
                        'description' => '创建选项页面',
                        'syntax' => 'XUN::createOptions( $id, $args )',
                        'parameters' => [
                            '$id' => ['type' => 'string', 'required' => true, 'description' => '唯一标识符'],
                            '$args' => ['type' => 'array', 'required' => false, 'description' => '选项页面参数']
                        ],
                        'return' => 'XUN_Options',
                        'example' => "XUN::createOptions( 'my_options', array(\n    'menu_title' => '主题设置',\n    'menu_slug' => 'my-theme-options',\n    'framework_title' => '我的主题设置'\n) );"
                    ],
                    'createSection' => [
                        'title' => 'createSection()',
                        'description' => '为选项页面创建区块',
                        'syntax' => 'XUN::createSection( $id, $section )',
                        'parameters' => [
                            '$id' => ['type' => 'string', 'required' => true, 'description' => '选项页面ID'],
                            '$section' => ['type' => 'array', 'required' => true, 'description' => '区块配置']
                        ],
                        'return' => 'void',
                        'example' => "XUN::createSection( 'my_options', array(\n    'title' => '基本设置',\n    'fields' => array(\n        array(\n            'id' => 'site_logo',\n            'type' => 'media',\n            'title' => '网站Logo'\n        )\n    )\n) );"
                    ],
                    'get_option' => [
                        'title' => 'get_option()',
                        'description' => '获取选项值',
                        'syntax' => 'XUN::get_option( $option_name, $field_id, $default )',
                        'parameters' => [
                            '$option_name' => ['type' => 'string', 'required' => true, 'description' => '选项组名称'],
                            '$field_id' => ['type' => 'string', 'required' => false, 'description' => '字段ID'],
                            '$default' => ['type' => 'mixed', 'required' => false, 'description' => '默认值']
                        ],
                        'return' => 'mixed',
                        'example' => "\$logo = XUN::get_option( 'my_options', 'site_logo' );\n\$color = XUN::get_option( 'my_options', 'theme_color', '#2563eb' );"
                    ],
                    'set_option' => [
                        'title' => 'set_option()',
                        'description' => '设置选项值',
                        'syntax' => 'XUN::set_option( $option_name, $field_id, $value )',
                        'parameters' => [
                            '$option_name' => ['type' => 'string', 'required' => true, 'description' => '选项组名称'],
                            '$field_id' => ['type' => 'string', 'required' => true, 'description' => '字段ID'],
                            '$value' => ['type' => 'mixed', 'required' => true, 'description' => '要设置的值']
                        ],
                        'return' => 'bool',
                        'example' => "XUN::set_option( 'my_options', 'theme_color', '#ff0000' );"
                    ]
                ]
            ],
            'XUN_Setup' => [
                'title' => 'XUN_Setup 设置类',
                'description' => '框架初始化和设置管理类',
                'methods' => [
                    'init' => [
                        'title' => 'init()',
                        'description' => '初始化框架',
                        'syntax' => 'XUN_Setup::init( $file, $premium )',
                        'parameters' => [
                            '$file' => ['type' => 'string', 'required' => true, 'description' => '框架主文件路径'],
                            '$premium' => ['type' => 'bool', 'required' => false, 'description' => '是否为高级版本']
                        ],
                        'return' => 'XUN_Setup',
                        'example' => "XUN_Setup::init( __FILE__, true );"
                    ]
                ]
            ],
            'XUN_Options' => [
                'title' => 'XUN_Options 选项类',
                'description' => '管理WordPress后台选项页面',
                'methods' => [
                    'get_options' => [
                        'title' => 'get_options()',
                        'description' => '获取选项数据',
                        'syntax' => '$options->get_options()',
                        'parameters' => [],
                        'return' => 'array',
                        'example' => "\$options = new XUN_Options( 'my_options', \$args );\n\$data = \$options->get_options();"
                    ],
                    'save_options' => [
                        'title' => 'save_options()',
                        'description' => '保存选项数据',
                        'syntax' => '$options->save_options( $data )',
                        'parameters' => [
                            '$data' => ['type' => 'array', 'required' => true, 'description' => '要保存的数据']
                        ],
                        'return' => 'bool',
                        'example' => "\$options->save_options( \$sanitized_data );"
                    ]
                ]
            ]
        ]
    ],
    'helpers' => [
        'title' => '辅助函数',
        'description' => '框架提供的全局辅助函数',
        'icon' => 'wrench-screwdriver',
        'color' => 'green',
        'items' => [
            'xun_get_option' => [
                'title' => 'xun_get_option()',
                'description' => '获取选项值的全局函数',
                'syntax' => 'xun_get_option( $option_name, $field_id, $default )',
                'parameters' => [
                    '$option_name' => ['type' => 'string', 'required' => true, 'description' => '选项组名称'],
                    '$field_id' => ['type' => 'string', 'required' => false, 'description' => '字段ID'],
                    '$default' => ['type' => 'mixed', 'required' => false, 'description' => '默认值']
                ],
                'return' => 'mixed',
                'example' => "\$value = xun_get_option( 'my_options', 'field_id', 'default' );"
            ],
            'xun_set_option' => [
                'title' => 'xun_set_option()',
                'description' => '设置选项值的全局函数',
                'syntax' => 'xun_set_option( $option_name, $field_id, $value )',
                'parameters' => [
                    '$option_name' => ['type' => 'string', 'required' => true, 'description' => '选项组名称'],
                    '$field_id' => ['type' => 'string', 'required' => true, 'description' => '字段ID'],
                    '$value' => ['type' => 'mixed', 'required' => true, 'description' => '要设置的值']
                ],
                'return' => 'bool',
                'example' => "xun_set_option( 'my_options', 'field_id', 'new_value' );"
            ]
        ]
    ],
    'hooks' => [
        'title' => '钩子系统',
        'description' => '框架提供的动作钩子和过滤器',
        'icon' => 'link',
        'color' => 'purple',
        'items' => [
            'actions' => [
                'title' => '动作钩子 (Actions)',
                'description' => '框架触发的动作钩子',
                'hooks' => [
                    'xun_init' => [
                        'title' => 'xun_init',
                        'description' => '框架初始化时触发',
                        'parameters' => [],
                        'example' => "add_action( 'xun_init', function() {\n    // 框架初始化后的操作\n} );"
                    ],
                    'xun_{unique}_saved' => [
                        'title' => 'xun_{unique}_saved',
                        'description' => '选项保存后触发',
                        'parameters' => [
                            '$data' => ['type' => 'array', 'description' => '保存的数据'],
                            '$instance' => ['type' => 'XUN_Options', 'description' => '选项实例']
                        ],
                        'example' => "add_action( 'xun_my_options_saved', function( \$data, \$instance ) {\n    // 选项保存后的操作\n}, 10, 2 );"
                    ]
                ]
            ],
            'filters' => [
                'title' => '过滤器钩子 (Filters)',
                'description' => '框架提供的过滤器钩子',
                'hooks' => [
                    'xun_validate_field_{type}' => [
                        'title' => 'xun_validate_field_{type}',
                        'description' => '字段验证过滤器',
                        'parameters' => [
                            '$value' => ['type' => 'mixed', 'description' => '字段值'],
                            '$field' => ['type' => 'array', 'description' => '字段配置']
                        ],
                        'return' => 'mixed',
                        'example' => "add_filter( 'xun_validate_field_text', function( \$value, \$field ) {\n    // 自定义文本字段验证\n    return sanitize_text_field( \$value );\n}, 10, 2 );"
                    ],
                    'xun_{unique}_args' => [
                        'title' => 'xun_{unique}_args',
                        'description' => '选项页面参数过滤器',
                        'parameters' => [
                            '$args' => ['type' => 'array', 'description' => '页面参数']
                        ],
                        'return' => 'array',
                        'example' => "add_filter( 'xun_my_options_args', function( \$args ) {\n    // 修改选项页面参数\n    \$args['menu_title'] = '自定义标题';\n    return \$args;\n} );"
                    ]
                ]
            ]
        ]
    ]
];

// 引入通用头部
include 'includes/header.php';
?>

        <!-- 主要内容区域 -->
        <main class="flex-1">
            <!-- Hero区域 -->
            <section class="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-20">
                <!-- 背景装饰 -->
                <div class="absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
                <div class="absolute bottom-0 left-0 -mb-20 -ml-20 w-80 h-80 bg-gradient-to-tr from-green-400/20 to-blue-400/20 rounded-full blur-3xl"></div>

                <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center">
                        <h1 class="text-3xl lg:text-5xl font-bold text-gray-900 mb-6">
                            <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                API参考
                            </span>
                        </h1>
                        
                        <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                            完整的API文档，包括<strong>核心类</strong>、<strong>方法</strong>、<strong>钩子</strong>和<strong>过滤器</strong>的详细说明和使用示例。
                        </p>

                        <!-- 统计信息 -->
                        <div class="flex flex-wrap justify-center gap-6 mb-12">
                            <div class="bg-white rounded-xl px-6 py-4 shadow-sm border border-gray-200">
                                <div class="text-2xl font-bold text-blue-600">3</div>
                                <div class="text-sm text-gray-600">核心类</div>
                            </div>
                            <div class="bg-white rounded-xl px-6 py-4 shadow-sm border border-gray-200">
                                <div class="text-2xl font-bold text-green-600">8</div>
                                <div class="text-sm text-gray-600">主要方法</div>
                            </div>
                            <div class="bg-white rounded-xl px-6 py-4 shadow-sm border border-gray-200">
                                <div class="text-2xl font-bold text-purple-600">6</div>
                                <div class="text-sm text-gray-600">钩子过滤器</div>
                            </div>
                        </div>

                        <!-- 搜索框 -->
                        <div class="max-w-md mx-auto">
                            <div class="relative">
                                <input 
                                    type="text" 
                                    id="api-search"
                                    placeholder="搜索API方法或类..."
                                    class="w-full px-4 py-3 pl-12 pr-4 text-gray-900 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                                >
                                <div class="absolute inset-y-0 left-0 flex items-center pl-4">
                                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- API分类导航 -->
            <section class="py-12 bg-white border-b border-gray-200">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">API分类</h2>
                        <p class="text-gray-600 max-w-2xl mx-auto">
                            按功能分类浏览所有API，快速找到您需要的方法和类
                        </p>
                    </div>

                    <!-- 分类卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <?php foreach ($api_categories as $key => $category): ?>
                        <div class="api-category-card bg-white rounded-2xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1" data-category="<?php echo $key; ?>">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-<?php echo $category['color']; ?>-100 rounded-xl flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-<?php echo $category['color']; ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        <?php echo htmlspecialchars($category['title']); ?>
                                    </h3>
                                    <p class="text-sm text-gray-500">
                                        <?php echo count($category['items']); ?> 项
                                    </p>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4">
                                <?php echo htmlspecialchars($category['description']); ?>
                            </p>
                            <a href="#<?php echo $key; ?>" class="inline-flex items-center text-<?php echo $category['color']; ?>-600 hover:text-<?php echo $category['color']; ?>-700 font-medium">
                                查看详情
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </section>

            <!-- API详细内容 -->
            <section class="py-20 bg-gray-50">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="flex flex-col lg:flex-row gap-8">
                        <!-- 侧边栏导航 -->
                        <div class="lg:w-1/4">
                            <div class="sticky top-8">
                                <div class="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
                                    <h3 class="text-lg font-semibold text-gray-900 mb-4">快速导航</h3>
                                    <nav class="space-y-2">
                                        <?php foreach ($api_categories as $key => $category): ?>
                                        <div class="api-nav-section">
                                            <a href="#<?php echo $key; ?>" class="flex items-center px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50 rounded-lg transition-colors">
                                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <?php echo htmlspecialchars($category['title']); ?>
                                            </a>
                                            <?php if ($key === 'core'): ?>
                                            <div class="ml-6 mt-2 space-y-1">
                                                <?php foreach ($category['items'] as $item_key => $item): ?>
                                                <a href="#<?php echo $item_key; ?>" class="block px-3 py-1 text-sm text-gray-600 hover:text-blue-600 transition-colors">
                                                    <?php echo htmlspecialchars($item['title']); ?>
                                                </a>
                                                <?php endforeach; ?>
                                            </div>
                                            <?php endif; ?>
                                        </div>
                                        <?php endforeach; ?>
                                    </nav>
                                </div>
                            </div>
                        </div>

                        <!-- 主要内容 -->
                        <div class="lg:w-3/4">
                            <div class="space-y-16">
                                <?php foreach ($api_categories as $category_key => $category): ?>
                                <div id="<?php echo $category_key; ?>" class="api-section">
                                    <!-- 分类标题 -->
                                    <div class="flex items-center mb-8">
                                        <div class="w-10 h-10 bg-<?php echo $category['color']; ?>-100 rounded-lg flex items-center justify-center mr-4">
                                            <svg class="w-5 h-5 text-<?php echo $category['color']; ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <h2 class="text-3xl font-bold text-gray-900">
                                                <?php echo htmlspecialchars($category['title']); ?>
                                            </h2>
                                            <p class="text-gray-600 mt-1">
                                                <?php echo htmlspecialchars($category['description']); ?>
                                            </p>
                                        </div>
                                    </div>

                                    <!-- 分类内容 -->
                                    <?php if ($category_key === 'core'): ?>
                                        <!-- 核心类 -->
                                        <div class="space-y-12">
                                            <?php foreach ($category['items'] as $class_key => $class_info): ?>
                                            <div id="<?php echo $class_key; ?>" class="api-class bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
                                                <!-- 类头部 -->
                                                <div class="p-6 border-b border-gray-100">
                                                    <h3 class="text-2xl font-bold text-gray-900 mb-2">
                                                        <?php echo htmlspecialchars($class_info['title']); ?>
                                                    </h3>
                                                    <p class="text-gray-600">
                                                        <?php echo htmlspecialchars($class_info['description']); ?>
                                                    </p>
                                                </div>

                                                <!-- 方法列表 -->
                                                <div class="p-6">
                                                    <h4 class="text-lg font-semibold text-gray-900 mb-4">方法</h4>
                                                    <div class="space-y-6">
                                                        <?php foreach ($class_info['methods'] as $method_key => $method): ?>
                                                        <div class="api-method border border-gray-200 rounded-xl p-6">
                                                            <div class="flex items-start justify-between mb-4">
                                                                <div>
                                                                    <h5 class="text-lg font-semibold text-gray-900 mb-1">
                                                                        <?php echo htmlspecialchars($method['title']); ?>
                                                                    </h5>
                                                                    <p class="text-gray-600">
                                                                        <?php echo htmlspecialchars($method['description']); ?>
                                                                    </p>
                                                                </div>
                                                                <button class="method-expand-btn text-gray-400 hover:text-gray-600 transition-colors">
                                                                    <svg class="w-5 h-5 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                                    </svg>
                                                                </button>
                                                            </div>

                                                            <!-- 方法详情 -->
                                                            <div class="method-details hidden">
                                                                <!-- 语法 -->
                                                                <div class="mb-4">
                                                                    <h6 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-2">语法</h6>
                                                                    <div class="bg-gray-900 rounded-lg p-3">
                                                                        <code class="text-green-400 text-sm"><?php echo htmlspecialchars($method['syntax']); ?></code>
                                                                    </div>
                                                                </div>

                                                                <!-- 参数 -->
                                                                <?php if (!empty($method['parameters'])): ?>
                                                                <div class="mb-4">
                                                                    <h6 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-2">参数</h6>
                                                                    <div class="space-y-2">
                                                                        <?php foreach ($method['parameters'] as $param_name => $param_info): ?>
                                                                        <div class="flex items-start">
                                                                            <code class="bg-gray-100 px-2 py-1 rounded text-sm text-gray-800 mr-3"><?php echo htmlspecialchars($param_name); ?></code>
                                                                            <div class="flex-1">
                                                                                <span class="text-sm text-blue-600 font-medium"><?php echo htmlspecialchars($param_info['type']); ?></span>
                                                                                <?php if ($param_info['required']): ?>
                                                                                <span class="text-xs text-red-600 ml-1">(必需)</span>
                                                                                <?php else: ?>
                                                                                <span class="text-xs text-gray-500 ml-1">(可选)</span>
                                                                                <?php endif; ?>
                                                                                <p class="text-sm text-gray-600 mt-1"><?php echo htmlspecialchars($param_info['description']); ?></p>
                                                                            </div>
                                                                        </div>
                                                                        <?php endforeach; ?>
                                                                    </div>
                                                                </div>
                                                                <?php endif; ?>

                                                                <!-- 返回值 -->
                                                                <div class="mb-4">
                                                                    <h6 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-2">返回值</h6>
                                                                    <span class="text-sm text-blue-600 font-medium"><?php echo htmlspecialchars($method['return']); ?></span>
                                                                </div>

                                                                <!-- 示例 -->
                                                                <div>
                                                                    <h6 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-2">示例</h6>
                                                                    <div class="bg-gray-900 rounded-lg p-4">
                                                                        <div class="flex items-center justify-between mb-2">
                                                                            <span class="text-gray-400 text-sm">PHP</span>
                                                                            <button class="copy-button text-gray-400 hover:text-white text-sm" data-copy-target="example-<?php echo $class_key; ?>-<?php echo $method_key; ?>">
                                                                                复制
                                                                            </button>
                                                                        </div>
                                                                        <pre id="example-<?php echo $class_key; ?>-<?php echo $method_key; ?>" class="text-sm overflow-x-auto"><code class="language-php"><?php echo htmlspecialchars($method['example']); ?></code></pre>
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php elseif ($category_key === 'helpers'): ?>
                                        <!-- 辅助函数 -->
                                        <div class="space-y-8">
                                            <?php foreach ($category['items'] as $func_key => $func_info): ?>
                                            <div class="api-function bg-white rounded-2xl shadow-sm border border-gray-200 p-6">
                                                <h3 class="text-xl font-bold text-gray-900 mb-2">
                                                    <?php echo htmlspecialchars($func_info['title']); ?>
                                                </h3>
                                                <p class="text-gray-600 mb-4">
                                                    <?php echo htmlspecialchars($func_info['description']); ?>
                                                </p>

                                                <!-- 语法 -->
                                                <div class="mb-4">
                                                    <h6 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-2">语法</h6>
                                                    <div class="bg-gray-900 rounded-lg p-3">
                                                        <code class="text-green-400 text-sm"><?php echo htmlspecialchars($func_info['syntax']); ?></code>
                                                    </div>
                                                </div>

                                                <!-- 参数 -->
                                                <?php if (!empty($func_info['parameters'])): ?>
                                                <div class="mb-4">
                                                    <h6 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-2">参数</h6>
                                                    <div class="space-y-2">
                                                        <?php foreach ($func_info['parameters'] as $param_name => $param_info): ?>
                                                        <div class="flex items-start">
                                                            <code class="bg-gray-100 px-2 py-1 rounded text-sm text-gray-800 mr-3"><?php echo htmlspecialchars($param_name); ?></code>
                                                            <div class="flex-1">
                                                                <span class="text-sm text-blue-600 font-medium"><?php echo htmlspecialchars($param_info['type']); ?></span>
                                                                <?php if ($param_info['required']): ?>
                                                                <span class="text-xs text-red-600 ml-1">(必需)</span>
                                                                <?php else: ?>
                                                                <span class="text-xs text-gray-500 ml-1">(可选)</span>
                                                                <?php endif; ?>
                                                                <p class="text-sm text-gray-600 mt-1"><?php echo htmlspecialchars($param_info['description']); ?></p>
                                                            </div>
                                                        </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                                <?php endif; ?>

                                                <!-- 返回值 -->
                                                <div class="mb-4">
                                                    <h6 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-2">返回值</h6>
                                                    <span class="text-sm text-blue-600 font-medium"><?php echo htmlspecialchars($func_info['return']); ?></span>
                                                </div>

                                                <!-- 示例 -->
                                                <div>
                                                    <h6 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-2">示例</h6>
                                                    <div class="bg-gray-900 rounded-lg p-4">
                                                        <div class="flex items-center justify-between mb-2">
                                                            <span class="text-gray-400 text-sm">PHP</span>
                                                            <button class="copy-button text-gray-400 hover:text-white text-sm" data-copy-target="example-<?php echo $func_key; ?>">
                                                                复制
                                                            </button>
                                                        </div>
                                                        <pre id="example-<?php echo $func_key; ?>" class="text-sm overflow-x-auto"><code class="language-php"><?php echo htmlspecialchars($func_info['example']); ?></code></pre>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>

                                    <?php elseif ($category_key === 'hooks'): ?>
                                        <!-- 钩子系统 -->
                                        <div class="space-y-8">
                                            <?php foreach ($category['items'] as $hook_type_key => $hook_type): ?>
                                            <div class="hook-type bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden">
                                                <div class="p-6 border-b border-gray-100">
                                                    <h3 class="text-2xl font-bold text-gray-900 mb-2">
                                                        <?php echo htmlspecialchars($hook_type['title']); ?>
                                                    </h3>
                                                    <p class="text-gray-600">
                                                        <?php echo htmlspecialchars($hook_type['description']); ?>
                                                    </p>
                                                </div>

                                                <div class="p-6">
                                                    <div class="space-y-6">
                                                        <?php foreach ($hook_type['hooks'] as $hook_key => $hook): ?>
                                                        <div class="hook-item border border-gray-200 rounded-xl p-6">
                                                            <h4 class="text-lg font-semibold text-gray-900 mb-2">
                                                                <code class="bg-gray-100 px-2 py-1 rounded text-sm"><?php echo htmlspecialchars($hook['title']); ?></code>
                                                            </h4>
                                                            <p class="text-gray-600 mb-4">
                                                                <?php echo htmlspecialchars($hook['description']); ?>
                                                            </p>

                                                            <!-- 参数 -->
                                                            <?php if (!empty($hook['parameters'])): ?>
                                                            <div class="mb-4">
                                                                <h6 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-2">参数</h6>
                                                                <div class="space-y-2">
                                                                    <?php foreach ($hook['parameters'] as $param_name => $param_info): ?>
                                                                    <div class="flex items-start">
                                                                        <code class="bg-gray-100 px-2 py-1 rounded text-sm text-gray-800 mr-3"><?php echo htmlspecialchars($param_name); ?></code>
                                                                        <div class="flex-1">
                                                                            <span class="text-sm text-blue-600 font-medium"><?php echo htmlspecialchars($param_info['type']); ?></span>
                                                                            <p class="text-sm text-gray-600 mt-1"><?php echo htmlspecialchars($param_info['description']); ?></p>
                                                                        </div>
                                                                    </div>
                                                                    <?php endforeach; ?>
                                                                </div>
                                                            </div>
                                                            <?php endif; ?>

                                                            <!-- 返回值 -->
                                                            <?php if (isset($hook['return'])): ?>
                                                            <div class="mb-4">
                                                                <h6 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-2">返回值</h6>
                                                                <span class="text-sm text-blue-600 font-medium"><?php echo htmlspecialchars($hook['return']); ?></span>
                                                            </div>
                                                            <?php endif; ?>

                                                            <!-- 示例 -->
                                                            <div>
                                                                <h6 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-2">示例</h6>
                                                                <div class="bg-gray-900 rounded-lg p-4">
                                                                    <div class="flex items-center justify-between mb-2">
                                                                        <span class="text-gray-400 text-sm">PHP</span>
                                                                        <button class="copy-button text-gray-400 hover:text-white text-sm" data-copy-target="example-<?php echo $hook_type_key; ?>-<?php echo $hook_key; ?>">
                                                                            复制
                                                                        </button>
                                                                    </div>
                                                                    <pre id="example-<?php echo $hook_type_key; ?>-<?php echo $hook_key; ?>" class="text-sm overflow-x-auto"><code class="language-php"><?php echo htmlspecialchars($hook['example']); ?></code></pre>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <?php endforeach; ?>
                                                    </div>
                                                </div>
                                            </div>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>

    <!-- JavaScript交互功能 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('API Reference page loaded, initializing...');

            // 搜索功能
            const searchInput = document.getElementById('api-search');
            const apiSections = document.querySelectorAll('.api-section');
            const apiMethods = document.querySelectorAll('.api-method');
            const apiFunctions = document.querySelectorAll('.api-function');
            const hookItems = document.querySelectorAll('.hook-item');

            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                // 搜索方法
                apiMethods.forEach(method => {
                    const methodTitle = method.querySelector('h5').textContent.toLowerCase();
                    const methodDesc = method.querySelector('p').textContent.toLowerCase();

                    if (methodTitle.includes(searchTerm) || methodDesc.includes(searchTerm)) {
                        method.style.display = 'block';
                        method.closest('.api-class').style.display = 'block';
                        method.closest('.api-section').style.display = 'block';
                    } else {
                        method.style.display = 'none';
                    }
                });

                // 搜索函数
                apiFunctions.forEach(func => {
                    const funcTitle = func.querySelector('h3').textContent.toLowerCase();
                    const funcDesc = func.querySelector('p').textContent.toLowerCase();

                    if (funcTitle.includes(searchTerm) || funcDesc.includes(searchTerm)) {
                        func.style.display = 'block';
                        func.closest('.api-section').style.display = 'block';
                    } else {
                        func.style.display = 'none';
                    }
                });

                // 搜索钩子
                hookItems.forEach(hook => {
                    const hookTitle = hook.querySelector('h4').textContent.toLowerCase();
                    const hookDesc = hook.querySelector('p').textContent.toLowerCase();

                    if (hookTitle.includes(searchTerm) || hookDesc.includes(searchTerm)) {
                        hook.style.display = 'block';
                        hook.closest('.hook-type').style.display = 'block';
                        hook.closest('.api-section').style.display = 'block';
                    } else {
                        hook.style.display = 'none';
                    }
                });

                // 隐藏空的分类
                if (searchTerm !== '') {
                    apiSections.forEach(section => {
                        const visibleItems = section.querySelectorAll('.api-method[style="display: block"], .api-function[style="display: block"], .hook-item[style="display: block"]');
                        if (visibleItems.length === 0) {
                            section.style.display = 'none';
                        } else {
                            section.style.display = 'block';
                        }
                    });
                } else {
                    // 显示所有内容
                    apiSections.forEach(section => section.style.display = 'block');
                    apiMethods.forEach(method => method.style.display = 'block');
                    apiFunctions.forEach(func => func.style.display = 'block');
                    hookItems.forEach(hook => hook.style.display = 'block');
                    document.querySelectorAll('.api-class, .hook-type').forEach(item => item.style.display = 'block');
                }
            });

            // 方法展开/收起功能
            const expandButtons = document.querySelectorAll('.method-expand-btn');

            expandButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const method = this.closest('.api-method');
                    const details = method.querySelector('.method-details');
                    const icon = this.querySelector('svg');

                    if (details.classList.contains('hidden')) {
                        details.classList.remove('hidden');
                        icon.style.transform = 'rotate(180deg)';
                    } else {
                        details.classList.add('hidden');
                        icon.style.transform = 'rotate(0deg)';
                    }
                });
            });

            // 代码复制功能
            document.querySelectorAll('.copy-button').forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-copy-target');
                    const codeElement = document.getElementById(targetId);
                    const text = codeElement.textContent;

                    navigator.clipboard.writeText(text).then(() => {
                        const originalText = this.textContent;
                        this.textContent = '已复制';
                        this.classList.add('text-green-400');

                        setTimeout(() => {
                            this.textContent = originalText;
                            this.classList.remove('text-green-400');
                        }, 2000);
                    }).catch(err => {
                        console.error('复制失败:', err);
                    });
                });
            });

            // 平滑滚动到锚点
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // 侧边栏导航高亮
            const observerOptions = {
                rootMargin: '-20% 0px -70% 0px'
            };

            const observer = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const id = entry.target.id;
                        const navLink = document.querySelector(`a[href="#${id}"]`);
                        if (navLink) {
                            // 移除所有活动状态
                            document.querySelectorAll('.api-nav-section a').forEach(link => {
                                link.classList.remove('text-blue-600', 'bg-blue-50');
                                link.classList.add('text-gray-700');
                            });
                            // 添加当前活动状态
                            navLink.classList.remove('text-gray-700');
                            navLink.classList.add('text-blue-600', 'bg-blue-50');
                        }
                    }
                });
            }, observerOptions);

            // 观察所有API分类
            document.querySelectorAll('.api-section').forEach(section => {
                observer.observe(section);
            });
        });
    </script>

<?php
// 引入通用底部
include 'includes/footer.php';
?>
