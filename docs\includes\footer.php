<?php
/**
 * Xun Framework 文档系统 - 页面底部
 *
 * 包含页面底部信息、版权声明、链接等内容。
 * 结束主要的页面结构。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

$site_title = xun_docs_get_config('site_title');
$author = xun_docs_get_config('author');
$social_links = xun_docs_get_config('social_links');
?>

                </div>
                <!-- 结束页面内容容器 -->
            </main>
            <!-- 结束主内容区域 -->
        </div>
        <!-- 结束主要内容区域 -->

        <!-- 页面底部 -->
        <footer class="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700 mt-auto">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <!-- 框架信息 -->
                    <div class="col-span-1 lg:col-span-2">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                <span class="text-white font-bold text-lg">X</span>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900 dark:text-white">
                                    <?php echo xun_docs_esc_html($site_title); ?>
                                </h3>
                                <p class="text-sm text-gray-500 dark:text-gray-400">
                                    现代化的WordPress选项框架
                                </p>
                            </div>
                        </div>
                        <p class="text-gray-600 dark:text-gray-400 mb-4 max-w-md">
                            基于PHP 8.1+和TailwindCSS 4.x构建，提供23种字段类型，
                            支持深色主题和响应式设计的WordPress选项框架。
                        </p>
                        <div class="flex space-x-4">
                            <?php if (!empty($social_links['github'])): ?>
                            <a 
                                href="<?php echo xun_docs_esc_url($social_links['github']); ?>" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                                aria-label="GitHub"
                            >
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                </svg>
                            </a>
                            <?php endif; ?>
                            
                            <?php if (!empty($social_links['website'])): ?>
                            <a 
                                href="<?php echo xun_docs_esc_url($social_links['website']); ?>" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                                aria-label="官方网站"
                            >
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                                </svg>
                            </a>
                            <?php endif; ?>
                            
                            <?php if (!empty($social_links['email'])): ?>
                            <a 
                                href="mailto:<?php echo xun_docs_esc_attr($social_links['email']); ?>" 
                                class="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
                                aria-label="邮箱联系"
                            >
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </a>
                            <?php endif; ?>
                        </div>
                    </div>

                    <!-- 快速链接 -->
                    <div>
                        <h4 class="font-semibold text-gray-900 dark:text-white mb-4">快速开始</h4>
                        <ul class="space-y-2">
                            <li>
                                <a href="<?php echo xun_docs_get_page_url('getting-started'); ?>" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                    快速开始
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo xun_docs_get_page_url('installation'); ?>" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                    安装指南
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo xun_docs_get_page_url('basic-usage'); ?>" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                    基础用法
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo xun_docs_get_page_url('examples'); ?>" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                    示例代码
                                </a>
                            </li>
                        </ul>
                    </div>

                    <!-- 文档链接 -->
                    <div>
                        <h4 class="font-semibold text-gray-900 dark:text-white mb-4">文档</h4>
                        <ul class="space-y-2">
                            <li>
                                <a href="<?php echo xun_docs_get_page_url('field-types'); ?>" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                    字段类型
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo xun_docs_get_page_url('api-reference'); ?>" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                    API 参考
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo xun_docs_get_page_url('hooks-filters'); ?>" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                    钩子过滤器
                                </a>
                            </li>
                            <li>
                                <a href="<?php echo xun_docs_get_page_url('best-practices'); ?>" class="text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                    最佳实践
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <!-- 底部分割线 -->
                <div class="border-t border-gray-200 dark:border-gray-700 mt-8 pt-8">
                    <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
                        <!-- 版权信息 -->
                        <div class="text-center md:text-left">
                            <p class="text-sm text-gray-500 dark:text-gray-400">
                                © 2025 <?php echo xun_docs_esc_html($site_title); ?>. 
                                Made with ❤️ by 
                                <a href="<?php echo xun_docs_esc_url($social_links['website'] ?? '#'); ?>" class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors">
                                    <?php echo xun_docs_esc_html($author); ?>
                                </a>
                            </p>
                        </div>

                        <!-- 版本和许可信息 -->
                        <div class="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                            <span class="flex items-center space-x-1">
                                <span>版本</span>
                                <code class="px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded text-xs font-mono">
                                    <?php echo xun_docs_esc_html(XUN_DOCS_VERSION); ?>
                                </code>
                            </span>
                            <span class="hidden md:block">•</span>
                            <a href="<?php echo xun_docs_get_page_url('license'); ?>" class="hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
                                MIT 许可证
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>
    <!-- 结束页面容器 -->

    <!-- 返回顶部按钮 -->
    <button 
        id="back-to-top" 
        class="fixed bottom-8 right-8 w-12 h-12 bg-blue-600 hover:bg-blue-700 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 opacity-0 invisible z-50 flex items-center justify-center"
        aria-label="返回顶部"
    >
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
        </svg>
    </button>

    <!-- JavaScript文件 -->
    <script src="<?php echo xun_docs_get_asset_url('js/docs.js'); ?>"></script>
    
    <!-- 代码高亮 -->
    <?php if (xun_docs_get_config('enable_code_copy')): ?>
    <script src="<?php echo xun_docs_get_asset_url('js/prism.js'); ?>"></script>
    <?php endif; ?>
    
    <!-- 搜索功能 -->
    <?php if (xun_docs_get_config('enable_search')): ?>
    <script src="<?php echo xun_docs_get_asset_url('js/search.js'); ?>"></script>
    <?php endif; ?>
    
    <!-- 主题切换 -->
    <?php if (xun_docs_get_config('enable_theme_switch')): ?>
    <script src="<?php echo xun_docs_get_asset_url('js/theme.js'); ?>"></script>
    <?php endif; ?>

    <!-- 页面特定脚本 -->
    <?php if (!empty($page_scripts)): ?>
        <?php foreach ($page_scripts as $script): ?>
        <script src="<?php echo xun_docs_get_asset_url($script); ?>"></script>
        <?php endforeach; ?>
    <?php endif; ?>

    <!-- 内联脚本 -->
    <?php if (!empty($inline_scripts)): ?>
    <script>
        <?php echo $inline_scripts; ?>
    </script>
    <?php endif; ?>

    <!-- 分析代码 -->
    <?php if (xun_docs_get_config('enable_analytics')): ?>
    <!-- 这里可以添加Google Analytics或其他分析代码 -->
    <?php endif; ?>
</body>
</html>
