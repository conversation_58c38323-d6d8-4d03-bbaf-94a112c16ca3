<?php
/**
 * Xun Framework 文档系统 - 首页内容
 *
 * 这是文档系统的首页落地页，展示框架特性、快速导航和核心功能。
 * 采用现代化设计，突出框架的主要优势。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

$field_types = xun_docs_get_field_types();
$total_fields = 0;
foreach ($field_types as $category) {
    $total_fields += count($category['fields']);
}
?>

<!-- Hero区域 -->
<div class="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-indigo-50 dark:from-gray-900 dark:via-gray-900 dark:to-blue-900/20">
    <!-- 背景装饰 -->
    <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
    <div class="absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
    <div class="absolute bottom-0 left-0 -mb-20 -ml-20 w-80 h-80 bg-gradient-to-tr from-green-400/20 to-blue-400/20 rounded-full blur-3xl"></div>
    
    <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 lg:py-32">
        <div class="text-center">
            <!-- Logo和标题 -->
            <div class="flex justify-center mb-8">
                <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-xl">
                    <span class="text-white font-bold text-3xl">X</span>
                </div>
            </div>
            
            <h1 class="text-5xl lg:text-7xl font-bold text-gray-900 dark:text-white mb-6">
                <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                    Xun Framework
                </span>
            </h1>
            
            <p class="text-xl lg:text-2xl text-gray-600 dark:text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
                现代化的 <strong class="text-blue-600 dark:text-blue-400">WordPress 选项框架</strong>，
                基于 PHP 8.1+ 和 TailwindCSS 4.x 构建，
                提供 <strong class="text-purple-600 dark:text-purple-400"><?php echo $total_fields; ?> 种字段类型</strong>
            </p>
            
            <!-- 特性标签 -->
            <div class="flex flex-wrap justify-center gap-3 mb-12">
                <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                    <?php echo xun_docs_get_icon('code-bracket', 'outline', 'w-4 h-4 mr-2'); ?>
                    PHP 8.1+
                </span>
                <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300">
                    <?php echo xun_docs_get_icon('swatch', 'outline', 'w-4 h-4 mr-2'); ?>
                    TailwindCSS 4.x
                </span>
                <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                    <?php echo xun_docs_get_icon('device-phone-mobile', 'outline', 'w-4 h-4 mr-2'); ?>
                    响应式设计
                </span>
                <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300">
                    <?php echo xun_docs_get_icon('moon', 'outline', 'w-4 h-4 mr-2'); ?>
                    深色主题
                </span>
            </div>
            
            <!-- 行动按钮 -->
            <div class="flex flex-col sm:flex-row gap-4 justify-center">
                <a 
                    href="<?php echo xun_docs_get_page_url('getting-started'); ?>" 
                    class="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1"
                >
                    <?php echo xun_docs_get_icon('rocket-launch', 'outline', 'w-6 h-6 mr-2'); ?>
                    快速开始
                </a>
                <a 
                    href="<?php echo xun_docs_get_page_url('examples'); ?>" 
                    class="inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1"
                >
                    <?php echo xun_docs_get_icon('code-bracket', 'outline', 'w-6 h-6 mr-2'); ?>
                    查看示例
                </a>
            </div>
        </div>
    </div>
</div>

<!-- 核心特性 -->
<div class="py-20 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                为什么选择 Xun Framework？
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                专为现代 WordPress 开发而设计，提供强大而灵活的选项管理解决方案
            </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 特性1：现代化设计 -->
            <div class="group p-8 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl border border-blue-200 dark:border-blue-800 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <?php echo xun_docs_get_icon('sparkles', 'outline', 'w-6 h-6 text-white'); ?>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">现代化设计</h3>
                <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                    基于 TailwindCSS 4.x 构建，提供美观的深色主题和完全响应式的用户界面，适配所有设备。
                </p>
            </div>
            
            <!-- 特性2：丰富字段 -->
            <div class="group p-8 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-2xl border border-purple-200 dark:border-purple-800 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div class="w-12 h-12 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <?php echo xun_docs_get_icon('squares-2x2', 'outline', 'w-6 h-6 text-white'); ?>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4"><?php echo $total_fields; ?> 种字段类型</h3>
                <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                    从基础的文本输入到复杂的重复器字段，涵盖所有常见的选项类型，满足各种开发需求。
                </p>
            </div>
            
            <!-- 特性3：易于使用 -->
            <div class="group p-8 bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl border border-green-200 dark:border-green-800 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div class="w-12 h-12 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <?php echo xun_docs_get_icon('bolt', 'outline', 'w-6 h-6 text-white'); ?>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">简单易用</h3>
                <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                    简洁的 API 设计，只需几行代码即可创建功能完整的选项页面，大大提高开发效率。
                </p>
            </div>
            
            <!-- 特性4：高性能 -->
            <div class="group p-8 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-2xl border border-orange-200 dark:border-orange-800 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div class="w-12 h-12 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <?php echo xun_docs_get_icon('lightning-bolt', 'outline', 'w-6 h-6 text-white'); ?>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">极速性能</h3>
                <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                    优化的代码结构和懒加载机制，确保框架运行高效，不会影响网站性能。
                </p>
            </div>
            
            <!-- 特性5：扩展性强 -->
            <div class="group p-8 bg-gradient-to-br from-teal-50 to-cyan-50 dark:from-teal-900/20 dark:to-cyan-900/20 rounded-2xl border border-teal-200 dark:border-teal-800 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div class="w-12 h-12 bg-gradient-to-br from-teal-500 to-teal-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <?php echo xun_docs_get_icon('puzzle-piece', 'outline', 'w-6 h-6 text-white'); ?>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">高度可扩展</h3>
                <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                    丰富的钩子和过滤器系统，支持自定义字段类型，轻松扩展框架功能。
                </p>
            </div>
            
            <!-- 特性6：开源免费 -->
            <div class="group p-8 bg-gradient-to-br from-indigo-50 to-blue-50 dark:from-indigo-900/20 dark:to-blue-900/20 rounded-2xl border border-indigo-200 dark:border-indigo-800 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div class="w-12 h-12 bg-gradient-to-br from-indigo-500 to-indigo-600 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300">
                    <?php echo xun_docs_get_icon('heart', 'outline', 'w-6 h-6 text-white'); ?>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">开源免费</h3>
                <p class="text-gray-600 dark:text-gray-400 leading-relaxed">
                    MIT 开源许可证，完全免费使用，活跃的社区支持和持续的功能更新。
                </p>
            </div>
        </div>
    </div>
</div>

<!-- 快速开始 -->
<div class="py-20 bg-gray-50 dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                5 分钟快速上手
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                简单几步，即可创建功能完整的 WordPress 选项页面
            </p>
        </div>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <!-- 步骤说明 -->
            <div class="space-y-8">
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0 w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">1</div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">下载框架</h3>
                        <p class="text-gray-600 dark:text-gray-400">从 GitHub 下载最新版本，或通过 Composer 安装到你的项目中。</p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0 w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">2</div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">引入框架</h3>
                        <p class="text-gray-600 dark:text-gray-400">在主题的 functions.php 文件中引入框架，只需一行代码。</p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0 w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">3</div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">创建选项</h3>
                        <p class="text-gray-600 dark:text-gray-400">使用简洁的 API 创建选项页面和字段，支持所有常见类型。</p>
                    </div>
                </div>
                
                <div class="flex items-start space-x-4">
                    <div class="flex-shrink-0 w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">4</div>
                    <div>
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-2">获取数据</h3>
                        <p class="text-gray-600 dark:text-gray-400">在主题中轻松获取和使用选项数据，实现动态内容展示。</p>
                    </div>
                </div>
            </div>
            
            <!-- 代码示例 -->
            <div class="bg-gray-900 rounded-2xl p-6 overflow-hidden">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex space-x-2">
                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                    </div>
                    <span class="text-gray-400 text-sm">functions.php</span>
                </div>
                <pre class="text-sm text-gray-300 overflow-x-auto"><code><?php echo htmlspecialchars('<?php
// 1. 引入框架
require_once get_template_directory() . \'/xun-framework/xun-framework.php\';

// 2. 创建选项页面
XUN::createOptions( \'my_theme_options\', array(
    \'menu_title\'      => \'主题设置\',
    \'menu_slug\'       => \'my-theme-options\',
    \'framework_title\' => \'我的主题设置\',
) );

// 3. 添加字段
XUN::createSection( \'my_theme_options\', array(
    \'title\'  => \'基本设置\',
    \'fields\' => array(
        array(
            \'id\'    => \'site_logo\',
            \'type\'  => \'media\',
            \'title\' => \'网站Logo\',
        ),
        array(
            \'id\'      => \'theme_color\',
            \'type\'    => \'color\',
            \'title\'   => \'主题颜色\',
            \'default\' => \'#2563eb\',
        ),
    ),
) );

// 4. 获取选项值
$logo = XUN::get_option( \'my_theme_options\', \'site_logo\' );
$color = XUN::get_option( \'my_theme_options\', \'theme_color\' );'); ?></code></pre>
            </div>
        </div>
        
        <div class="text-center mt-12">
            <a 
                href="<?php echo xun_docs_get_page_url('getting-started'); ?>" 
                class="inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1"
            >
                开始使用 Xun Framework
                <?php echo xun_docs_get_icon('arrow-right', 'outline', 'w-5 h-5 ml-2'); ?>
            </a>
        </div>
    </div>
</div>

<!-- 字段类型展示 -->
<div class="py-20 bg-white dark:bg-gray-900">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                丰富的字段类型
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                <?php echo $total_fields; ?> 种精心设计的字段类型，满足各种选项配置需求
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <?php foreach ($field_types as $category_id => $category): ?>
            <div class="group">
                <div class="bg-gradient-to-br from-gray-50 to-white dark:from-gray-800 dark:to-gray-900 rounded-2xl border border-gray-200 dark:border-gray-700 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
                            <?php echo xun_docs_esc_html($category['title']); ?>
                        </h3>
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300">
                            <?php echo count($category['fields']); ?> 个
                        </span>
                    </div>

                    <p class="text-gray-600 dark:text-gray-400 text-sm mb-4">
                        <?php echo xun_docs_esc_html($category['description']); ?>
                    </p>

                    <div class="grid grid-cols-2 gap-2">
                        <?php foreach (array_slice($category['fields'], 0, 4) as $field_id => $field): ?>
                        <div class="flex items-center space-x-2 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                            <div class="w-2 h-2 bg-<?php echo $field['color']; ?>-500 rounded-full"></div>
                            <span class="text-xs text-gray-700 dark:text-gray-300 truncate">
                                <?php echo xun_docs_esc_html($field['title']); ?>
                            </span>
                        </div>
                        <?php endforeach; ?>

                        <?php if (count($category['fields']) > 4): ?>
                        <div class="col-span-2 text-center">
                            <span class="text-xs text-gray-500 dark:text-gray-400">
                                还有 <?php echo count($category['fields']) - 4; ?> 个字段...
                            </span>
                        </div>
                        <?php endif; ?>
                    </div>

                    <div class="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                        <a
                            href="<?php echo xun_docs_get_page_url('field-types'); ?>"
                            class="text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 text-sm font-medium group-hover:underline transition-colors"
                        >
                            查看所有字段 →
                        </a>
                    </div>
                </div>
            </div>
            <?php endforeach; ?>
        </div>

        <div class="text-center mt-12">
            <a
                href="<?php echo xun_docs_get_page_url('field-types'); ?>"
                class="inline-flex items-center px-8 py-4 text-lg font-semibold text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:-translate-y-1"
            >
                探索所有字段类型
                <?php echo xun_docs_get_icon('arrow-right', 'outline', 'w-5 h-5 ml-2'); ?>
            </a>
        </div>
    </div>
</div>

<!-- 统计数据 -->
<div class="py-20 bg-gradient-to-br from-blue-600 to-purple-700">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-white mb-4">
                框架数据一览
            </h2>
            <p class="text-xl text-blue-100 max-w-3xl mx-auto">
                持续更新和完善的现代化 WordPress 选项框架
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div class="text-center">
                <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <?php echo xun_docs_get_icon('squares-2x2', 'outline', 'w-8 h-8 text-white'); ?>
                </div>
                <div class="text-4xl font-bold text-white mb-2"><?php echo $total_fields; ?></div>
                <div class="text-blue-100">字段类型</div>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                    </svg>
                </div>
                <div class="text-4xl font-bold text-white mb-2">PHP 8.1+</div>
                <div class="text-blue-100">最低版本要求</div>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <?php echo xun_docs_get_icon('heart', 'outline', 'w-8 h-8 text-white'); ?>
                </div>
                <div class="text-4xl font-bold text-white mb-2">MIT</div>
                <div class="text-blue-100">开源许可证</div>
            </div>

            <div class="text-center">
                <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                    <?php echo xun_docs_get_icon('star', 'outline', 'w-8 h-8 text-white'); ?>
                </div>
                <div class="text-4xl font-bold text-white mb-2">v<?php echo XUN_DOCS_VERSION; ?></div>
                <div class="text-blue-100">当前版本</div>
            </div>
        </div>
    </div>
</div>

<!-- 社区和支持 -->
<div class="py-20 bg-gray-50 dark:bg-gray-800">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16">
            <h2 class="text-4xl font-bold text-gray-900 dark:text-white mb-4">
                社区与支持
            </h2>
            <p class="text-xl text-gray-600 dark:text-gray-400 max-w-3xl mx-auto">
                加入我们的社区，获取帮助，分享经验，共同完善框架
            </p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <!-- GitHub -->
            <div class="text-center p-8 bg-white dark:bg-gray-900 rounded-2xl border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <div class="w-16 h-16 bg-gray-900 dark:bg-white rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <svg class="w-8 h-8 text-white dark:text-gray-900" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">GitHub 仓库</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">
                    查看源代码，提交问题，参与开发，为项目贡献力量。
                </p>
                <a
                    href="<?php echo xun_docs_esc_url(xun_docs_get_config('social_links')['github'] ?? '#'); ?>"
                    target="_blank"
                    rel="noopener noreferrer"
                    class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
                >
                    访问 GitHub
                    <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                    </svg>
                </a>
            </div>

            <!-- 文档 -->
            <div class="text-center p-8 bg-white dark:bg-gray-900 rounded-2xl border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <div class="w-16 h-16 bg-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <?php echo xun_docs_get_icon('book-open', 'outline', 'w-8 h-8 text-white'); ?>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">完整文档</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">
                    详细的使用指南、API 参考和最佳实践，助你快速掌握框架。
                </p>
                <a
                    href="<?php echo xun_docs_get_page_url('getting-started'); ?>"
                    class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
                >
                    阅读文档
                    <?php echo xun_docs_get_icon('arrow-right', 'outline', 'w-4 h-4 ml-1'); ?>
                </a>
            </div>

            <!-- 联系支持 -->
            <div class="text-center p-8 bg-white dark:bg-gray-900 rounded-2xl border border-gray-200 dark:border-gray-700 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                <div class="w-16 h-16 bg-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6">
                    <?php echo xun_docs_get_icon('envelope', 'outline', 'w-8 h-8 text-white'); ?>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-4">技术支持</h3>
                <p class="text-gray-600 dark:text-gray-400 mb-6">
                    遇到问题？需要帮助？随时联系我们获取专业的技术支持。
                </p>
                <a
                    href="mailto:<?php echo xun_docs_esc_attr(xun_docs_get_config('social_links')['email'] ?? ''); ?>"
                    class="inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
                >
                    联系我们
                    <?php echo xun_docs_get_icon('envelope', 'outline', 'w-4 h-4 ml-1'); ?>
                </a>
            </div>
        </div>
    </div>
</div>
