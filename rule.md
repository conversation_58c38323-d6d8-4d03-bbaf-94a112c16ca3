# WordPress无头主题开发规划

## 项目概述

开发一个WordPress无头主题系统，分为服务端和客户端两个版本：
- **服务端版本**：包含授权系统 + 页面构建器 + 基础功能
- **客户端版本**：包含页面构建器构建的内容渲染 + 基础功能

## 技术栈

### 后端技术
- **WordPress** 6.8.1+
- **PHP** 8.1+
- **WPGraphQL插件** - GraphQL API支持
- **自定义插件开发** - 核心功能实现

### 前端技术
- **Next.js** 14+ (App Router)
- **HeroUI** - UI组件库
- **Tailwind CSS** 4.x - 样式框架
- **TypeScript** - 类型安全
- **React** 18+ - 前端框架

### 页面构建器技术
- **@dnd-kit/core** - 拖拽引擎
- **Zustand** - 状态管理
- **React Query** - 数据获取和缓存
- **Monaco Editor** - 代码编辑器

## 项目架构

### 目录结构
```
wordpress-headless-theme/
├── server/                          # 服务端版本
│   ├── wordpress-plugin/            # WordPress插件
│   │   ├── includes/
│   │   │   ├── class-auth-system.php
│   │   │   ├── class-page-builder.php
│   │   │   ├── class-graphql-extensions.php
│   │   │   └── class-data-manager.php
│   │   ├── admin/
│   │   │   ├── settings/
│   │   │   └── dashboard/
│   │   └── plugin.php
│   └── frontend/                    # 服务端前端
│       ├── src/
│       │   ├── app/
│       │   │   ├── builder/         # 页面构建器
│       │   │   ├── dashboard/       # 管理面板
│       │   │   └── auth/           # 授权管理
│       │   ├── components/
│       │   │   ├── builder/        # 构建器组件
│       │   │   ├── ui/             # UI组件
│       │   │   └── forms/          # 表单组件
│       │   └── lib/
│       │       ├── api/            # API客户端
│       │       ├── auth/           # 授权逻辑
│       │       └── utils/          # 工具函数
│       ├── package.json
│       └── next.config.js
├── client/                          # 客户端版本
│   ├── wordpress-plugin/            # 客户端插件
│   │   ├── includes/
│   │   │   ├── class-renderer.php
│   │   │   ├── class-import-export.php
│   │   │   └── class-data-manager.php
│   │   └── plugin.php
│   └── frontend/                    # 客户端前端
│       ├── src/
│       │   ├── app/
│       │   │   ├── (pages)/        # 动态页面
│       │   │   └── api/            # API路由
│       │   ├── components/
│       │   │   ├── render/         # 渲染组件
│       │   │   └── ui/             # UI组件
│       │   └── lib/
│       │       ├── renderer/       # 渲染引擎
│       │       └── utils/          # 工具函数
│       ├── package.json
│       └── next.config.js
└── shared/                          # 共享代码
    ├── components/                  # 共享组件库
    │   ├── base/                   # 基础组件
    │   ├── layout/                 # 布局组件
    │   ├── media/                  # 媒体组件
    │   └── advanced/               # 高级组件
    ├── types/                      # TypeScript类型定义
    ├── utils/                      # 共享工具函数
    └── styles/                     # 共享样式
```

## 核心功能模块

### 1. WordPress插件开发

#### 1.1 服务端插件功能
```php
// 主要功能类
class ServerPlugin {
    // 授权系统 (服务端独有)
    private $auth_system;
    
    // 页面构建器数据管理
    private $page_builder;
    
    // GraphQL扩展
    private $graphql_extensions;
    
    // 网站设置管理
    private $site_settings;
    
    // 用户管理
    private $user_manager;
    
    // 支付系统集成
    private $payment_system;
}
```

#### 1.2 客户端插件功能
```php
// 主要功能类
class ClientPlugin {
    // 页面渲染引擎
    private $renderer;
    
    // 数据导入导出
    private $import_export;
    
    // 缓存管理
    private $cache_manager;
    
    // 网站设置管理
    private $site_settings;
}
```

### 2. 数据库设计

#### 2.1 利用WordPress原生表
```sql
-- 页面构建器数据存储
-- 使用 wp_posts 表
post_type = 'page_builder_page'
post_content = JSON格式的页面配置
post_status = 'publish' | 'draft' | 'private'

-- 使用 wp_postmeta 表存储扩展配置
meta_key = '_page_builder_config'     -- 页面配置
meta_key = '_page_builder_components' -- 组件配置
meta_key = '_page_builder_styles'     -- 样式配置
meta_key = '_page_builder_settings'   -- 页面设置

-- 使用 wp_options 表存储全局配置
option_name = 'page_builder_global_settings'  -- 全局设置
option_name = 'page_builder_component_library' -- 组件库配置
option_name = 'page_builder_theme_settings'   -- 主题设置

-- 使用 wp_usermeta 表存储用户配置
meta_key = '_page_builder_preferences'  -- 用户偏好设置
meta_key = '_page_builder_permissions'  -- 用户权限配置
```

#### 2.2 最小化自定义表
```sql
-- 仅在必要时创建自定义表
CREATE TABLE wp_page_builder_templates (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    name varchar(255) NOT NULL,
    category varchar(100) NOT NULL,
    config longtext NOT NULL,
    preview_image varchar(255),
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id)
);

CREATE TABLE wp_page_builder_licenses (
    id bigint(20) NOT NULL AUTO_INCREMENT,
    license_key varchar(255) NOT NULL UNIQUE,
    user_id bigint(20) NOT NULL,
    status enum('active', 'expired', 'suspended') DEFAULT 'active',
    expires_at datetime,
    created_at datetime DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (user_id) REFERENCES wp_users(ID)
);
```

## 页面构建器详细设计

### 1. 构建器布局架构
```typescript
interface BuilderLayout {
  toolbar: {
    position: 'top';
    height: '64px';
    components: [
      'save-button',
      'preview-toggle',
      'device-selector',
      'undo-redo',
      'publish-options'
    ];
  };
  
  sidebar: {
    left: {
      width: '280px';
      panels: [
        'component-library',
        'page-structure',
        'asset-manager'
      ];
    };
    
    right: {
      width: '320px';
      panels: [
        'component-properties',
        'style-editor',
        'responsive-settings'
      ];
    };
  };
  
  canvas: {
    position: 'center';
    responsive: true;
    zoom: 'fit-to-screen' | 'custom';
    devices: ['desktop', 'tablet', 'mobile'];
  };
}
```

### 2. 组件系统架构
```typescript
// 基础组件接口
interface BaseComponent {
  id: string;
  type: string;
  name: string;
  category: ComponentCategory;
  icon: string;
  description: string;
  
  // 组件属性配置
  props: ComponentProps;
  
  // 样式配置
  styles: ComponentStyles;
  
  // 数据源配置
  dataSource?: DataSourceConfig;
  
  // 渲染方法
  render: (props: any, styles: any, data?: any) => ReactElement;
  
  // 配置面板
  configPanel: ConfigPanelDefinition;
}

// 组件分类
enum ComponentCategory {
  BASIC = 'basic',        // 基础组件
  LAYOUT = 'layout',      // 布局组件
  MEDIA = 'media',        // 媒体组件
  FORM = 'form',          // 表单组件
  ADVANCED = 'advanced',  // 高级组件
  CUSTOM = 'custom'       // 自定义组件
}
```

### 3. 组件库详细规划

#### 3.1 基础组件 (Basic Components)
```typescript
const BasicComponents = {
  // 文本组件
  text: {
    name: '文本',
    props: {
      content: { type: 'richtext', default: '请输入文本内容' },
      tag: { type: 'select', options: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'span'] },
      alignment: { type: 'select', options: ['left', 'center', 'right', 'justify'] }
    },
    styles: {
      fontSize: { type: 'slider', min: 12, max: 72, unit: 'px' },
      fontWeight: { type: 'select', options: ['normal', 'bold', '100', '200', '300', '400', '500', '600', '700', '800', '900'] },
      color: { type: 'color' },
      lineHeight: { type: 'slider', min: 1, max: 3, step: 0.1 },
      letterSpacing: { type: 'slider', min: -2, max: 10, unit: 'px' }
    }
  },
  
  // 图片组件
  image: {
    name: '图片',
    props: {
      src: { type: 'media', accept: 'image/*' },
      alt: { type: 'text', default: '' },
      caption: { type: 'text', default: '' },
      link: { type: 'url', default: '' },
      openInNewTab: { type: 'boolean', default: false }
    },
    styles: {
      width: { type: 'dimension' },
      height: { type: 'dimension' },
      objectFit: { type: 'select', options: ['cover', 'contain', 'fill', 'none', 'scale-down'] },
      borderRadius: { type: 'dimension' },
      filter: { type: 'filter-editor' }
    }
  },
  
  // 按钮组件
  button: {
    name: '按钮',
    props: {
      text: { type: 'text', default: '点击按钮' },
      link: { type: 'url', default: '' },
      openInNewTab: { type: 'boolean', default: false },
      size: { type: 'select', options: ['small', 'medium', 'large'] },
      variant: { type: 'select', options: ['primary', 'secondary', 'outline', 'ghost'] },
      icon: { type: 'icon-picker', default: null },
      iconPosition: { type: 'select', options: ['left', 'right'] }
    }
  }
};
```

#### 3.2 布局组件 (Layout Components)
```typescript
const LayoutComponents = {
  // 容器组件
  container: {
    name: '容器',
    props: {
      maxWidth: { type: 'dimension', default: '1200px' },
      padding: { type: 'spacing' },
      margin: { type: 'spacing' },
      centerContent: { type: 'boolean', default: true }
    },
    children: true, // 支持嵌套子组件
    droppable: true
  },
  
  // 栅格系统
  grid: {
    name: '栅格',
    props: {
      columns: { type: 'number', min: 1, max: 12, default: 2 },
      gap: { type: 'dimension', default: '1rem' },
      responsive: {
        type: 'responsive-grid',
        breakpoints: {
          mobile: { columns: 1 },
          tablet: { columns: 2 },
          desktop: { columns: 3 }
        }
      }
    },
    children: true,
    droppable: true
  },
  
  // 弹性布局
  flexbox: {
    name: '弹性布局',
    props: {
      direction: { type: 'select', options: ['row', 'column'] },
      justifyContent: { type: 'select', options: ['flex-start', 'center', 'flex-end', 'space-between', 'space-around', 'space-evenly'] },
      alignItems: { type: 'select', options: ['flex-start', 'center', 'flex-end', 'stretch'] },
      wrap: { type: 'select', options: ['nowrap', 'wrap', 'wrap-reverse'] },
      gap: { type: 'dimension', default: '1rem' }
    },
    children: true,
    droppable: true
  }
};
```

#### 3.3 媒体组件 (Media Components)
```typescript
const MediaComponents = {
  // 轮播图组件
  carousel: {
    name: '轮播图',
    props: {
      images: { 
        type: 'media-gallery', 
        accept: 'image/*',
        multiple: true,
        fields: {
          title: { type: 'text' },
          description: { type: 'textarea' },
          link: { type: 'url' },
          openInNewTab: { type: 'boolean' }
        }
      },
      autoplay: { type: 'boolean', default: true },
      interval: { type: 'number', min: 1000, max: 10000, default: 3000, unit: 'ms' },
      showDots: { type: 'boolean', default: true },
      showArrows: { type: 'boolean', default: true },
      infinite: { type: 'boolean', default: true },
      effect: { type: 'select', options: ['slide', 'fade', 'cube', 'flip'] }
    },
    styles: {
      height: { type: 'dimension', default: '400px' },
      borderRadius: { type: 'dimension' }
    }
  },
  
  // 视频组件
  video: {
    name: '视频',
    props: {
      source: { 
        type: 'video-source',
        options: {
          upload: { type: 'media', accept: 'video/*' },
          youtube: { type: 'url', placeholder: 'YouTube视频链接' },
          vimeo: { type: 'url', placeholder: 'Vimeo视频链接' },
          embed: { type: 'textarea', placeholder: '嵌入代码' }
        }
      },
      poster: { type: 'media', accept: 'image/*' },
      autoplay: { type: 'boolean', default: false },
      muted: { type: 'boolean', default: false },
      loop: { type: 'boolean', default: false },
      controls: { type: 'boolean', default: true }
    }
  },
  
  // 画廊组件
  gallery: {
    name: '画廊',
    props: {
      images: { type: 'media-gallery', accept: 'image/*', multiple: true },
      layout: { type: 'select', options: ['grid', 'masonry', 'slider'] },
      columns: { type: 'responsive-number', default: { desktop: 3, tablet: 2, mobile: 1 } },
      gap: { type: 'dimension', default: '1rem' },
      lightbox: { type: 'boolean', default: true },
      captions: { type: 'boolean', default: false }
    }
  }
};
```

#### 3.4 高级组件 (Advanced Components)
```typescript
const AdvancedComponents = {
  // 文章列表组件 - 预览时显示模拟数据，发布后显示真实WordPress数据
  postList: {
    name: '文章列表',
    dataSource: {
      type: 'posts', // 客户端会根据此配置获取WordPress文章
      query: {
        postType: { type: 'select', options: ['post', 'page', 'custom'], default: 'post' },
        category: { type: 'category-select', multiple: true, placeholder: '选择分类' },
        tags: { type: 'tag-select', multiple: true, placeholder: '选择标签' },
        author: { type: 'author-select', multiple: true, placeholder: '选择作者' },
        orderBy: { type: 'select', options: ['date', 'title', 'menu_order', 'rand'], default: 'date' },
        order: { type: 'select', options: ['ASC', 'DESC'], default: 'DESC' },
        limit: { type: 'number', min: 1, max: 50, default: 10 },
        offset: { type: 'number', min: 0, default: 0 }
      }
    },
    props: {
      layout: { type: 'select', options: ['list', 'grid', 'card'], default: 'grid' },
      columns: { type: 'responsive-number', default: { desktop: 3, tablet: 2, mobile: 1 } },
      showFeaturedImage: { type: 'boolean', default: true },
      showTitle: { type: 'boolean', default: true },
      showExcerpt: { type: 'boolean', default: true },
      showDate: { type: 'boolean', default: true },
      showAuthor: { type: 'boolean', default: true },
      showCategories: { type: 'boolean', default: true },
      showReadMore: { type: 'boolean', default: true },
      readMoreText: { type: 'text', default: '阅读更多' },
      excerptLength: { type: 'number', min: 10, max: 500, default: 150 },
      pagination: { type: 'boolean', default: false },
      noPostsMessage: { type: 'text', default: '暂无文章' }
    },
    // 预览时的渲染逻辑
    renderPreview: (props, data) => {
      const mockPosts = data || []; // 使用模拟数据
      return (
        <div className={`post-list layout-${props.layout}`}>
          {mockPosts.map(post => (
            <article key={post.id} className="post-item">
              {props.showFeaturedImage && post.featuredImage && (
                <img src={post.featuredImage.sourceUrl} alt={post.title} />
              )}
              <div className="post-content">
                {props.showTitle && <h3>{post.title}</h3>}
                {props.showDate && <time>{post.date}</time>}
                {props.showAuthor && <span>by {post.author.name}</span>}
                {props.showExcerpt && <p>{post.excerpt}</p>}
                {props.showReadMore && (
                  <a href="#" className="read-more">{props.readMoreText}</a>
                )}
              </div>
            </article>
          ))}
        </div>
      );
    }
  },
  
  // 表单组件 - 纯布局组件，不涉及数据获取
  form: {
    name: '表单',
    props: {
      fields: {
        type: 'form-builder',
        fieldTypes: {
          text: { name: '文本输入', validation: ['required', 'minLength', 'maxLength', 'pattern'] },
          email: { name: '邮箱', validation: ['required', 'email'] },
          tel: { name: '电话', validation: ['required', 'tel'] },
          textarea: { name: '多行文本', validation: ['required', 'minLength', 'maxLength'] },
          select: { name: '下拉选择', validation: ['required'] },
          checkbox: { name: '复选框', validation: ['required'] },
          radio: { name: '单选按钮', validation: ['required'] },
          file: { name: '文件上传', validation: ['required', 'fileType', 'fileSize'] }
        }
      },
      submitAction: {
        type: 'select',
        options: ['email', 'database', 'webhook', 'redirect']
      },
      submitText: { type: 'text', default: '提交' },
      successMessage: { type: 'textarea', default: '表单提交成功！' },
      errorMessage: { type: 'textarea', default: '表单提交失败，请重试。' },
      formId: { type: 'text', placeholder: '表单唯一标识' }
    },
    // 预览时显示表单布局
    renderPreview: (props) => {
      return (
        <form className="preview-form">
          {props.fields.map((field, index) => (
            <div key={index} className="form-field">
              <label>{field.label}</label>
              {field.type === 'textarea' ? (
                <textarea placeholder={field.placeholder} disabled />
              ) : (
                <input
                  type={field.type}
                  placeholder={field.placeholder}
                  disabled
                />
              )}
            </div>
          ))}
          <button type="button" disabled>{props.submitText}</button>
        </form>
      );
    }
  },

  // 搜索组件 - 布局组件，客户端实现搜索功能
  search: {
    name: '搜索',
    props: {
      placeholder: { type: 'text', default: '搜索...' },
      searchIn: {
        type: 'checkbox-group',
        options: [
          { value: 'posts', label: '文章' },
          { value: 'pages', label: '页面' },
          { value: 'products', label: '产品' },
          { value: 'custom', label: '自定义内容' }
        ],
        default: ['posts']
      },
      showCategories: { type: 'boolean', default: false },
      showFilters: { type: 'boolean', default: false },
      resultsPerPage: { type: 'number', min: 5, max: 50, default: 10 },
      showResultCount: { type: 'boolean', default: true },
      highlightKeywords: { type: 'boolean', default: true },
      buttonText: { type: 'text', default: '搜索' }
    },
    // 预览时显示搜索框布局
    renderPreview: (props) => {
      return (
        <div className="search-component">
          <div className="search-form">
            <input
              type="text"
              placeholder={props.placeholder}
              disabled
            />
            <button type="button" disabled>{props.buttonText}</button>
          </div>
          {props.showCategories && (
            <div className="search-categories">
              <span>分类筛选:</span>
              <select disabled>
                <option>所有分类</option>
                <option>技术</option>
                <option>设计</option>
              </select>
            </div>
          )}
          <div className="search-results-preview">
            <p>搜索结果将在这里显示</p>
          </div>
        </div>
      );
    }
  },

  // 菜单导航组件 - 客户端根据WordPress菜单渲染
  navigation: {
    name: '导航菜单',
    dataSource: {
      type: 'menu', // 客户端获取WordPress菜单
      query: {
        menuLocation: { type: 'select', options: ['primary', 'secondary', 'footer'], default: 'primary' },
        menuId: { type: 'number', placeholder: '菜单ID（可选）' }
      }
    },
    props: {
      layout: { type: 'select', options: ['horizontal', 'vertical', 'dropdown'], default: 'horizontal' },
      showSubmenus: { type: 'boolean', default: true },
      mobileBreakpoint: { type: 'select', options: ['768px', '1024px', '1200px'], default: '768px' },
      hamburgerIcon: { type: 'boolean', default: true }
    },
    // 预览时显示模拟菜单
    renderPreview: (props) => {
      const mockMenuItems = [
        { id: 1, title: '首页', url: '#', children: [] },
        {
          id: 2,
          title: '产品',
          url: '#',
          children: [
            { id: 21, title: '产品A', url: '#' },
            { id: 22, title: '产品B', url: '#' }
          ]
        },
        { id: 3, title: '关于我们', url: '#', children: [] },
        { id: 4, title: '联系我们', url: '#', children: [] }
      ];

      return (
        <nav className={`navigation layout-${props.layout}`}>
          <ul className="menu">
            {mockMenuItems.map(item => (
              <li key={item.id} className="menu-item">
                <a href={item.url}>{item.title}</a>
                {props.showSubmenus && item.children.length > 0 && (
                  <ul className="submenu">
                    {item.children.map(child => (
                      <li key={child.id}>
                        <a href={child.url}>{child.title}</a>
                      </li>
                    ))}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </nav>
      );
    }
  }
};
```

## 实时预览系统

### 1. 预览数据管理
```typescript
// 预览数据管理器 - 提供模拟数据用于布局预览
class PreviewDataManager {
  private mockData: Map<string, any>;

  constructor() {
    this.mockData = new Map();
    this.initializeMockData();
  }

  // 初始化模拟数据
  private initializeMockData() {
    // 文章列表模拟数据
    this.mockData.set('posts', [
      {
        id: 1,
        title: '示例文章标题一',
        excerpt: '这是一篇示例文章的摘要内容，用于展示文章列表组件的布局效果...',
        date: '2024-01-15',
        author: { name: '作者姓名', avatar: '/mock/avatar1.jpg' },
        featuredImage: { sourceUrl: '/mock/post1.jpg', altText: '示例图片' },
        categories: [{ name: '技术', slug: 'tech' }]
      },
      {
        id: 2,
        title: '示例文章标题二',
        excerpt: '这是另一篇示例文章的摘要内容，展示不同长度的文本效果...',
        date: '2024-01-14',
        author: { name: '编辑姓名', avatar: '/mock/avatar2.jpg' },
        featuredImage: { sourceUrl: '/mock/post2.jpg', altText: '示例图片' },
        categories: [{ name: '设计', slug: 'design' }]
      },
      {
        id: 3,
        title: '示例文章标题三',
        excerpt: '第三篇示例文章，用于展示更多的布局可能性和样式效果...',
        date: '2024-01-13',
        author: { name: '管理员', avatar: '/mock/avatar3.jpg' },
        featuredImage: { sourceUrl: '/mock/post3.jpg', altText: '示例图片' },
        categories: [{ name: '生活', slug: 'life' }]
      }
    ]);

    // 页面列表模拟数据
    this.mockData.set('pages', [
      {
        id: 1,
        title: '关于我们',
        excerpt: '了解我们的团队和使命',
        slug: 'about'
      },
      {
        id: 2,
        title: '联系方式',
        excerpt: '如何与我们取得联系',
        slug: 'contact'
      }
    ]);

    // 产品列表模拟数据
    this.mockData.set('products', [
      {
        id: 1,
        title: '示例产品一',
        price: '¥299',
        image: '/mock/product1.jpg',
        rating: 4.5
      },
      {
        id: 2,
        title: '示例产品二',
        price: '¥599',
        image: '/mock/product2.jpg',
        rating: 4.8
      }
    ]);

    // 轮播图模拟数据
    this.mockData.set('carousel', [
      {
        id: 1,
        title: '轮播图标题一',
        description: '这是第一张轮播图的描述文字',
        image: '/mock/slide1.jpg',
        link: '#'
      },
      {
        id: 2,
        title: '轮播图标题二',
        description: '这是第二张轮播图的描述文字',
        image: '/mock/slide2.jpg',
        link: '#'
      },
      {
        id: 3,
        title: '轮播图标题三',
        description: '这是第三张轮播图的描述文字',
        image: '/mock/slide3.jpg',
        link: '#'
      }
    ]);
  }

  // 根据组件配置获取对应的模拟数据
  getMockData(component: ComponentData): any {
    const { type, dataSource } = component;

    switch (type) {
      case 'postList':
        return this.getFilteredPosts(dataSource?.query || {});
      case 'pageList':
        return this.mockData.get('pages');
      case 'productList':
        return this.mockData.get('products');
      case 'carousel':
        return this.mockData.get('carousel');
      default:
        return null;
    }
  }

  private getFilteredPosts(query: any) {
    let posts = this.mockData.get('posts') || [];

    // 模拟分类过滤
    if (query.category) {
      posts = posts.filter((post: any) =>
        post.categories.some((cat: any) => cat.slug === query.category)
      );
    }

    // 模拟数量限制
    if (query.limit) {
      posts = posts.slice(0, query.limit);
    }

    // 模拟排序
    if (query.orderBy === 'title') {
      posts.sort((a: any, b: any) => a.title.localeCompare(b.title));
    }

    return posts;
  }
}

// 预览渲染引擎 - 专门用于构建器预览
class PreviewRenderEngine {
  private componentRegistry: ComponentRegistry;
  private styleManager: StyleManager;
  private previewDataManager: PreviewDataManager;

  constructor() {
    this.componentRegistry = new ComponentRegistry();
    this.styleManager = new StyleManager(true); // 预览模式
    this.previewDataManager = new PreviewDataManager();
  }

  render(pageData: PageData): ReactElement {
    // 注入全局样式
    this.styleManager.injectGlobalStyles(pageData.globalStyles);

    // 渲染组件树
    return this.renderComponentTree(pageData.components);
  }

  private renderComponentTree(components: ComponentData[]): ReactElement {
    return (
      <div className="preview-page-container">
        {components.map((component, index) => {
          const ComponentClass = this.componentRegistry.get(component.type);
          if (!ComponentClass) return null;

          // 获取模拟数据
          const mockData = this.previewDataManager.getMockData(component);

          return (
            <div
              key={component.id || index}
              data-component-id={component.id}
              data-component-type={component.type}
              className="preview-component-wrapper"
            >
              <ComponentClass
                {...component.props}
                styles={component.styles}
                data={mockData}
                isPreview={true}
              />
            </div>
          );
        })}
      </div>
    );
  }
}
```

### 2. 客户端渲染引擎
```typescript
// 客户端渲染引擎 - 用于渲染用户实际的WordPress数据
class ClientRenderEngine {
  private componentRegistry: ComponentRegistry;
  private styleManager: StyleManager;
  private wordpressDataManager: WordPressDataManager;

  constructor(wordpressApiUrl: string) {
    this.componentRegistry = new ComponentRegistry();
    this.styleManager = new StyleManager(false); // 生产模式
    this.wordpressDataManager = new WordPressDataManager(wordpressApiUrl);
  }

  async render(pageData: PageData): Promise<ReactElement> {
    // 注入全局样式
    this.styleManager.injectGlobalStyles(pageData.globalStyles);

    // 渲染组件树
    return await this.renderComponentTree(pageData.components);
  }

  private async renderComponentTree(components: ComponentData[]): Promise<ReactElement> {
    const renderedComponents = await Promise.all(
      components.map(async (component, index) => {
        const ComponentClass = this.componentRegistry.get(component.type);
        if (!ComponentClass) return null;

        // 获取真实WordPress数据
        const realData = await this.wordpressDataManager.fetchComponentData(component);

        return (
          <div
            key={component.id || index}
            data-component-id={component.id}
            data-component-type={component.type}
            className="client-component-wrapper"
          >
            <ComponentClass
              {...component.props}
              styles={component.styles}
              data={realData}
              isPreview={false}
            />
          </div>
        );
      })
    );

    return (
      <div className="client-page-container">
        {renderedComponents}
      </div>
    );
  }
}

// WordPress数据管理器 - 获取用户实际的WordPress数据
class WordPressDataManager {
  private cache: Map<string, CacheEntry>;
  private cacheTimeout: number = 300000; // 5分钟缓存
  private graphqlClient: GraphQLClient;

  constructor(apiUrl: string) {
    this.cache = new Map();
    this.graphqlClient = new GraphQLClient({
      endpoint: `${apiUrl}/graphql`
    });
  }

  async fetchComponentData(component: ComponentData): Promise<any> {
    if (!component.dataSource) return null;

    const cacheKey = this.generateCacheKey(component);

    // 检查缓存
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey);
      if (Date.now() - cached.timestamp < this.cacheTimeout) {
        return cached.data;
      }
    }

    // 获取真实数据
    const data = await this.fetchFromWordPress(component.dataSource);

    // 更新缓存
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });

    return data;
  }

  private async fetchFromWordPress(dataSource: DataSourceConfig): Promise<any> {
    const { type, query } = dataSource;

    switch (type) {
      case 'posts':
        return this.fetchPosts(query);
      case 'pages':
        return this.fetchPages(query);
      case 'custom-post-type':
        return this.fetchCustomPostType(query);
      default:
        return null;
    }
  }

  private async fetchPosts(query: any) {
    const graphqlQuery = `
      query GetPosts($first: Int, $where: RootQueryToPostConnectionWhereArgs) {
        posts(first: $first, where: $where) {
          nodes {
            id
            title
            excerpt
            date
            slug
            link
            featuredImage {
              node {
                sourceUrl
                altText
              }
            }
            author {
              node {
                name
                avatar {
                  url
                }
              }
            }
            categories {
              nodes {
                name
                slug
              }
            }
          }
        }
      }
    `;

    const variables = {
      first: query.limit || 10,
      where: {
        categoryName: query.category,
        orderby: {
          field: query.orderBy?.toUpperCase() || 'DATE',
          order: query.order || 'DESC'
        }
      }
    };

    const response = await this.graphqlClient.request(graphqlQuery, variables);
    return response.posts.nodes;
  }

  private generateCacheKey(component: ComponentData): string {
    return `${component.type}_${JSON.stringify(component.dataSource)}`;
  }
}
```

### 3. 样式同步系统
```typescript
// 样式管理器 - 确保预览和发布样式完全一致
class StyleManager {
  private styleSheet: HTMLStyleElement;
  private componentStyles: Map<string, string>;
  private globalStyles: GlobalStyles;
  private isPreview: boolean;
  
  constructor(isPreview: boolean = false) {
    this.componentStyles = new Map();
    this.isPreview = isPreview;
    this.initializeStyleSheet();
  }
  
  injectComponentStyles(componentId: string, styles: ComponentStyles): void {
    const cssText = this.generateComponentCSS(componentId, styles);
    this.componentStyles.set(componentId, cssText);
    this.updateStyleSheet();
  }
  
  injectGlobalStyles(globalStyles: GlobalStyles): void {
    this.globalStyles = globalStyles;
    this.updateStyleSheet();
  }
  
  private generateComponentCSS(componentId: string, styles: ComponentStyles): string {
    const selector = `[data-component-id="${componentId}"]`;
    let css = `${selector} {\n`;
    
    // 基础样式
    Object.entries(styles.base || {}).forEach(([property, value]) => {
      css += `  ${this.camelToKebab(property)}: ${value};\n`;
    });
    
    css += '}\n';
    
    // 响应式样式
    if (styles.responsive) {
      Object.entries(styles.responsive).forEach(([breakpoint, responsiveStyles]) => {
        const mediaQuery = this.getMediaQuery(breakpoint);
        css += `@media ${mediaQuery} {\n`;
        css += `  ${selector} {\n`;
        Object.entries(responsiveStyles).forEach(([property, value]) => {
          css += `    ${this.camelToKebab(property)}: ${value};\n`;
        });
        css += '  }\n';
        css += '}\n';
      });
    }
    
    // 伪类样式
    if (styles.pseudo) {
      Object.entries(styles.pseudo).forEach(([pseudoClass, pseudoStyles]) => {
        css += `${selector}:${pseudoClass} {\n`;
        Object.entries(pseudoStyles).forEach(([property, value]) => {
          css += `  ${this.camelToKebab(property)}: ${value};\n`;
        });
        css += '}\n';
      });
    }
    
    return css;
  }
  
  private getMediaQuery(breakpoint: string): string {
    const breakpoints = {
      mobile: '(max-width: 767px)',
      tablet: '(min-width: 768px) and (max-width: 1023px)',
      desktop: '(min-width: 1024px)',
      'mobile-up': '(min-width: 768px)',
      'tablet-up': '(min-width: 1024px)'
    };
    
    return breakpoints[breakpoint] || breakpoint;
  }
}
```

## 布局导出和发布系统

### 1. 布局数据处理
```typescript
// 布局导出管理器 - 只导出布局结构，不包含具体内容
class LayoutExportManager {
  private encryptionService: EncryptionService;

  constructor() {
    this.encryptionService = new EncryptionService();
  }

  // 清理页面数据，只保留布局相关信息
  private cleanPageDataForExport(pageData: PageData): LayoutData {
    return {
      version: '1.0',
      title: pageData.title,
      components: this.cleanComponents(pageData.components),
      globalStyles: pageData.globalStyles,
      settings: {
        responsive: pageData.settings.responsive,
        seo: pageData.settings.seo,
        performance: pageData.settings.performance
      },
      metadata: {
        generator: 'WordPress Page Builder',
        exportTime: Date.now(),
        builderVersion: process.env.NEXT_PUBLIC_BUILDER_VERSION
      }
    };
  }

  // 清理组件数据，移除预览相关的模拟数据
  private cleanComponents(components: ComponentData[]): LayoutComponentData[] {
    return components.map(component => ({
      id: component.id,
      type: component.type,
      props: this.cleanComponentProps(component.props),
      styles: component.styles,
      responsive: component.responsive,
      dataSource: component.dataSource, // 保留数据源配置，但不包含具体数据
      children: component.children ? this.cleanComponents(component.children) : undefined
    }));
  }

  // 清理组件属性，移除预览数据
  private cleanComponentProps(props: any): any {
    const cleanedProps = { ...props };

    // 移除预览相关的属性
    delete cleanedProps._previewData;
    delete cleanedProps._mockData;
    delete cleanedProps._tempId;

    return cleanedProps;
  }

  // 发布到WordPress站点
  async publishToWordPress(pageData: PageData, targetSite: string): Promise<PublishResult> {
    try {
      // 1. 清理数据，只保留布局信息
      const layoutData = this.cleanPageDataForExport(pageData);

      // 2. 验证目标站点
      await this.validateTargetSite(targetSite);

      // 3. 加密布局数据
      const encryptedLayout = this.encryptionService.encrypt(layoutData);

      // 4. 发送到目标站点
      const response = await fetch(`${targetSite}/wp-json/page-builder/v1/import`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-Page-Builder-Auth': await this.generateAuthToken(targetSite)
        },
        body: JSON.stringify({
          layoutData: encryptedLayout,
          metadata: {
            sourceUrl: window.location.origin,
            timestamp: Date.now()
          }
        })
      });

      if (!response.ok) {
        throw new Error(`发布失败: ${response.statusText}`);
      }

      const result = await response.json();

      return {
        success: true,
        pageId: result.pageId,
        url: result.url,
        message: '布局发布成功'
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  // 导出加密的布局JSON
  async exportEncryptedJSON(pageData: PageData): Promise<string> {
    const layoutData = this.cleanPageDataForExport(pageData);

    const exportPackage = {
      type: 'page-builder-layout',
      version: '1.0',
      layout: layoutData,
      checksum: this.generateChecksum(layoutData)
    };

    return this.encryptionService.encrypt(exportPackage);
  }

  // 生成布局文件下载
  downloadLayoutFile(pageData: PageData, filename?: string): void {
    const layoutJson = this.exportEncryptedJSON(pageData);
    const blob = new Blob([layoutJson], { type: 'application/json' });
    const url = URL.createObjectURL(blob);

    const link = document.createElement('a');
    link.href = url;
    link.download = filename || `layout-${Date.now()}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  private async validateTargetSite(siteUrl: string): Promise<boolean> {
    try {
      const response = await fetch(`${siteUrl}/wp-json/page-builder/v1/status`);
      const status = await response.json();

      if (!status.active) {
        throw new Error('目标站点未安装或未激活页面构建器插件');
      }

      return true;
    } catch (error) {
      throw new Error(`无法连接到目标站点: ${error.message}`);
    }
  }

  private generateChecksum(data: any): string {
    return btoa(JSON.stringify(data)).slice(0, 16);
  }

  private async generateAuthToken(siteUrl: string): Promise<string> {
    // 生成认证令牌的逻辑
    const timestamp = Date.now();
    const nonce = Math.random().toString(36).substring(7);
    return btoa(`${timestamp}:${nonce}`);
  }
}
```

### 2. 客户端布局导入系统
```php
// 客户端布局导入管理器
class LayoutImportManager {
    private $encryption_service;
    private $layout_validator;
    private $layout_renderer;

    public function __construct() {
        $this->encryption_service = new EncryptionService();
        $this->layout_validator = new LayoutValidator();
        $this->layout_renderer = new LayoutRenderer();
    }

    // 导入加密的布局JSON
    public function import_encrypted_layout($encrypted_data) {
        try {
            // 1. 解密布局数据
            $decrypted_package = $this->encryption_service->decrypt($encrypted_data);

            // 2. 验证数据包格式
            if (!$this->validate_layout_package($decrypted_package)) {
                throw new Exception('布局数据包格式无效');
            }

            $layout_data = $decrypted_package['layout'];

            // 3. 验证布局数据
            if (!$this->layout_validator->validate($layout_data)) {
                throw new Exception('布局数据验证失败');
            }

            // 4. 存储布局到数据库
            $layout_id = $this->store_layout_data($layout_data);

            // 5. 清理缓存
            $this->clear_layout_cache();

            return array(
                'success' => true,
                'layout_id' => $layout_id,
                'message' => '布局导入成功'
            );
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }

    // 接收服务端发布的布局
    public function receive_published_layout($request_data) {
        try {
            // 1. 验证请求来源
            if (!$this->validate_publish_request($request_data)) {
                throw new Exception('无效的发布请求');
            }

            // 2. 解密布局数据
            $layout_data = $this->encryption_service->decrypt($request_data['layoutData']);

            // 3. 验证布局
            if (!$this->layout_validator->validate($layout_data)) {
                throw new Exception('布局验证失败');
            }

            // 4. 存储布局
            $layout_id = $this->store_layout_data($layout_data);

            // 5. 生成页面URL
            $page_url = $this->generate_page_url($layout_id);

            return array(
                'success' => true,
                'pageId' => $layout_id,
                'url' => $page_url,
                'message' => '布局接收成功'
            );
        } catch (Exception $e) {
            return array(
                'success' => false,
                'error' => $e->getMessage()
            );
        }
    }

    private function store_layout_data($layout_data) {
        // 存储布局到 wp_posts
        $post_id = wp_insert_post(array(
            'post_type' => 'page_builder_layout',
            'post_title' => $layout_data['title'],
            'post_content' => '', // 内容由布局动态生成
            'post_status' => 'publish',
            'meta_input' => array(
                '_layout_data' => json_encode($layout_data),
                '_layout_version' => $layout_data['version'],
                '_layout_components' => json_encode($layout_data['components']),
                '_layout_styles' => json_encode($layout_data['globalStyles']),
                '_layout_settings' => json_encode($layout_data['settings'])
            )
        ));

        return $post_id;
    }

    private function validate_layout_package($package) {
        return isset($package['type']) &&
               $package['type'] === 'page-builder-layout' &&
               isset($package['layout']) &&
               isset($package['version']);
    }

    private function validate_publish_request($request_data) {
        // 验证请求头和认证信息
        return isset($request_data['layoutData']) &&
               isset($request_data['metadata']);
    }

    private function generate_page_url($layout_id) {
        return home_url("/layout/{$layout_id}");
    }

    private function clear_layout_cache() {
        // 清理布局相关缓存
        wp_cache_delete_group('page_builder_layouts');
    }
}

// 布局渲染器 - 将布局配置转换为实际页面
class LayoutRenderer {
    private $component_registry;
    private $data_fetcher;

    public function __construct() {
        $this->component_registry = new ComponentRegistry();
        $this->data_fetcher = new WordPressDataFetcher();
    }

    // 渲染布局为HTML
    public function render_layout($layout_id) {
        $layout_data = $this->get_layout_data($layout_id);

        if (!$layout_data) {
            return '<p>布局不存在</p>';
        }

        // 注入全局样式
        $this->inject_global_styles($layout_data['globalStyles']);

        // 渲染组件树
        return $this->render_components($layout_data['components']);
    }

    private function render_components($components) {
        $html = '<div class="layout-container">';

        foreach ($components as $component) {
            $html .= $this->render_single_component($component);
        }

        $html .= '</div>';
        return $html;
    }

    private function render_single_component($component) {
        $component_class = $this->component_registry->get($component['type']);

        if (!$component_class) {
            return "<!-- 未知组件类型: {$component['type']} -->";
        }

        // 获取真实WordPress数据
        $real_data = null;
        if (isset($component['dataSource'])) {
            $real_data = $this->data_fetcher->fetch($component['dataSource']);
        }

        // 渲染组件
        return $component_class->render($component['props'], $component['styles'], $real_data);
    }

    private function get_layout_data($layout_id) {
        $layout_json = get_post_meta($layout_id, '_layout_data', true);
        return $layout_json ? json_decode($layout_json, true) : null;
    }

    private function inject_global_styles($global_styles) {
        if (empty($global_styles)) return;

        $css = '<style id="layout-global-styles">';
        foreach ($global_styles as $selector => $styles) {
            $css .= "{$selector} {";
            foreach ($styles as $property => $value) {
                $css .= "{$property}: {$value};";
            }
            $css .= "}";
        }
        $css .= '</style>';

        // 添加到页面头部
        add_action('wp_head', function() use ($css) {
            echo $css;
        });
    }
}
```

## 授权系统 (服务端独有)

### 1. 许可证管理
```php
// 许可证管理系统
class LicenseManager {
    private $license_server_url;
    private $encryption_key;
    
    public function __construct() {
        $this->license_server_url = get_option('license_server_url');
        $this->encryption_key = get_option('license_encryption_key');
    }
    
    public function validate_license($license_key) {
        // 1. 本地验证
        $local_validation = $this->validate_local_license($license_key);
        if (!$local_validation['valid']) {
            return $local_validation;
        }
        
        // 2. 远程验证
        $remote_validation = $this->validate_remote_license($license_key);
        
        // 3. 更新本地缓存
        if ($remote_validation['valid']) {
            $this->update_license_cache($license_key, $remote_validation['data']);
        }
        
        return $remote_validation;
    }
    
    public function check_feature_access($feature, $license_key) {
        $license_data = $this->get_license_data($license_key);
        
        if (!$license_data || !$license_data['valid']) {
            return false;
        }
        
        $allowed_features = $license_data['features'];
        return in_array($feature, $allowed_features);
    }
    
    private function validate_remote_license($license_key) {
        $response = wp_remote_post($this->license_server_url . '/validate', array(
            'body' => array(
                'license_key' => $license_key,
                'domain' => get_site_url(),
                'version' => get_option('page_builder_version')
            ),
            'timeout' => 30
        ));
        
        if (is_wp_error($response)) {
            return array('valid' => false, 'error' => '无法连接到许可证服务器');
        }
        
        $body = wp_remote_retrieve_body($response);
        return json_decode($body, true);
    }
}
```

### 2. 用户权限管理
```php
// 用户权限管理
class PermissionManager {
    private $capabilities = array(
        'page_builder_access' => '访问页面构建器',
        'page_builder_create' => '创建页面',
        'page_builder_edit' => '编辑页面',
        'page_builder_delete' => '删除页面',
        'page_builder_publish' => '发布页面',
        'page_builder_export' => '导出页面',
        'page_builder_manage_templates' => '管理模板',
        'page_builder_manage_settings' => '管理设置'
    );
    
    public function __construct() {
        add_action('init', array($this, 'add_capabilities'));
        add_filter('user_has_cap', array($this, 'check_license_capabilities'), 10, 3);
    }
    
    public function add_capabilities() {
        $role = get_role('administrator');
        foreach ($this->capabilities as $cap => $description) {
            $role->add_cap($cap);
        }
    }
    
    public function check_license_capabilities($allcaps, $caps, $args) {
        $license_manager = new LicenseManager();
        $license_key = get_option('page_builder_license_key');
        
        foreach ($caps as $cap) {
            if (array_key_exists($cap, $this->capabilities)) {
                if (!$license_manager->check_feature_access($cap, $license_key)) {
                    $allcaps[$cap] = false;
                }
            }
        }
        
        return $allcaps;
    }
}
```

## 性能优化策略

### 1. 前端性能优化
```typescript
// 组件懒加载
const LazyComponent = React.lazy(() => import('./Component'));

// 虚拟滚动
import { FixedSizeList as List } from 'react-window';

// 图片懒加载
import { LazyLoadImage } from 'react-lazy-load-image-component';

// 代码分割
const BuilderPage = React.lazy(() => import('./pages/BuilderPage'));
const PreviewPage = React.lazy(() => import('./pages/PreviewPage'));
```

### 2. 缓存策略
```php
// WordPress缓存管理
class CacheManager {
    private $cache_group = 'page_builder';
    private $cache_expiration = 3600; // 1小时
    
    public function get_page_data($page_id) {
        $cache_key = "page_data_{$page_id}";
        $cached_data = wp_cache_get($cache_key, $this->cache_group);
        
        if ($cached_data !== false) {
            return $cached_data;
        }
        
        $page_data = $this->fetch_page_data($page_id);
        wp_cache_set($cache_key, $page_data, $this->cache_group, $this->cache_expiration);
        
        return $page_data;
    }
    
    public function clear_page_cache($page_id) {
        $cache_key = "page_data_{$page_id}";
        wp_cache_delete($cache_key, $this->cache_group);
    }
}
```

## 开发阶段规划

### 第一阶段：基础架构 (3-4周)
1. **WordPress插件框架搭建**
   - 创建服务端和客户端插件基础结构
   - 集成WPGraphQL插件
   - 设计数据库schema
   - 实现基础API接口

2. **Next.js项目初始化**
   - 服务端和客户端项目脚手架
   - HeroUI + Tailwind 4配置
   - TypeScript配置
   - 基础路由和布局

3. **共享组件库开发**
   - 定义组件标准接口
   - 开发基础组件 (文本、图片、按钮)
   - 实现组件注册机制
   - 组件配置schema设计

### 第二阶段：页面构建器核心 (4-5周)
1. **拖拽引擎集成**
   - @dnd-kit/core集成
   - 拖拽区域设计
   - 组件拖拽逻辑
   - 拖拽预览效果

2. **构建器界面开发**
   - 工具栏组件
   - 组件面板
   - 属性配置面板
   - 画布区域

3. **实时预览系统**
   - 统一渲染引擎
   - 样式同步机制
   - 数据实时获取
   - 响应式预览

### 第三阶段：高级组件和功能 (3-4周)
1. **高级组件开发**
   - 布局组件 (容器、栅格、弹性布局)
   - 媒体组件 (轮播图、视频、画廊)
   - 数据组件 (文章列表、搜索)
   - 表单组件

2. **数据源集成**
   - WordPress数据源
   - 自定义数据源
   - 数据缓存机制
   - 实时数据同步

### 第四阶段：发布和部署系统 (3-4周)
1. **发布功能开发**
   - 发布到WordPress API
   - 数据加密传输
   - 导入导出功能
   - 版本管理

2. **客户端渲染引擎**
   - JSON解析器
   - 组件渲染器
   - 样式生成器
   - 性能优化

### 第五阶段：授权和安全 (2-3周)
1. **授权系统开发 (服务端)**
   - 许可证验证
   - 用户权限管理
   - 功能访问控制
   - 安全加密

2. **安全性增强**
   - 数据验证
   - XSS防护
   - CSRF保护
   - 输入过滤

### 第六阶段：测试和优化 (2-3周)
1. **功能测试**
   - 单元测试
   - 集成测试
   - 端到端测试
   - 性能测试

2. **用户体验优化**
   - 界面优化
   - 交互改进
   - 错误处理
   - 用户反馈

## 技术实现细节

### 1. GraphQL Schema扩展
```php
// 扩展WPGraphQL Schema
class PageBuilderGraphQLExtensions {
    public function __construct() {
        add_action('graphql_register_types', array($this, 'register_types'));
        add_action('graphql_register_fields', array($this, 'register_fields'));
    }

    public function register_types() {
        // 注册页面构建器类型
        register_graphql_object_type('PageBuilderPage', array(
            'description' => '页面构建器页面',
            'fields' => array(
                'id' => array('type' => 'ID'),
                'title' => array('type' => 'String'),
                'components' => array('type' => array('list_of' => 'PageBuilderComponent')),
                'styles' => array('type' => 'String'),
                'settings' => array('type' => 'String'),
                'createdAt' => array('type' => 'String'),
                'updatedAt' => array('type' => 'String')
            )
        ));

        // 注册组件类型
        register_graphql_object_type('PageBuilderComponent', array(
            'description' => '页面构建器组件',
            'fields' => array(
                'id' => array('type' => 'String'),
                'type' => array('type' => 'String'),
                'props' => array('type' => 'String'),
                'styles' => array('type' => 'String'),
                'children' => array('type' => array('list_of' => 'PageBuilderComponent'))
            )
        ));
    }

    public function register_fields() {
        // 在RootQuery中添加页面构建器查询
        register_graphql_field('RootQuery', 'pageBuilderPages', array(
            'type' => array('list_of' => 'PageBuilderPage'),
            'description' => '获取页面构建器页面列表',
            'args' => array(
                'first' => array('type' => 'Int'),
                'after' => array('type' => 'String'),
                'where' => array('type' => 'PageBuilderPageWhereArgs')
            ),
            'resolve' => array($this, 'resolve_page_builder_pages')
        ));

        register_graphql_field('RootQuery', 'pageBuilderPage', array(
            'type' => 'PageBuilderPage',
            'description' => '获取单个页面构建器页面',
            'args' => array(
                'id' => array('type' => 'ID'),
                'slug' => array('type' => 'String')
            ),
            'resolve' => array($this, 'resolve_page_builder_page')
        ));
    }
}
```

### 2. 组件注册系统
```typescript
// 组件注册中心
class ComponentRegistry {
  private static instance: ComponentRegistry;
  private components: Map<string, ComponentDefinition>;
  private categories: Map<string, ComponentCategory>;

  private constructor() {
    this.components = new Map();
    this.categories = new Map();
    this.initializeDefaultComponents();
  }

  static getInstance(): ComponentRegistry {
    if (!ComponentRegistry.instance) {
      ComponentRegistry.instance = new ComponentRegistry();
    }
    return ComponentRegistry.instance;
  }

  register(definition: ComponentDefinition): void {
    // 验证组件定义
    this.validateComponentDefinition(definition);

    // 注册组件
    this.components.set(definition.type, definition);

    // 更新分类
    this.updateCategory(definition.category, definition);
  }

  get(type: string): ComponentClass | null {
    const definition = this.components.get(type);
    return definition ? definition.component : null;
  }

  getByCategory(category: string): ComponentDefinition[] {
    return Array.from(this.components.values())
      .filter(def => def.category === category);
  }

  getAllComponents(): ComponentDefinition[] {
    return Array.from(this.components.values());
  }

  private initializeDefaultComponents(): void {
    // 注册基础组件
    this.register({
      type: 'text',
      name: '文本',
      category: 'basic',
      icon: 'Type',
      component: TextComponent,
      props: TextComponentProps,
      configPanel: TextConfigPanel
    });

    this.register({
      type: 'image',
      name: '图片',
      category: 'basic',
      icon: 'Image',
      component: ImageComponent,
      props: ImageComponentProps,
      configPanel: ImageConfigPanel
    });

    // ... 注册其他组件
  }
}

// 组件定义接口
interface ComponentDefinition {
  type: string;
  name: string;
  category: string;
  icon: string;
  description?: string;
  component: ComponentClass;
  props: PropDefinition;
  configPanel: ConfigPanelComponent;
  dataSource?: DataSourceDefinition;
  preview?: PreviewComponent;
}
```

### 3. 拖拽系统实现
```typescript
// 拖拽上下文
import { DndContext, DragOverlay, closestCenter } from '@dnd-kit/core';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';

export const DragDropProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [activeId, setActiveId] = useState<string | null>(null);
  const [draggedComponent, setDraggedComponent] = useState<ComponentData | null>(null);

  const handleDragStart = (event: DragStartEvent) => {
    const { active } = event;
    setActiveId(active.id as string);

    // 获取被拖拽的组件数据
    const component = getComponentById(active.id as string);
    setDraggedComponent(component);
  };

  const handleDragEnd = (event: DragEndEvent) => {
    const { active, over } = event;

    if (!over) {
      setActiveId(null);
      setDraggedComponent(null);
      return;
    }

    // 处理组件拖拽到画布
    if (over.id === 'canvas-drop-zone') {
      handleDropToCanvas(active.id as string, over.data.current?.position);
    }

    // 处理组件重新排序
    if (active.id !== over.id) {
      handleComponentReorder(active.id as string, over.id as string);
    }

    setActiveId(null);
    setDraggedComponent(null);
  };

  return (
    <DndContext
      collisionDetection={closestCenter}
      onDragStart={handleDragStart}
      onDragEnd={handleDragEnd}
    >
      {children}
      <DragOverlay>
        {activeId && draggedComponent ? (
          <ComponentPreview component={draggedComponent} isDragging />
        ) : null}
      </DragOverlay>
    </DndContext>
  );
};

// 可拖拽组件
import { useDraggable } from '@dnd-kit/core';

export const DraggableComponent: React.FC<{
  component: ComponentDefinition;
  children: React.ReactNode;
}> = ({ component, children }) => {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    isDragging
  } = useDraggable({
    id: component.type,
    data: {
      type: 'component',
      component
    }
  });

  const style = transform ? {
    transform: `translate3d(${transform.x}px, ${transform.y}px, 0)`,
  } : undefined;

  return (
    <div
      ref={setNodeRef}
      style={style}
      {...listeners}
      {...attributes}
      className={`draggable-component ${isDragging ? 'dragging' : ''}`}
    >
      {children}
    </div>
  );
};

// 可放置区域
import { useDroppable } from '@dnd-kit/core';

export const DroppableCanvas: React.FC<{
  children: React.ReactNode;
}> = ({ children }) => {
  const { isOver, setNodeRef } = useDroppable({
    id: 'canvas-drop-zone',
    data: {
      type: 'canvas'
    }
  });

  return (
    <div
      ref={setNodeRef}
      className={`canvas-drop-zone ${isOver ? 'drag-over' : ''}`}
    >
      {children}
    </div>
  );
};
```

### 4. 状态管理系统
```typescript
// 使用Zustand进行状态管理
import { create } from 'zustand';
import { devtools, persist } from 'zustand/middleware';

interface BuilderState {
  // 页面数据
  pageData: PageData;

  // 选中的组件
  selectedComponent: string | null;

  // 编辑模式
  editMode: 'design' | 'preview' | 'code';

  // 设备预览模式
  deviceMode: 'desktop' | 'tablet' | 'mobile';

  // 历史记录
  history: PageData[];
  historyIndex: number;

  // 操作方法
  setPageData: (data: PageData) => void;
  selectComponent: (id: string | null) => void;
  addComponent: (component: ComponentData, parentId?: string, index?: number) => void;
  updateComponent: (id: string, updates: Partial<ComponentData>) => void;
  deleteComponent: (id: string) => void;
  moveComponent: (id: string, newParentId: string, newIndex: number) => void;

  // 历史操作
  undo: () => void;
  redo: () => void;
  saveToHistory: () => void;

  // 模式切换
  setEditMode: (mode: 'design' | 'preview' | 'code') => void;
  setDeviceMode: (mode: 'desktop' | 'tablet' | 'mobile') => void;
}

export const useBuilderStore = create<BuilderState>()(
  devtools(
    persist(
      (set, get) => ({
        pageData: {
          id: '',
          title: '',
          components: [],
          globalStyles: {},
          settings: {}
        },
        selectedComponent: null,
        editMode: 'design',
        deviceMode: 'desktop',
        history: [],
        historyIndex: -1,

        setPageData: (data) => {
          set({ pageData: data });
          get().saveToHistory();
        },

        selectComponent: (id) => set({ selectedComponent: id }),

        addComponent: (component, parentId, index) => {
          const state = get();
          const newPageData = addComponentToPage(
            state.pageData,
            component,
            parentId,
            index
          );
          set({ pageData: newPageData });
          state.saveToHistory();
        },

        updateComponent: (id, updates) => {
          const state = get();
          const newPageData = updateComponentInPage(
            state.pageData,
            id,
            updates
          );
          set({ pageData: newPageData });
          state.saveToHistory();
        },

        deleteComponent: (id) => {
          const state = get();
          const newPageData = deleteComponentFromPage(state.pageData, id);
          set({
            pageData: newPageData,
            selectedComponent: state.selectedComponent === id ? null : state.selectedComponent
          });
          state.saveToHistory();
        },

        moveComponent: (id, newParentId, newIndex) => {
          const state = get();
          const newPageData = moveComponentInPage(
            state.pageData,
            id,
            newParentId,
            newIndex
          );
          set({ pageData: newPageData });
          state.saveToHistory();
        },

        undo: () => {
          const state = get();
          if (state.historyIndex > 0) {
            const newIndex = state.historyIndex - 1;
            set({
              pageData: state.history[newIndex],
              historyIndex: newIndex
            });
          }
        },

        redo: () => {
          const state = get();
          if (state.historyIndex < state.history.length - 1) {
            const newIndex = state.historyIndex + 1;
            set({
              pageData: state.history[newIndex],
              historyIndex: newIndex
            });
          }
        },

        saveToHistory: () => {
          const state = get();
          const newHistory = state.history.slice(0, state.historyIndex + 1);
          newHistory.push(state.pageData);

          // 限制历史记录数量
          if (newHistory.length > 50) {
            newHistory.shift();
          }

          set({
            history: newHistory,
            historyIndex: newHistory.length - 1
          });
        },

        setEditMode: (mode) => set({ editMode: mode }),
        setDeviceMode: (mode) => set({ deviceMode: mode })
      }),
      {
        name: 'page-builder-storage',
        partialize: (state) => ({
          pageData: state.pageData,
          editMode: state.editMode,
          deviceMode: state.deviceMode
        })
      }
    )
  )
);
```

### 5. 配置面板系统
```typescript
// 动态配置面板
export const ConfigPanel: React.FC<{
  component: ComponentData;
  onUpdate: (updates: Partial<ComponentData>) => void;
}> = ({ component, onUpdate }) => {
  const componentDef = ComponentRegistry.getInstance().get(component.type);

  if (!componentDef) return null;

  return (
    <div className="config-panel">
      <div className="config-header">
        <h3>{componentDef.name} 设置</h3>
      </div>

      <div className="config-content">
        {/* 属性配置 */}
        <ConfigSection title="属性设置">
          <PropsEditor
            definition={componentDef.props}
            values={component.props}
            onChange={(props) => onUpdate({ props })}
          />
        </ConfigSection>

        {/* 样式配置 */}
        <ConfigSection title="样式设置">
          <StyleEditor
            values={component.styles}
            onChange={(styles) => onUpdate({ styles })}
          />
        </ConfigSection>

        {/* 响应式配置 */}
        <ConfigSection title="响应式设置">
          <ResponsiveEditor
            values={component.responsive}
            onChange={(responsive) => onUpdate({ responsive })}
          />
        </ConfigSection>

        {/* 数据源配置 */}
        {componentDef.dataSource && (
          <ConfigSection title="数据源设置">
            <DataSourceEditor
              definition={componentDef.dataSource}
              values={component.dataSource}
              onChange={(dataSource) => onUpdate({ dataSource })}
            />
          </ConfigSection>
        )}
      </div>
    </div>
  );
};

// 属性编辑器
const PropsEditor: React.FC<{
  definition: PropDefinition;
  values: any;
  onChange: (values: any) => void;
}> = ({ definition, values, onChange }) => {
  const handleChange = (key: string, value: any) => {
    onChange({
      ...values,
      [key]: value
    });
  };

  return (
    <div className="props-editor">
      {Object.entries(definition).map(([key, propDef]) => (
        <div key={key} className="prop-field">
          <label>{propDef.label || key}</label>
          <PropFieldEditor
            type={propDef.type}
            value={values[key]}
            options={propDef.options}
            onChange={(value) => handleChange(key, value)}
          />
        </div>
      ))}
    </div>
  );
};

// 字段编辑器
const PropFieldEditor: React.FC<{
  type: string;
  value: any;
  options?: any;
  onChange: (value: any) => void;
}> = ({ type, value, options, onChange }) => {
  switch (type) {
    case 'text':
      return (
        <Input
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder={options?.placeholder}
        />
      );

    case 'textarea':
      return (
        <Textarea
          value={value || ''}
          onChange={(e) => onChange(e.target.value)}
          placeholder={options?.placeholder}
          rows={options?.rows || 3}
        />
      );

    case 'select':
      return (
        <Select
          value={value}
          onValueChange={onChange}
        >
          {options?.options?.map((option: any) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </Select>
      );

    case 'boolean':
      return (
        <Switch
          checked={value || false}
          onCheckedChange={onChange}
        />
      );

    case 'number':
      return (
        <Input
          type="number"
          value={value || 0}
          onChange={(e) => onChange(Number(e.target.value))}
          min={options?.min}
          max={options?.max}
          step={options?.step}
        />
      );

    case 'color':
      return (
        <ColorPicker
          value={value || '#000000'}
          onChange={onChange}
        />
      );

    case 'media':
      return (
        <MediaPicker
          value={value}
          onChange={onChange}
          accept={options?.accept}
          multiple={options?.multiple}
        />
      );

    default:
      return <div>Unsupported field type: {type}</div>;
  }
};
```

## 总开发周期：17-23周

这个规划提供了一个完整的WordPress无头主题开发路线图，确保项目的可行性和高质量交付。每个阶段都有明确的目标和可交付成果，便于项目管理和进度跟踪。

## 关键技术要点总结

1. **统一渲染引擎**：确保预览和发布完全一致
2. **组件化架构**：高度可扩展的组件系统
3. **实时数据同步**：GraphQL + 智能缓存策略
4. **拖拽交互**：基于@dnd-kit的专业拖拽体验
5. **状态管理**：Zustand提供的轻量级状态管理
6. **样式系统**：TailwindCSS + 动态样式注入
7. **授权系统**：完整的许可证和权限管理
8. **性能优化**：懒加载、缓存、代码分割等策略

这个项目在技术上完全可行，关键是要做好架构设计和分阶段实施。建议先从基础功能开始，逐步迭代完善功能。

## 安全性设计

### 1. 数据验证和过滤
```php
// 数据验证器
class DataValidator {
    private $allowed_components = array();
    private $allowed_props = array();

    public function __construct() {
        $this->load_component_whitelist();
    }

    public function validate_page_data($page_data) {
        // 验证页面结构
        if (!$this->validate_page_structure($page_data)) {
            throw new ValidationException('页面结构验证失败');
        }

        // 验证组件数据
        foreach ($page_data['components'] as $component) {
            if (!$this->validate_component($component)) {
                throw new ValidationException('组件验证失败: ' . $component['type']);
            }
        }

        return true;
    }

    private function validate_component($component) {
        // 检查组件类型是否在白名单中
        if (!in_array($component['type'], $this->allowed_components)) {
            return false;
        }

        // 验证组件属性
        if (!$this->validate_component_props($component['type'], $component['props'])) {
            return false;
        }

        // 验证样式属性
        if (!$this->validate_component_styles($component['styles'])) {
            return false;
        }

        // 递归验证子组件
        if (isset($component['children'])) {
            foreach ($component['children'] as $child) {
                if (!$this->validate_component($child)) {
                    return false;
                }
            }
        }

        return true;
    }

    private function validate_component_props($component_type, $props) {
        $allowed_props = $this->allowed_props[$component_type] ?? array();

        foreach ($props as $key => $value) {
            if (!isset($allowed_props[$key])) {
                return false;
            }

            $prop_config = $allowed_props[$key];
            if (!$this->validate_prop_value($value, $prop_config)) {
                return false;
            }
        }

        return true;
    }

    private function validate_prop_value($value, $config) {
        switch ($config['type']) {
            case 'string':
                return is_string($value) && strlen($value) <= ($config['maxLength'] ?? 1000);
            case 'number':
                return is_numeric($value) &&
                       $value >= ($config['min'] ?? PHP_INT_MIN) &&
                       $value <= ($config['max'] ?? PHP_INT_MAX);
            case 'boolean':
                return is_bool($value);
            case 'url':
                return filter_var($value, FILTER_VALIDATE_URL) !== false;
            case 'email':
                return filter_var($value, FILTER_VALIDATE_EMAIL) !== false;
            default:
                return true;
        }
    }
}

// 输入过滤器
class InputSanitizer {
    public function sanitize_page_data($page_data) {
        return array(
            'title' => sanitize_text_field($page_data['title']),
            'components' => $this->sanitize_components($page_data['components']),
            'globalStyles' => $this->sanitize_styles($page_data['globalStyles']),
            'settings' => $this->sanitize_settings($page_data['settings'])
        );
    }

    private function sanitize_components($components) {
        return array_map(array($this, 'sanitize_component'), $components);
    }

    private function sanitize_component($component) {
        $sanitized = array(
            'id' => sanitize_key($component['id']),
            'type' => sanitize_key($component['type']),
            'props' => $this->sanitize_component_props($component['props']),
            'styles' => $this->sanitize_styles($component['styles'])
        );

        if (isset($component['children'])) {
            $sanitized['children'] = $this->sanitize_components($component['children']);
        }

        return $sanitized;
    }

    private function sanitize_component_props($props) {
        $sanitized = array();

        foreach ($props as $key => $value) {
            $sanitized_key = sanitize_key($key);

            if (is_string($value)) {
                $sanitized[$sanitized_key] = wp_kses_post($value);
            } elseif (is_array($value)) {
                $sanitized[$sanitized_key] = $this->sanitize_array($value);
            } else {
                $sanitized[$sanitized_key] = $value;
            }
        }

        return $sanitized;
    }
}
```

### 2. 权限控制系统
```php
// 权限控制器
class PermissionController {
    private $user_capabilities;
    private $license_manager;

    public function __construct() {
        $this->license_manager = new LicenseManager();
        $this->load_user_capabilities();
    }

    public function can_access_builder($user_id = null) {
        $user_id = $user_id ?: get_current_user_id();

        // 检查基础权限
        if (!user_can($user_id, 'page_builder_access')) {
            return false;
        }

        // 检查许可证
        $license_key = get_option('page_builder_license_key');
        if (!$this->license_manager->validate_license($license_key)) {
            return false;
        }

        return true;
    }

    public function can_create_page($user_id = null) {
        return $this->can_access_builder($user_id) &&
               user_can($user_id ?: get_current_user_id(), 'page_builder_create');
    }

    public function can_edit_page($page_id, $user_id = null) {
        $user_id = $user_id ?: get_current_user_id();

        if (!$this->can_access_builder($user_id)) {
            return false;
        }

        // 检查页面所有权
        $page_author = get_post_field('post_author', $page_id);
        if ($page_author == $user_id) {
            return true;
        }

        // 检查编辑权限
        return user_can($user_id, 'page_builder_edit_others');
    }

    public function can_publish_page($user_id = null) {
        return $this->can_access_builder($user_id) &&
               user_can($user_id ?: get_current_user_id(), 'page_builder_publish');
    }
}
```

### 3. 加密系统
```php
// 加密服务
class EncryptionService {
    private $encryption_key;
    private $cipher_method = 'AES-256-CBC';

    public function __construct() {
        $this->encryption_key = $this->get_encryption_key();
    }

    public function encrypt($data) {
        $json_data = json_encode($data);
        $iv = openssl_random_pseudo_bytes(openssl_cipher_iv_length($this->cipher_method));
        $encrypted = openssl_encrypt($json_data, $this->cipher_method, $this->encryption_key, 0, $iv);

        return base64_encode($iv . $encrypted);
    }

    public function decrypt($encrypted_data) {
        $data = base64_decode($encrypted_data);
        $iv_length = openssl_cipher_iv_length($this->cipher_method);
        $iv = substr($data, 0, $iv_length);
        $encrypted = substr($data, $iv_length);

        $decrypted = openssl_decrypt($encrypted, $this->cipher_method, $this->encryption_key, 0, $iv);

        if ($decrypted === false) {
            throw new Exception('解密失败');
        }

        return json_decode($decrypted, true);
    }

    private function get_encryption_key() {
        $key = get_option('page_builder_encryption_key');

        if (!$key) {
            $key = wp_generate_password(32, false);
            update_option('page_builder_encryption_key', $key);
        }

        return hash('sha256', $key . SECURE_AUTH_KEY);
    }
}
```

## 测试策略

### 1. 单元测试
```typescript
// 组件测试示例
import { render, screen, fireEvent } from '@testing-library/react';
import { TextComponent } from '../components/TextComponent';

describe('TextComponent', () => {
  const defaultProps = {
    content: 'Test content',
    tag: 'p',
    styles: {
      fontSize: '16px',
      color: '#000000'
    }
  };

  it('renders with correct content', () => {
    render(<TextComponent {...defaultProps} />);
    expect(screen.getByText('Test content')).toBeInTheDocument();
  });

  it('applies correct styles', () => {
    render(<TextComponent {...defaultProps} />);
    const element = screen.getByText('Test content');
    expect(element).toHaveStyle({
      fontSize: '16px',
      color: '#000000'
    });
  });

  it('renders with correct HTML tag', () => {
    render(<TextComponent {...defaultProps} tag="h1" />);
    expect(screen.getByRole('heading', { level: 1 })).toBeInTheDocument();
  });
});

// 状态管理测试
import { renderHook, act } from '@testing-library/react';
import { useBuilderStore } from '../store/builderStore';

describe('useBuilderStore', () => {
  beforeEach(() => {
    useBuilderStore.getState().setPageData({
      id: '',
      title: '',
      components: [],
      globalStyles: {},
      settings: {}
    });
  });

  it('adds component correctly', () => {
    const { result } = renderHook(() => useBuilderStore());

    act(() => {
      result.current.addComponent({
        id: 'test-1',
        type: 'text',
        props: { content: 'Test' },
        styles: {}
      });
    });

    expect(result.current.pageData.components).toHaveLength(1);
    expect(result.current.pageData.components[0].id).toBe('test-1');
  });

  it('updates component correctly', () => {
    const { result } = renderHook(() => useBuilderStore());

    // 先添加组件
    act(() => {
      result.current.addComponent({
        id: 'test-1',
        type: 'text',
        props: { content: 'Test' },
        styles: {}
      });
    });

    // 更新组件
    act(() => {
      result.current.updateComponent('test-1', {
        props: { content: 'Updated Test' }
      });
    });

    expect(result.current.pageData.components[0].props.content).toBe('Updated Test');
  });
});
```

### 2. 集成测试
```typescript
// 页面构建器集成测试
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { PageBuilder } from '../pages/PageBuilder';
import { DragDropProvider } from '../providers/DragDropProvider';

describe('PageBuilder Integration', () => {
  const renderPageBuilder = () => {
    return render(
      <DragDropProvider>
        <PageBuilder />
      </DragDropProvider>
    );
  };

  it('allows dragging component from panel to canvas', async () => {
    renderPageBuilder();

    // 获取组件面板中的文本组件
    const textComponent = screen.getByTestId('component-text');
    const canvas = screen.getByTestId('canvas-drop-zone');

    // 模拟拖拽操作
    fireEvent.dragStart(textComponent);
    fireEvent.dragEnter(canvas);
    fireEvent.dragOver(canvas);
    fireEvent.drop(canvas);

    // 验证组件是否添加到画布
    await waitFor(() => {
      expect(screen.getByTestId('canvas-component-text')).toBeInTheDocument();
    });
  });

  it('shows component properties when selected', async () => {
    renderPageBuilder();

    // 添加组件到画布
    const textComponent = screen.getByTestId('component-text');
    const canvas = screen.getByTestId('canvas-drop-zone');

    fireEvent.dragStart(textComponent);
    fireEvent.drop(canvas);

    // 选择画布中的组件
    await waitFor(() => {
      const canvasComponent = screen.getByTestId('canvas-component-text');
      fireEvent.click(canvasComponent);
    });

    // 验证属性面板是否显示
    expect(screen.getByTestId('properties-panel')).toBeInTheDocument();
    expect(screen.getByLabelText('内容')).toBeInTheDocument();
  });
});
```

### 3. 端到端测试
```typescript
// 使用Playwright进行E2E测试
import { test, expect } from '@playwright/test';

test.describe('Page Builder E2E', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/builder');
    await page.waitForLoadState('networkidle');
  });

  test('complete page building workflow', async ({ page }) => {
    // 1. 拖拽文本组件到画布
    await page.dragAndDrop('[data-testid="component-text"]', '[data-testid="canvas-drop-zone"]');

    // 2. 选择组件并编辑属性
    await page.click('[data-testid="canvas-component-text"]');
    await page.fill('[data-testid="text-content-input"]', '这是测试文本');

    // 3. 添加图片组件
    await page.dragAndDrop('[data-testid="component-image"]', '[data-testid="canvas-drop-zone"]');

    // 4. 预览页面
    await page.click('[data-testid="preview-button"]');
    await expect(page.locator('[data-testid="preview-frame"]')).toBeVisible();

    // 5. 保存页面
    await page.click('[data-testid="save-button"]');
    await expect(page.locator('.success-message')).toBeVisible();

    // 6. 发布页面
    await page.click('[data-testid="publish-button"]');
    await page.fill('[data-testid="target-site-input"]', 'https://example.com');
    await page.click('[data-testid="confirm-publish"]');

    await expect(page.locator('.publish-success')).toBeVisible();
  });

  test('responsive design preview', async ({ page }) => {
    // 添加组件
    await page.dragAndDrop('[data-testid="component-text"]', '[data-testid="canvas-drop-zone"]');

    // 切换到平板预览
    await page.click('[data-testid="device-tablet"]');
    await expect(page.locator('[data-testid="canvas"]')).toHaveClass(/tablet-view/);

    // 切换到手机预览
    await page.click('[data-testid="device-mobile"]');
    await expect(page.locator('[data-testid="canvas"]')).toHaveClass(/mobile-view/);

    // 验证响应式样式
    const component = page.locator('[data-testid="canvas-component-text"]');
    await expect(component).toHaveCSS('width', '100%');
  });
});
```

## 部署方案

### 1. 开发环境配置
```yaml
# docker-compose.yml
version: '3.8'
services:
  wordpress:
    image: wordpress:latest
    ports:
      - "8080:80"
    environment:
      WORDPRESS_DB_HOST: db
      WORDPRESS_DB_USER: wordpress
      WORDPRESS_DB_PASSWORD: wordpress
      WORDPRESS_DB_NAME: wordpress
    volumes:
      - ./wordpress:/var/www/html
      - ./plugins:/var/www/html/wp-content/plugins/page-builder
    depends_on:
      - db

  db:
    image: mysql:8.0
    environment:
      MYSQL_DATABASE: wordpress
      MYSQL_USER: wordpress
      MYSQL_PASSWORD: wordpress
      MYSQL_ROOT_PASSWORD: rootpassword
    volumes:
      - db_data:/var/lib/mysql

  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    volumes:
      - ./frontend:/app
      - /app/node_modules
    environment:
      - NODE_ENV=development
      - NEXT_PUBLIC_WORDPRESS_URL=http://localhost:8080
    depends_on:
      - wordpress

volumes:
  db_data:
```

### 2. 生产环境部署
```bash
#!/bin/bash
# deploy.sh

# 构建前端应用
echo "Building frontend applications..."
cd server/frontend
npm run build
cd ../../client/frontend
npm run build
cd ../..

# 打包WordPress插件
echo "Packaging WordPress plugins..."
zip -r server-plugin.zip server/wordpress-plugin/
zip -r client-plugin.zip client/wordpress-plugin/

# 部署到服务器
echo "Deploying to production..."
rsync -avz --delete server/frontend/out/ user@server:/var/www/server-frontend/
rsync -avz --delete client/frontend/out/ user@server:/var/www/client-frontend/

# 上传插件到WordPress
echo "Uploading plugins..."
scp server-plugin.zip user@server:/tmp/
scp client-plugin.zip user@server:/tmp/

echo "Deployment completed!"
```

### 3. CI/CD配置
```yaml
# .github/workflows/deploy.yml
name: Deploy Page Builder

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Install dependencies
      run: |
        cd server/frontend && npm ci
        cd ../../client/frontend && npm ci

    - name: Run tests
      run: |
        cd server/frontend && npm test
        cd ../../client/frontend && npm test

    - name: Run E2E tests
      run: |
        cd server/frontend && npm run test:e2e

  build:
    needs: test
    runs-on: ubuntu-latest

    steps:
    - uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'
        cache: 'npm'

    - name: Build applications
      run: |
        cd server/frontend && npm ci && npm run build
        cd ../../client/frontend && npm ci && npm run build

    - name: Upload build artifacts
      uses: actions/upload-artifact@v3
      with:
        name: build-files
        path: |
          server/frontend/out/
          client/frontend/out/

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
    - name: Download build artifacts
      uses: actions/download-artifact@v3
      with:
        name: build-files

    - name: Deploy to production
      run: |
        # 部署脚本
        echo "Deploying to production..."
```

## 监控和维护

### 1. 性能监控
```typescript
// 性能监控服务
class PerformanceMonitor {
  private metrics: Map<string, number[]>;

  constructor() {
    this.metrics = new Map();
    this.initializeMonitoring();
  }

  private initializeMonitoring() {
    // 监控页面加载时间
    window.addEventListener('load', () => {
      const loadTime = performance.now();
      this.recordMetric('page_load_time', loadTime);
    });

    // 监控组件渲染时间
    this.monitorComponentRender();

    // 监控API响应时间
    this.monitorAPIRequests();
  }

  recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }

    const values = this.metrics.get(name)!;
    values.push(value);

    // 保持最近100个数据点
    if (values.length > 100) {
      values.shift();
    }

    // 发送到监控服务
    this.sendToMonitoringService(name, value);
  }

  private sendToMonitoringService(metric: string, value: number) {
    // 发送到监控服务（如Google Analytics、Sentry等）
    if (typeof gtag !== 'undefined') {
      gtag('event', 'performance_metric', {
        metric_name: metric,
        metric_value: value
      });
    }
  }
}
```

### 2. 错误监控
```typescript
// 错误监控和报告
class ErrorMonitor {
  constructor() {
    this.initializeErrorHandling();
  }

  private initializeErrorHandling() {
    // 全局错误处理
    window.addEventListener('error', (event) => {
      this.reportError({
        type: 'javascript_error',
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        stack: event.error?.stack
      });
    });

    // Promise错误处理
    window.addEventListener('unhandledrejection', (event) => {
      this.reportError({
        type: 'promise_rejection',
        message: event.reason?.message || 'Unhandled promise rejection',
        stack: event.reason?.stack
      });
    });

    // React错误边界
    this.setupReactErrorBoundary();
  }

  reportError(error: any) {
    // 发送错误报告到监控服务
    fetch('/api/error-report', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        ...error,
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href
      })
    }).catch(console.error);
  }
}
```

## 🎯 核心架构总结

### 预览系统重新定义
经过调整，预览系统的核心逻辑如下：

#### 🔍 **服务端预览（构建器中）**
- **目的**：纯粹用于布局和样式预览
- **数据源**：服务端提供的模拟数据
- **特点**：
  - 文章列表显示预设的示例文章
  - 轮播图显示示例图片和文字
  - 表单显示布局结构（禁用状态）
  - 菜单显示模拟的导航结构
  - 所有组件都使用统一的示例内容

#### 📤 **导出和发布**
- **导出内容**：只导出布局配置和样式
- **不包含**：具体的内容数据
- **格式**：加密的JSON文件，包含：
  ```json
  {
    "components": [
      {
        "type": "postList",
        "props": { "layout": "grid", "showDate": true },
        "styles": { "padding": "20px" },
        "dataSource": { "type": "posts", "query": { "limit": 6 } }
      }
    ],
    "globalStyles": { ... },
    "settings": { ... }
  }
  ```

#### 🎨 **客户端渲染**
- **数据获取**：根据布局配置从客户的WordPress获取真实数据
- **渲染逻辑**：
  - `postList` 组件 → 获取客户的WordPress文章
  - `navigation` 组件 → 获取客户的WordPress菜单
  - `search` 组件 → 搜索客户的WordPress内容
  - `form` 组件 → 提交到客户的WordPress

### 🔄 **数据流程图**
```
服务端构建器:
模拟数据 → 预览组件 → 布局配置 → 导出/发布

客户端渲染:
布局配置 → 解析组件 → 获取WordPress数据 → 渲染页面
```

### 💡 **关键优势**
1. **预览专注布局**：用户专注于设计和布局，不被内容干扰
2. **数据分离**：布局和内容完全分离，提高复用性
3. **性能优化**：客户端只需要渲染自己的数据
4. **安全性**：不涉及跨站点数据访问
5. **灵活性**：同一个布局可以适用于不同的WordPress站点

### 🛠 **技术实现要点**

#### 组件双重渲染模式
```typescript
// 每个组件都有两种渲染模式
interface ComponentDefinition {
  // 预览模式：使用模拟数据
  renderPreview: (props: any, mockData?: any) => ReactElement;

  // 客户端模式：使用真实数据
  renderClient: (props: any, realData?: any) => ReactElement;

  // 数据源配置：告诉客户端如何获取数据
  dataSource?: DataSourceConfig;
}
```

#### 布局配置标准化
```typescript
interface LayoutData {
  version: string;
  components: LayoutComponentData[];
  globalStyles: GlobalStyles;
  settings: LayoutSettings;
  metadata: {
    generator: string;
    exportTime: number;
    builderVersion: string;
  };
}
```

这个调整后的架构更加清晰和实用，真正实现了"布局构建器"的概念，而不是"内容管理器"。用户可以专注于设计美观的布局，然后将这个布局应用到任何WordPress站点上。

这个超级详细的规划文档涵盖了WordPress无头主题开发的所有关键方面，从技术架构到安全性、测试和部署。项目虽然复杂，但通过合理的分阶段实施和严格的质量控制，完全可以成功交付。
