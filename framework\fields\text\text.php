<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework 文本字段类型
 * 
 * 这个类实现了基础的文本输入字段功能。
 * 支持各种文本输入类型，如text、email、url、password等。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Field_text' ) ) {
    
    /**
     * XUN_Field_text 文本字段类
     * 
     * 提供文本输入字段的完整功能，包括：
     * - 基础文本输入
     * - 邮箱输入
     * - URL输入
     * - 密码输入
     * - 数字输入
     * 
     * @since 1.0
     */
    class XUN_Field_text extends XUN_Fields {
        
        /**
         * 构造函数
         * 
         * 初始化文本字段实例。
         * 
         * @since 1.0
         * 
         * @param array  $field  字段配置数组
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }
        
        /**
         * 渲染文本字段
         *
         * 输出文本输入字段的HTML代码。
         *
         * @since 1.0
         */
        public function render() {

            // 获取输入类型，默认为text
            $type = ( ! empty( $this->field['attributes']['type'] ) ) ? $this->field['attributes']['type'] : 'text';

            // 获取占位符
            $placeholder = ! empty( $this->field['placeholder'] ) ? $this->field['placeholder'] : '';

            // 获取字段标题
            $title = ! empty( $this->field['title'] ) ? $this->field['title'] : '';

            // 获取字段描述
            $desc = ! empty( $this->field['desc'] ) ? $this->field['desc'] : '';

            // 输出前置内容
            echo $this->field_before();

            // 开始字段容器（标题由框架统一处理）
            echo '<div>';

            // 输入框容器
            echo '<div class="mt-2">';

            // 现代化输入框样式
            $input_classes = 'block w-full rounded-md bg-white px-3 py-1.5 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6';

            // 根据字段类型添加特定样式
            if ( $type === 'number' ) {
                $input_classes .= ' text-right';
            }

            // 如果有错误，添加错误样式
            if ( ! empty( $this->field['_error'] ) ) {
                $input_classes = str_replace('outline-gray-300 focus:outline-indigo-600', 'outline-red-300 focus:outline-red-600', $input_classes);
            }

            // 输出现代化文本输入框
            echo '<input type="' . esc_attr( $type ) . '" ';
            echo 'name="' . esc_attr( $this->field_name() ) . '" ';
            echo 'id="' . esc_attr( $this->field_id() ) . '" ';
            echo 'value="' . esc_attr( $this->value ) . '" ';
            echo 'class="' . esc_attr( $input_classes ) . '" ';

            if ( ! empty( $placeholder ) ) {
                echo 'placeholder="' . esc_attr( $placeholder ) . '" ';
            }

            // 如果有描述，添加aria-describedby属性
            if ( ! empty( $desc ) ) {
                echo 'aria-describedby="' . esc_attr( $this->field_id() . '-description' ) . '" ';
            }

            echo $this->field_attributes() . ' />';

            echo '</div>'; // 结束输入框容器

            // 输出描述文本
            if ( ! empty( $desc ) ) {
                echo '<p id="' . esc_attr( $this->field_id() . '-description' ) . '" class="mt-2 text-sm text-gray-500">';
                echo wp_kses_post( $desc );
                echo '</p>';
            }

            // 输出错误信息
            if ( ! empty( $this->field['_error'] ) ) {
                echo '<p class="mt-2 text-sm text-red-600">';
                echo esc_html( $this->field['_error'] );
                echo '</p>';
            }

            // 输出后置内容（不包括desc，因为我们已经输出了）
            if ( ! empty( $this->field['after'] ) ) {
                echo '<div class="xun-after-text">' . $this->field['after'] . '</div>';
            }

            // 输出帮助信息
            if ( ! empty( $this->field['help'] ) ) {
                echo '<div class="xun-help">';
                echo '<span class="xun-help-text">' . $this->field['help'] . '</span>';
                echo '<i class="xun-help-icon">?</i>';
                echo '</div>';
            }

            echo '</div>'; // 结束字段容器
        }
        
        /**
         * 加载字段资源
         * 
         * 加载文本字段所需的CSS和JavaScript文件。
         * 
         * @since 1.0
         */
        public function enqueue() {
            
            // 文本字段通常不需要额外的CSS或JS
            // 如果需要特殊功能（如输入验证），可以在这里加载
            
            // 示例：加载字段特定的样式
            // wp_enqueue_style( 'xun-field-text', XUN_URL . 'fields/text/text.css', array(), XUN_VERSION );
            
            // 示例：加载字段特定的脚本
            // wp_enqueue_script( 'xun-field-text', XUN_URL . 'fields/text/text.js', array( 'jquery' ), XUN_VERSION, true );
        }
        
        /**
         * 验证文本字段值
         * 
         * 根据字段类型对输入值进行验证和清理。
         * 
         * @since 1.0
         * 
         * @param mixed $value 要验证的值
         * 
         * @return mixed 验证后的值
         */
        public function validate( $value ) {
            
            // 获取输入类型
            $type = ( ! empty( $this->field['attributes']['type'] ) ) ? $this->field['attributes']['type'] : 'text';
            
            // 根据不同类型进行验证
            switch ( $type ) {
                case 'email':
                    // 验证邮箱格式
                    $value = sanitize_email( $value );
                    if ( ! empty( $value ) && ! is_email( $value ) ) {
                        // 如果邮箱格式不正确，返回空值或错误
                        $value = '';
                    }
                    break;
                    
                case 'url':
                    // 验证URL格式
                    $value = esc_url_raw( $value );
                    break;
                    
                case 'number':
                    // 验证数字
                    $value = intval( $value );
                    break;
                    
                case 'password':
                    // 密码字段不进行特殊处理，保持原值
                    $value = $value;
                    break;
                    
                case 'tel':
                    // 电话号码清理
                    $value = preg_replace( '/[^0-9+\-\s\(\)]/', '', $value );
                    break;
                    
                default:
                    // 默认文本清理
                    $value = sanitize_text_field( $value );
                    break;
            }
            
            // 检查最大长度限制
            if ( ! empty( $this->field['max_length'] ) ) {
                $max_length = intval( $this->field['max_length'] );
                if ( strlen( $value ) > $max_length ) {
                    $value = substr( $value, 0, $max_length );
                }
            }
            
            // 检查最小长度限制
            if ( ! empty( $this->field['min_length'] ) ) {
                $min_length = intval( $this->field['min_length'] );
                if ( strlen( $value ) < $min_length ) {
                    // 可以设置错误信息或返回默认值
                    // 这里简单返回空值
                    $value = '';
                }
            }
            
            // 应用自定义验证过滤器
            $value = apply_filters( 'xun_validate_text_field', $value, $this->field );
            $value = apply_filters( "xun_validate_text_field_{$this->field['id']}", $value, $this->field );
            
            return $value;
        }
        
        /**
         * 获取字段默认属性
         * 
         * 返回文本字段的默认HTML属性。
         * 
         * @since 1.0
         * 
         * @return array 默认属性数组
         */
        public function get_default_attributes() {
            
            $attributes = array();
            
            // 添加CSS类
            $attributes['class'] = 'xun-field-text';
            
            // 添加最大长度属性
            if ( ! empty( $this->field['max_length'] ) ) {
                $attributes['maxlength'] = intval( $this->field['max_length'] );
            }
            
            // 添加最小长度属性（HTML5）
            if ( ! empty( $this->field['min_length'] ) ) {
                $attributes['minlength'] = intval( $this->field['min_length'] );
            }
            
            // 添加必填属性
            if ( $this->is_required() ) {
                $attributes['required'] = 'required';
            }
            
            // 添加只读属性
            if ( ! empty( $this->field['readonly'] ) ) {
                $attributes['readonly'] = 'readonly';
            }
            
            // 添加禁用属性
            if ( ! empty( $this->field['disabled'] ) ) {
                $attributes['disabled'] = 'disabled';
            }
            
            return $attributes;
        }
        
        /**
         * 获取字段配置示例
         * 
         * 返回文本字段的配置示例，用于文档和开发参考。
         * 
         * @since 1.0
         * 
         * @return array 配置示例数组
         */
        public static function get_config_example() {
            
            return array(
                'id'          => 'text_field_example',
                'type'        => 'text',
                'title'       => '文本字段示例',
                'desc'        => '这是一个文本输入字段的描述',
                'placeholder' => '请输入文本...',
                'default'     => '默认值',
                'attributes'  => array(
                    'type'      => 'text', // text, email, url, password, tel, number
                    'maxlength' => 100,
                ),
                'max_length'  => 100,
                'min_length'  => 5,
                'required'    => false,
                'readonly'    => false,
                'disabled'    => false,
                'class'       => 'custom-class',
                'before'      => '前置内容',
                'after'       => '后置内容',
                'help'        => '帮助信息',
            );
        }
    }
}
