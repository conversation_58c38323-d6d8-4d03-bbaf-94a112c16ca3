/**
 * XUN Repeater Field JavaScript
 * 
 * 现代化的重复器字段交互逻辑
 * 支持拖拽排序、折叠展开、批量操作、键盘导航等高级功能
 * 比CSF更好的用户体验
 * 
 * @since 1.0.0
 * @version 1.0.0
 */

(function($) {
    'use strict';

    /**
     * Repeater Field 类
     */
    var XunRepeaterField = {
        
        /**
         * 初始化
         */
        init: function() {
            this.bindEvents();
            this.initializeFields();
            this.initSortable();
        },

        /**
         * 绑定事件
         */
        bindEvents: function() {
            var self = this;

            // 添加项目
            $(document).on('click', '.xun-repeater-add', function(e) {
                e.preventDefault();
                self.addItem($(this));
            });

            // 删除项目
            $(document).on('click', '.xun-repeater-remove', function(e) {
                e.preventDefault();
                self.removeItem($(this));
            });

            // 复制项目
            $(document).on('click', '.xun-repeater-clone', function(e) {
                e.preventDefault();
                self.cloneItem($(this));
            });

            // 折叠/展开项目 - 点击图标
            $(document).on('click', '.xun-repeater-toggle', function(e) {
                e.preventDefault();
                self.toggleItem($(this));
            });

            // 折叠/展开项目 - 点击标题栏（无障碍访问）
            $(document).on('click', '.xun-repeater-item-header', function(e) {
                // 如果点击的是按钮、SVG图标或其子元素，不触发展开/折叠
                if ($(e.target).is('button, input, select, textarea, a, svg, path') ||
                    $(e.target).closest('button').length > 0 ||
                    $(e.target).closest('.xun-repeater-toggle, .xun-repeater-remove, .xun-repeater-clone').length > 0) {
                    return;
                }
                e.preventDefault();
                var $toggleBtn = $(this).find('.xun-repeater-toggle');
                if ($toggleBtn.length) {
                    self.toggleItem($toggleBtn);
                }
            });

            // 键盘导航支持 - 项目标题栏
            $(document).on('keydown', '.xun-repeater-item-header', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    var $toggleBtn = $(this).find('.xun-repeater-toggle');
                    if ($toggleBtn.length) {
                        self.toggleItem($toggleBtn);
                    }
                }
            });

            // 全部折叠
            $(document).on('click', '.xun-repeater-collapse-all', function(e) {
                e.preventDefault();
                self.collapseAll($(this));
            });

            // 全部展开
            $(document).on('click', '.xun-repeater-expand-all', function(e) {
                e.preventDefault();
                self.expandAll($(this));
            });

            // 清空全部
            $(document).on('click', '.xun-repeater-clear-all', function(e) {
                e.preventDefault();
                self.clearAll($(this));
            });

            // 键盘快捷键
            $(document).on('keydown', '.xun-repeater-field', function(e) {
                self.handleKeyboardShortcuts(e, $(this));
            });

            // 监听输入框变化，实时更新标题
            $(document).on('input change', '.xun-repeater-item input, .xun-repeater-item textarea, .xun-repeater-item select', function() {
                self.updateItemTitle($(this));
            });

            // 拖拽开始
            $(document).on('sortstart', '.xun-repeater-items', function(e, ui) {
                self.onSortStart(ui);
            });

            // 拖拽结束
            $(document).on('sortstop', '.xun-repeater-items', function(e, ui) {
                self.onSortStop(ui);
            });

            // 拖拽更新
            $(document).on('sortupdate', '.xun-repeater-items', function(e, ui) {
                self.onSortUpdate($(this));
            });
        },

        /**
         * 初始化所有字段
         */
        initializeFields: function() {
            $('.xun-repeater-field').each(function() {
                var $field = $(this);
                XunRepeaterField.updateFieldState($field);
                XunRepeaterField.updateItemNumbers($field);
                XunRepeaterField.initializeAccessibility($field);

                // 初始化所有项目的标题
                $field.find('.xun-repeater-item').each(function() {
                    var $item = $(this);
                    var previewField = $field.data('preview-field') || '';
                    if (previewField) {
                        var $previewInput = $item.find('input[name*="[' + previewField + ']"], textarea[name*="[' + previewField + ']"], select[name*="[' + previewField + ']"]').first();
                        if ($previewInput.length > 0) {
                            XunRepeaterField.updateItemTitle($previewInput);
                        }
                    }
                });
            });
        },

        /**
         * 初始化无障碍访问属性
         */
        initializeAccessibility: function($field) {
            $field.find('.xun-repeater-item').each(function() {
                var $item = $(this);
                var $header = $item.find('.xun-repeater-item-header');
                var $content = $item.find('.xun-repeater-item-content');
                var $toggleBtn = $item.find('.xun-repeater-toggle');
                var isCollapsed = $content.is(':hidden');

                // 为标题栏添加ARIA属性
                $header.attr({
                    'role': 'button',
                    'tabindex': '0',
                    'aria-expanded': isCollapsed ? 'false' : 'true',
                    'aria-label': '展开或折叠此项目'
                });

                // 为切换按钮添加ARIA属性
                $toggleBtn.attr({
                    'aria-expanded': isCollapsed ? 'false' : 'true',
                    'aria-label': '展开或折叠'
                });

                // 为内容区域添加ARIA属性
                $content.attr({
                    'aria-hidden': isCollapsed ? 'true' : 'false'
                });
            });
        },

        /**
         * 初始化拖拽排序
         */
        initSortable: function() {
            $('.xun-repeater-items').each(function() {
                var $container = $(this);
                var $field = $container.closest('.xun-repeater-field');
                var sortable = $field.data('sortable');

                if (sortable) {
                    $container.sortable({
                        handle: '.xun-repeater-sort-handle',
                        placeholder: 'xun-repeater-placeholder bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg',
                        tolerance: 'pointer',
                        cursor: 'move',
                        opacity: 0.9,
                        distance: 5,
                        scroll: true,
                        scrollSensitivity: 100,
                        scrollSpeed: 20,
                        cursorAt: {
                            left: 10,
                            top: 10
                        },
                        helper: 'clone'
                    });
                }
            });
        },

        /**
         * 添加项目
         */
        addItem: function($button) {
            var $field = $button.closest('.xun-repeater-field');
            var $container = $field.find('.xun-repeater-items');
            var $template = $field.find('.xun-repeater-template');
            var max = parseInt($field.data('max')) || 0;
            var currentCount = $container.children('.xun-repeater-item').length;

            // 检查最大数量限制
            if (max > 0 && currentCount >= max) {
                this.showAlert($field, 'max');
                return;
            }

            // 克隆模板
            var $newItem = $template.children().first().clone();
            var newIndex = currentCount;

            // 替换模板变量
            $newItem = this.replaceTemplateVars($newItem, newIndex);

            // 直接添加，无动画
            $newItem.appendTo($container);

            // 初始化新项目的无障碍访问属性
            this.initializeAccessibility($newItem.closest('.xun-repeater-field'));

            // 聚焦第一个输入框
            var $firstInput = $newItem.find('input, textarea, select').first();
            $firstInput.focus();

            // 如果第一个输入框是preview_field，立即更新标题
            setTimeout(() => {
                this.updateItemTitle($firstInput);
            }, 100);

            // 更新字段状态
            this.updateFieldState($field);

            // 先更新项目编号，但不影响已有的自定义标题
            this.updateItemNumbers($field);

            // 触发自定义事件
            this.triggerEvent($field, 'item:added', { item: $newItem, index: newIndex });
        },

        /**
         * 删除项目
         */
        removeItem: function($button) {
            var $item = $button.closest('.xun-repeater-item');
            var $field = $item.closest('.xun-repeater-field');
            var min = parseInt($field.data('min')) || 0;
            var currentCount = $field.find('.xun-repeater-items .xun-repeater-item').length;

            // 检查最小数量限制
            if (min > 0 && currentCount <= min) {
                this.showAlert($field, 'min');
                return;
            }

            // 确认删除
            if (window.xunRepeater && window.xunRepeater.confirmDelete) {
                if (!confirm(window.xunRepeater.confirmDelete)) {
                    return;
                }
            }

            var index = $item.data('index');

            // 直接删除，无动画
            $item.remove();
            this.updateFieldState($field);
            this.updateItemNumbers($field);

            // 触发自定义事件
            this.triggerEvent($field, 'item:removed', { index: index });
        },

        /**
         * 复制项目
         */
        cloneItem: function($button) {
            var $item = $button.closest('.xun-repeater-item');
            var $field = $item.closest('.xun-repeater-field');
            var $container = $field.find('.xun-repeater-items');
            var max = parseInt($field.data('max')) || 0;
            var currentCount = $container.children('.xun-repeater-item').length;

            // 检查最大数量限制
            if (max > 0 && currentCount >= max) {
                this.showAlert($field, 'max');
                return;
            }

            // 克隆项目
            var $clonedItem = $item.clone();
            var newIndex = currentCount;

            // 更新索引和名称
            this.updateItemIndex($clonedItem, newIndex);

            // 清除某些字段的值（如文件上传）
            $clonedItem.find('input[type="file"]').val('');
            $clonedItem.find('.media-preview').empty();

            // 插入到当前项目后面
            $clonedItem.hide().insertAfter($item).slideDown(300);

            // 初始化新项目的无障碍访问属性
            this.initializeAccessibility($field);

            // 更新字段状态
            this.updateFieldState($field);
            this.updateItemNumbers($field);

            // 触发自定义事件
            this.triggerEvent($field, 'item:cloned', { 
                original: $item, 
                clone: $clonedItem, 
                index: newIndex 
            });
        },

        /**
         * 折叠/展开项目
         */
        toggleItem: function($button) {
            var $item = $button.closest('.xun-repeater-item');
            var $content = $item.find('.xun-repeater-item-content');
            var $icon = $button.find('svg');
            var $header = $item.find('.xun-repeater-item-header');
            var isCollapsed = $content.is(':hidden');

            if (isCollapsed) {
                // 展开
                $content.slideDown(200);
                $icon.removeClass('rotate-180');
                $item.removeClass('collapsed');

                // 更新ARIA属性
                $button.attr('aria-expanded', 'true');
                $header.attr('aria-expanded', 'true');
                $content.attr('aria-hidden', 'false');
            } else {
                // 折叠
                $content.slideUp(200);
                $icon.addClass('rotate-180');
                $item.addClass('collapsed');

                // 更新ARIA属性
                $button.attr('aria-expanded', 'false');
                $header.attr('aria-expanded', 'false');
                $content.attr('aria-hidden', 'true');
            }

            // 触发自定义事件
            this.triggerEvent($item.closest('.xun-repeater-field'), 'item:toggled', {
                item: $item,
                collapsed: !isCollapsed
            });
        },

        /**
         * 全部折叠
         */
        collapseAll: function($button) {
            var $field = $button.closest('.xun-repeater-field');
            var $items = $field.find('.xun-repeater-item');

            $items.each(function() {
                var $item = $(this);
                var $content = $item.find('.xun-repeater-item-content');
                var $icon = $item.find('.xun-repeater-toggle svg');

                if ($content.is(':visible')) {
                    $content.slideUp(200);
                    $icon.addClass('rotate-180');
                    $item.addClass('collapsed');
                }
            });

            // 触发自定义事件
            this.triggerEvent($field, 'items:collapsed');
        },

        /**
         * 全部展开
         */
        expandAll: function($button) {
            var $field = $button.closest('.xun-repeater-field');
            var $items = $field.find('.xun-repeater-item');

            $items.each(function() {
                var $item = $(this);
                var $content = $item.find('.xun-repeater-item-content');
                var $icon = $item.find('.xun-repeater-toggle svg');

                if ($content.is(':hidden')) {
                    $content.slideDown(200);
                    $icon.removeClass('rotate-180');
                    $item.removeClass('collapsed');
                }
            });

            // 触发自定义事件
            this.triggerEvent($field, 'items:expanded');
        },

        /**
         * 清空全部
         */
        clearAll: function($button) {
            var $field = $button.closest('.xun-repeater-field');
            var min = parseInt($field.data('min')) || 0;

            // 确认清空
            if (window.xunRepeater && window.xunRepeater.confirmClear) {
                if (!confirm(window.xunRepeater.confirmClear)) {
                    return;
                }
            }

            var $items = $field.find('.xun-repeater-item');
            var itemsToRemove = $items.length - min;

            if (itemsToRemove <= 0) {
                this.showAlert($field, 'min');
                return;
            }

            // 删除项目（保留最小数量）
            $items.slice(min).each(function(index) {
                var $item = $(this);
                setTimeout(function() {
                    $item.slideUp(300, function() {
                        $item.remove();
                        if (index === itemsToRemove - 1) {
                            XunRepeaterField.updateFieldState($field);
                            XunRepeaterField.updateItemNumbers($field);
                        }
                    });
                }, index * 100);
            });

            // 触发自定义事件
            this.triggerEvent($field, 'items:cleared');
        },

        /**
         * 处理键盘快捷键
         */
        handleKeyboardShortcuts: function(e, $field) {
            // Ctrl/Cmd + Enter: 添加新项目
            if ((e.ctrlKey || e.metaKey) && e.which === 13) {
                e.preventDefault();
                $field.find('.xun-repeater-add').click();
            }

            // Ctrl/Cmd + D: 复制当前项目
            if ((e.ctrlKey || e.metaKey) && e.which === 68) {
                var $focusedItem = $(e.target).closest('.xun-repeater-item');
                if ($focusedItem.length) {
                    e.preventDefault();
                    $focusedItem.find('.xun-repeater-clone').click();
                }
            }

            // Delete: 删除当前项目
            if (e.which === 46 && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                var $focusedItem = $(e.target).closest('.xun-repeater-item');
                if ($focusedItem.length) {
                    e.preventDefault();
                    $focusedItem.find('.xun-repeater-remove').click();
                }
            }
        },

        /**
         * 拖拽开始
         */
        onSortStart: function(ui) {
            ui.item.addClass('xun-repeater-dragging');
            ui.placeholder.height(ui.item.outerHeight());
        },

        /**
         * 拖拽结束
         */
        onSortStop: function(ui) {
            ui.item.removeClass('xun-repeater-dragging');
        },

        /**
         * 拖拽更新
         */
        onSortUpdate: function($container) {
            var $field = $container.closest('.xun-repeater-field');
            
            // 更新项目索引
            $container.children('.xun-repeater-item').each(function(index) {
                XunRepeaterField.updateItemIndex($(this), index);
            });

            // 更新项目编号
            this.updateItemNumbers($field);

            // 触发自定义事件
            this.triggerEvent($field, 'items:sorted');
        },

        /**
         * 替换模板变量
         */
        replaceTemplateVars: function($element, index) {
            var html = $element.prop('outerHTML');
            html = html.replace(/\{\{INDEX\}\}/g, index);
            html = html.replace(/\{\{NUMBER\}\}/g, index + 1);
            html = html.replace(/___/g, '');
            
            var $newElement = $(html);
            $element.replaceWith($newElement);
            return $newElement;
        },

        /**
         * 更新项目标题
         */
        updateItemTitle: function($input) {
            var $item = $input.closest('.xun-repeater-item');
            var $field = $item.closest('.xun-repeater-field');
            var $titleSpan = $item.find('.xun-repeater-item-title');

            if ($titleSpan.length === 0) return;

            // 获取preview_field配置
            var previewField = $field.data('preview-field') || '';
            if (!previewField) return;

            // 检查当前输入框是否是preview_field
            var inputName = $input.attr('name') || '';
            if (inputName.indexOf('[' + previewField + ']') === -1) return;

            // 获取输入值
            var inputValue = $input.val() || '';
            var defaultTitle = $titleSpan.attr('data-default-title') || $titleSpan.data('default-title') || '项目';

            // 更新标题
            if (inputValue.trim() !== '') {
                // 限制标题长度，避免过长
                var trimmedValue = inputValue.length > 30 ? inputValue.substring(0, 30) + '...' : inputValue;
                $titleSpan.text(trimmedValue);
            } else {
                $titleSpan.text(defaultTitle);
            }
        },

        /**
         * 更新项目索引
         */
        updateItemIndex: function($item, newIndex) {
            $item.attr('data-index', newIndex);
            
            // 更新表单字段名称
            $item.find('input, textarea, select').each(function() {
                var $input = $(this);
                var name = $input.attr('name');
                if (name) {
                    // 替换索引
                    name = name.replace(/\[\d+\]/, '[' + newIndex + ']');
                    $input.attr('name', name);
                }
                
                var id = $input.attr('id');
                if (id) {
                    // 替换ID中的索引
                    id = id.replace(/_\d+_/, '_' + newIndex + '_');
                    $input.attr('id', id);
                }
            });

            // 更新标签的for属性
            $item.find('label').each(function() {
                var $label = $(this);
                var forAttr = $label.attr('for');
                if (forAttr) {
                    forAttr = forAttr.replace(/_\d+_/, '_' + newIndex + '_');
                    $label.attr('for', forAttr);
                }
            });
        },

        /**
         * 更新项目编号
         */
        updateItemNumbers: function($field) {
            var previewField = $field.data('preview-field') || '';

            $field.find('.xun-repeater-item').each(function(index) {
                var $item = $(this);
                var $titleSpan = $item.find('.xun-repeater-item-title');
                var newDefaultTitle = '项目 #' + (index + 1);

                if ($titleSpan.length > 0) {
                    // 更新默认标题属性
                    $titleSpan.attr('data-default-title', newDefaultTitle);

                    // 只有当前显示的是默认标题时，才更新显示文本
                    var currentText = $titleSpan.text();
                    var oldDefaultTitle = $titleSpan.data('default-title') || '';

                    // 如果当前显示的是旧的默认标题，或者是模板标记，则更新为新的默认标题
                    if (currentText === oldDefaultTitle || currentText.indexOf('{{NUMBER}}') !== -1) {
                        $titleSpan.text(newDefaultTitle);
                    }

                    // 如果有preview_field但当前显示的是默认标题，检查是否有输入值
                    if (previewField && currentText === newDefaultTitle) {
                        var $previewInput = $item.find('input[name*="[' + previewField + ']"], textarea[name*="[' + previewField + ']"], select[name*="[' + previewField + ']"]').first();
                        if ($previewInput.length > 0 && $previewInput.val().trim() !== '') {
                            this.updateItemTitle($previewInput);
                        }
                    }
                } else {
                    // 兼容旧版本，如果没有找到title span，则查找旧的选择器
                    $item.find('.xun-repeater-item-header .text-sm').first().text(newDefaultTitle);
                }
            }.bind(this));
        },

        /**
         * 更新字段状态
         */
        updateFieldState: function($field) {
            var $container = $field.find('.xun-repeater-items');
            var $emptyState = $field.find('.xun-repeater-empty-state');
            var $addButton = $field.find('.xun-repeater-add');
            var $count = $field.find('.xun-repeater-count');
            
            var itemCount = $container.children('.xun-repeater-item').length;
            var max = parseInt($field.data('max')) || 0;
            var min = parseInt($field.data('min')) || 0;

            // 更新计数
            $count.text(itemCount);

            // 显示/隐藏空状态
            if (itemCount === 0) {
                $emptyState.removeClass('hidden');
                $container.addClass('hidden');
            } else {
                $emptyState.addClass('hidden');
                $container.removeClass('hidden');
            }

            // 控制添加按钮状态
            if (max > 0 && itemCount >= max) {
                $addButton.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');
                this.showAlert($field, 'max');
            } else {
                $addButton.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
                this.hideAlert($field, 'max');
            }

            // 控制删除按钮状态
            $field.find('.xun-repeater-remove').each(function() {
                if (min > 0 && itemCount <= min) {
                    $(this).addClass('opacity-50 cursor-not-allowed').prop('disabled', true);
                } else {
                    $(this).removeClass('opacity-50 cursor-not-allowed').prop('disabled', false);
                }
            });

            // 触发自定义事件
            this.triggerEvent($field, 'state:updated', { count: itemCount });
        },

        /**
         * 显示提示
         */
        showAlert: function($field, type) {
            var $alert = $field.find('.xun-repeater-' + type + '-alert');
            if ($alert.length && $alert.hasClass('hidden')) {
                $alert.removeClass('hidden').hide().slideDown(200);
            }
        },

        /**
         * 隐藏提示
         */
        hideAlert: function($field, type) {
            var $alert = $field.find('.xun-repeater-' + type + '-alert');
            if ($alert.length && !$alert.hasClass('hidden')) {
                $alert.slideUp(200, function() {
                    $alert.addClass('hidden');
                });
            }
        },

        /**
         * 触发自定义事件
         */
        triggerEvent: function($field, eventName, data) {
            var fieldId = $field.data('field-id');
            $field.trigger('xun:repeater:' + eventName, $.extend({
                fieldId: fieldId
            }, data || {}));
        },

        /**
         * 重新初始化字段（用于动态添加的字段）
         */
        reinit: function() {
            this.initializeFields();
            this.initSortable();
        },

        /**
         * 获取字段数据
         */
        getData: function(fieldId) {
            var $field = $('.xun-repeater-field[data-field-id="' + fieldId + '"]');
            var data = [];

            $field.find('.xun-repeater-item').each(function() {
                var itemData = {};
                $(this).find('input, textarea, select').each(function() {
                    var $input = $(this);
                    var name = $input.attr('name');
                    if (name) {
                        var fieldName = name.match(/\[([^\]]+)\]$/);
                        if (fieldName) {
                            itemData[fieldName[1]] = $input.val();
                        }
                    }
                });
                data.push(itemData);
            });

            return data;
        },

        /**
         * 设置字段数据
         */
        setData: function(fieldId, data) {
            var $field = $('.xun-repeater-field[data-field-id="' + fieldId + '"]');
            
            // 清空现有项目
            $field.find('.xun-repeater-item').remove();

            // 添加新项目
            if (Array.isArray(data)) {
                data.forEach(function(itemData, index) {
                    // 这里需要根据数据创建项目
                    // 实现较复杂，暂时省略
                });
            }

            this.updateFieldState($field);
        }
    };

    // 文档就绪时初始化
    $(document).ready(function() {
        XunRepeaterField.init();
    });

    // 暴露到全局
    window.XunRepeaterField = XunRepeaterField;

})(jQuery);
