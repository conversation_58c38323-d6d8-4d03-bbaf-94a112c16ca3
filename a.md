# WordPress无头主题开发规则

## 项目概述

开发一个WordPress无头主题系统，分为服务端和客户端两个版本：
- **服务端版本**：包含授权系统 + 页面构建器 + 基础功能
- **客户端版本**：包含页面构建器构建的内容渲染 + 基础功能

## 技术栈

### 后端技术
- **WordPress** 6.8.1+
- **PHP** 8.1+
- **WPGraphQL插件** - GraphQL API支持
- **自定义插件开发** - 核心功能实现

### 前端技术
- **Next.js** 14+ (App Router)
- **HeroUI** - UI组件库
- **Tailwind CSS** 4.x - 样式框架
- **TypeScript** - 类型安全
- **React** 18+ - 前端框架

### 页面构建器技术
- **@dnd-kit/core** - 拖拽引擎
- **Zustand** - 状态管理
- **React Query** - 数据获取和缓存
- **Monaco Editor** - 代码编辑器

## 页面构建器布局

```
├── 拖拽编辑器
│   ├── 组件面板 (左侧)
│   ├── 画布区域 (中间)
│   ├── 属性面板 (右侧)
│   └── 工具栏 (顶部)
├── 组件系统
│   ├── 基础组件 (文本、图片、按钮等)
│   ├── 布局组件 (容器、栅格、分栏等)
│   ├── 高级组件 (轮播图、表单、画廊等)
│   └── 自定义组件
├── 样式系统
│   ├── 全局样式设置
│   ├── 组件样式配置
│   ├── 响应式设计
│   └── 主题色彩管理
└── 预览系统
    ├── 实时预览
    ├── 设备预览 (桌面/平板/手机)
    ├── 交互预览
    └── 性能预览
```

## 组件系统

### 基础组件
- 文本组件
- 图片组件
- 按钮组件
- 链接组件
- 图标组件

### 布局组件
- 容器组件
- 栅格系统
- 弹性布局
- 分栏组件
- 间距组件

### 高级组件
- 轮播图组件
- 表单组件
- 画廊组件
- 文章列表组件
- 导航菜单组件
- 搜索组件
- 视频组件

### 自定义组件
- 可扩展的自定义组件框架
- 支持第三方组件集成

## 样式系统

### 全局样式设置
- 主题色彩管理
- 字体设置
- 间距系统
- 断点设置

### 组件样式配置
- 统一的样式配置接口
- 支持Hover、Focus等状态样式
- 支持响应式样式配置

### 响应式设计
- 移动优先的设计理念
- 支持桌面/平板/手机三种设备预览
- 灵活的断点设置

## 预览系统

### 实时预览
- 拖拽组件时实时预览
- 属性修改时实时更新
- 样式调整时实时反馈

### 设备预览
- 桌面端预览 (1200px+)
- 平板端预览 (768px-1199px)
- 手机端预览 (767px-)

### 交互预览
- 表单交互预览
- 轮播图交互预览
- 导航菜单交互预览

## 数据库要求

### 利用WordPress原生表
- 使用 `wp_posts` 表存储页面构建器数据
- 使用 `wp_postmeta` 表存储扩展配置
- 使用 `wp_options` 表存储全局配置
- 使用 `wp_usermeta` 表存储用户配置

### 最小化自定义表
- 仅在必要时创建自定义表
- 优先使用WordPress原生表结构
- 保持与WordPress标准的兼容性

## 核心功能逻辑

### 服务端功能
1. **页面构建器**
   - 拖拽式组件编辑
   - 实时预览功能
   - 组件属性配置
   - 响应式设计支持
   - 布局导出功能

2. **授权系统**
   - 许可证验证
   - 用户权限管理
   - 功能访问控制
   - 安全加密机制

3. **数据管理**
   - 页面数据存储
   - 组件配置管理
   - 样式数据管理
   - 用户设置存储

### 客户端功能
1. **页面渲染**
   - 解析布局配置
   - 渲染组件结构
   - 应用样式设置
   - 获取真实数据

2. **数据获取**
   - 通过GraphQL获取WordPress数据
   - 缓存机制优化
   - 错误处理机制
   - 数据更新同步

3. **交互功能**
   - 表单提交处理
   - 搜索功能实现
   - 导航菜单交互
   - 用户界面响应

### 发布和导出
1. **发布到WordPress**
   - 加密数据传输
   - WordPress插件接收
   - 数据存储到数据库
   - 前端渲染支持

2. **导出JSON**
   - 布局数据加密
   - JSON文件生成
   - 导入功能支持
   - 版本兼容性保证

## 开发规范

### 代码规范
- 遵循WordPress编码标准
- 使用TypeScript类型检查
- 统一组件接口设计
- 严格的代码审查流程

### 安全规范
- 数据验证和过滤
- 权限控制系统
- 加密传输机制
- XSS和CSRF防护

### 性能规范
- 组件懒加载
- 数据缓存策略
- 图片懒加载
- 代码分割优化