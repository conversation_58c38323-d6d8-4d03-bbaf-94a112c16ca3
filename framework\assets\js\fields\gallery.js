/**
 * Xun Framework Gallery Field JavaScript
 * 
 * 现代化图片库字段的JavaScript功能实现，提供比Codestar Framework更优秀的用户体验。
 * 
 * 功能特性：
 * - WordPress媒体库集成
 * - 拖拽排序
 * - 批量操作
 * - 图片预览
 * - 响应式设计
 * - 键盘导航
 * - 无障碍访问
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

(function($) {
    'use strict';

    /**
     * XunGalleryField 类
     * 
     * 管理图片库字段的所有交互功能
     */
    class XunGalleryField {
        
        /**
         * 构造函数
         * 
         * @param {jQuery} $container 字段容器元素
         */
        constructor($container) {
            this.$container = $container;
            this.$input = $container.find('.xun-gallery-input');
            this.$galleryContainer = $container.find('.xun-gallery-container');
            this.$grid = $container.find('.xun-gallery-grid');
            this.$emptyState = $container.find('.xun-gallery-empty-state');
            this.$uploadArea = $container.find('.xun-gallery-upload-area');
            this.$loading = $container.find('.xun-gallery-loading');
            this.$toolbar = $container.find('.xun-gallery-toolbar');
            
            // 解析配置
            this.config = this.parseConfig();
            
            // 当前图片ID数组
            this.imageIds = this.parseImageIds();
            
            // 批量模式状态
            this.batchMode = false;
            
            // WordPress媒体库实例
            this.mediaFrame = null;
            
            // 初始化
            this.init();
        }
        
        /**
         * 初始化字段
         */
        init() {
            this.bindEvents();
            this.initSortable();
            this.updateUI();
            this.setupKeyboardNavigation();
        }
        
        /**
         * 解析字段配置
         * 
         * @returns {Object} 配置对象
         */
        parseConfig() {
            const $configScript = this.$container.find('.xun-gallery-config');
            if ($configScript.length > 0) {
                try {
                    return JSON.parse($configScript.text());
                } catch (e) {
                    console.error('Gallery field config parse error:', e);
                }
            }
            
            // 默认配置
            return {
                maxFiles: 0,
                minFiles: 0,
                allowedTypes: ['image'],
                maxFileSize: 0,
                enableSorting: true,
                enableBatch: true,
                enablePreview: true,
                previewSize: 'thumbnail',
                gridColumns: { sm: 2, md: 3, lg: 4, xl: 5 },
                messages: {
                    errorMaxFiles: '最多只能上传 {max} 张图片',
                    errorMinFiles: '至少需要上传 {min} 张图片',
                    errorFileType: '不支持的文件类型',
                    errorFileSize: '文件大小超出限制',
                    confirmClear: '确定要清空图片库吗？此操作不可撤销。',
                    confirmDelete: '确定要删除选中的图片吗？此操作不可撤销。',
                    confirmRemove: '确定要移除这张图片吗？'
                }
            };
        }
        
        /**
         * 解析图片ID数组
         * 
         * @returns {Array} 图片ID数组
         */
        parseImageIds() {
            const value = this.$input.val();
            if (!value) return [];
            
            return value.split(',').map(id => parseInt(id.trim())).filter(id => id > 0);
        }
        
        /**
         * 绑定事件
         */
        bindEvents() {
            // 添加图片按钮
            this.$container.on('click', '.xun-gallery-add-btn', (e) => {
                e.preventDefault();
                this.openMediaLibrary();
            });
            
            // 编辑图片库按钮
            this.$container.on('click', '.xun-gallery-edit-btn', (e) => {
                e.preventDefault();
                this.openMediaLibrary(true);
            });
            
            // 清空图片库按钮
            this.$container.on('click', '.xun-gallery-clear-btn', (e) => {
                e.preventDefault();
                this.clearGallery();
            });
            
            // 移除单张图片
            this.$container.on('click', '.xun-gallery-remove-btn', (e) => {
                e.preventDefault();
                const imageId = parseInt($(e.currentTarget).data('image-id'));
                this.removeImage(imageId);
            });
            
            // 图片预览
            this.$container.on('click', '.xun-gallery-preview-btn', (e) => {
                e.preventDefault();
                const imageUrl = $(e.currentTarget).data('image-url');
                const imageTitle = $(e.currentTarget).data('image-title');
                this.previewImage(imageUrl, imageTitle);
            });
            
            // 批量操作相关事件
            if (this.config.enableBatch) {
                this.bindBatchEvents();
            }
            
            // 拖拽上传
            this.bindDragDropEvents();
        }
        
        /**
         * 绑定批量操作事件
         */
        bindBatchEvents() {
            // 批量模式切换
            this.$container.on('click', '.xun-gallery-batch-toggle-btn', (e) => {
                e.preventDefault();
                this.toggleBatchMode();
            });
            
            // 全选
            this.$container.on('click', '.xun-gallery-select-all-btn', (e) => {
                e.preventDefault();
                this.selectAll();
            });
            
            // 取消全选
            this.$container.on('click', '.xun-gallery-deselect-all-btn', (e) => {
                e.preventDefault();
                this.deselectAll();
            });
            
            // 删除选中
            this.$container.on('click', '.xun-gallery-delete-selected-btn', (e) => {
                e.preventDefault();
                this.deleteSelected();
            });
            
            // 复选框变化
            this.$container.on('change', '.xun-gallery-item-checkbox', () => {
                this.updateBatchControls();
            });
        }
        
        /**
         * 绑定拖拽上传事件
         */
        bindDragDropEvents() {
            const $dropZone = this.$uploadArea.length > 0 ? this.$uploadArea : this.$container;
            
            $dropZone.on('dragover dragenter', (e) => {
                e.preventDefault();
                e.stopPropagation();
                $dropZone.addClass('drag-over');
            });
            
            $dropZone.on('dragleave dragend', (e) => {
                e.preventDefault();
                e.stopPropagation();
                $dropZone.removeClass('drag-over');
            });
            
            $dropZone.on('drop', (e) => {
                e.preventDefault();
                e.stopPropagation();
                $dropZone.removeClass('drag-over');
                
                const files = e.originalEvent.dataTransfer.files;
                if (files.length > 0) {
                    this.handleFileUpload(files);
                }
            });
        }
        
        /**
         * 初始化拖拽排序
         */
        initSortable() {
            if (!this.config.enableSorting || !this.$grid.length) return;
            
            this.$grid.sortable({
                items: '.xun-gallery-item',
                handle: '.xun-gallery-drag-handle',
                placeholder: 'xun-gallery-placeholder',
                tolerance: 'pointer',
                cursor: 'grabbing',
                opacity: 0.8,
                start: (event, ui) => {
                    ui.placeholder.height(ui.item.height());
                    ui.placeholder.addClass('border-2 border-dashed border-indigo-300 bg-indigo-50 rounded-lg');
                },
                update: () => {
                    this.updateImageOrder();
                }
            });
        }
        
        /**
         * 设置键盘导航
         */
        setupKeyboardNavigation() {
            this.$container.on('keydown', '.xun-gallery-item', (e) => {
                const $item = $(e.currentTarget);
                const $items = this.$container.find('.xun-gallery-item');
                const currentIndex = $items.index($item);
                
                switch (e.key) {
                    case 'ArrowRight':
                    case 'ArrowDown':
                        e.preventDefault();
                        const nextIndex = (currentIndex + 1) % $items.length;
                        $items.eq(nextIndex).focus();
                        break;
                        
                    case 'ArrowLeft':
                    case 'ArrowUp':
                        e.preventDefault();
                        const prevIndex = currentIndex === 0 ? $items.length - 1 : currentIndex - 1;
                        $items.eq(prevIndex).focus();
                        break;
                        
                    case 'Delete':
                    case 'Backspace':
                        e.preventDefault();
                        const imageId = parseInt($item.data('image-id'));
                        this.removeImage(imageId);
                        break;
                        
                    case 'Enter':
                    case ' ':
                        e.preventDefault();
                        $item.find('.xun-gallery-preview-btn').click();
                        break;
                }
            });
        }

        /**
         * 打开WordPress媒体库
         *
         * @param {boolean} editMode 是否为编辑模式
         */
        openMediaLibrary(editMode = false) {
            // 创建媒体库实例
            if (!this.mediaFrame) {
                this.mediaFrame = wp.media({
                    title: xunGalleryField.i18n.mediaTitle,
                    button: {
                        text: xunGalleryField.i18n.mediaButtonText
                    },
                    multiple: true,
                    library: {
                        type: this.config.allowedTypes
                    }
                });

                // 选择图片后的回调
                this.mediaFrame.on('select', () => {
                    const selection = this.mediaFrame.state().get('selection');
                    const newImageIds = [];

                    selection.each((attachment) => {
                        newImageIds.push(attachment.get('id'));
                    });

                    if (editMode) {
                        this.setImages(newImageIds);
                    } else {
                        this.addImages(newImageIds);
                    }
                });
            }

            // 如果是编辑模式，预选当前图片
            if (editMode && this.imageIds.length > 0) {
                const selection = this.mediaFrame.state().get('selection');
                selection.reset();

                this.imageIds.forEach(imageId => {
                    const attachment = wp.media.attachment(imageId);
                    attachment.fetch();
                    selection.add(attachment);
                });
            }

            // 打开媒体库
            this.mediaFrame.open();
        }

        /**
         * 添加图片到图片库
         *
         * @param {Array} newImageIds 新图片ID数组
         */
        addImages(newImageIds) {
            if (!Array.isArray(newImageIds) || newImageIds.length === 0) return;

            // 检查最大文件数限制
            if (this.config.maxFiles > 0) {
                const totalCount = this.imageIds.length + newImageIds.length;
                if (totalCount > this.config.maxFiles) {
                    const allowedCount = this.config.maxFiles - this.imageIds.length;
                    if (allowedCount <= 0) {
                        this.showError(this.config.messages.errorMaxFiles.replace('{max}', this.config.maxFiles));
                        return;
                    }
                    newImageIds = newImageIds.slice(0, allowedCount);
                    this.showWarning(`只能再添加 ${allowedCount} 张图片，已自动截取前 ${allowedCount} 张。`);
                }
            }

            // 过滤重复图片
            const uniqueIds = newImageIds.filter(id => !this.imageIds.includes(id));

            if (uniqueIds.length === 0) {
                this.showWarning('所选图片已存在于图片库中。');
                return;
            }

            // 添加到图片ID数组
            this.imageIds = this.imageIds.concat(uniqueIds);

            // 更新UI
            this.updateValue();
            this.renderImages();
            this.updateUI();

            this.showSuccess(`成功添加 ${uniqueIds.length} 张图片。`);
        }

        /**
         * 设置图片库（替换所有图片）
         *
         * @param {Array} imageIds 图片ID数组
         */
        setImages(imageIds) {
            if (!Array.isArray(imageIds)) return;

            // 检查最大文件数限制
            if (this.config.maxFiles > 0 && imageIds.length > this.config.maxFiles) {
                imageIds = imageIds.slice(0, this.config.maxFiles);
                this.showWarning(`最多只能选择 ${this.config.maxFiles} 张图片，已自动截取前 ${this.config.maxFiles} 张。`);
            }

            this.imageIds = imageIds;

            // 更新UI
            this.updateValue();
            this.renderImages();
            this.updateUI();

            this.showSuccess(`图片库已更新，共 ${imageIds.length} 张图片。`);
        }

        /**
         * 移除单张图片
         *
         * @param {number} imageId 图片ID
         */
        removeImage(imageId) {
            if (!confirm(this.config.messages.confirmRemove)) return;

            const index = this.imageIds.indexOf(imageId);
            if (index > -1) {
                this.imageIds.splice(index, 1);

                // 更新UI
                this.updateValue();
                this.renderImages();
                this.updateUI();

                this.showSuccess('图片已移除。');
            }
        }

        /**
         * 清空图片库
         */
        clearGallery() {
            if (!confirm(this.config.messages.confirmClear)) return;

            this.imageIds = [];

            // 更新UI
            this.updateValue();
            this.renderImages();
            this.updateUI();

            this.showSuccess('图片库已清空。');
        }

        /**
         * 更新图片顺序（拖拽排序后调用）
         */
        updateImageOrder() {
            const newOrder = [];
            this.$grid.find('.xun-gallery-item').each(function() {
                const imageId = parseInt($(this).data('image-id'));
                if (imageId > 0) {
                    newOrder.push(imageId);
                }
            });

            this.imageIds = newOrder;
            this.updateValue();
        }

        /**
         * 切换批量模式
         */
        toggleBatchMode() {
            this.batchMode = !this.batchMode;

            if (this.batchMode) {
                this.$container.addClass('batch-mode');
                this.$container.find('.xun-gallery-batch-controls').removeClass('hidden');
                this.$container.find('.xun-gallery-batch-toggle-btn').text('退出批量');
            } else {
                this.$container.removeClass('batch-mode');
                this.$container.find('.xun-gallery-batch-controls').addClass('hidden');
                this.$container.find('.xun-gallery-batch-toggle-btn').text('批量操作');
                this.deselectAll();
            }
        }

        /**
         * 全选图片
         */
        selectAll() {
            this.$container.find('.xun-gallery-item-checkbox').prop('checked', true);
            this.updateBatchControls();
        }

        /**
         * 取消全选
         */
        deselectAll() {
            this.$container.find('.xun-gallery-item-checkbox').prop('checked', false);
            this.updateBatchControls();
        }

        /**
         * 删除选中的图片
         */
        deleteSelected() {
            const selectedIds = [];
            this.$container.find('.xun-gallery-item-checkbox:checked').each(function() {
                selectedIds.push(parseInt($(this).val()));
            });

            if (selectedIds.length === 0) {
                this.showWarning('请先选择要删除的图片。');
                return;
            }

            if (!confirm(this.config.messages.confirmDelete)) return;

            // 从图片ID数组中移除选中的图片
            this.imageIds = this.imageIds.filter(id => !selectedIds.includes(id));

            // 更新UI
            this.updateValue();
            this.renderImages();
            this.updateUI();

            this.showSuccess(`成功删除 ${selectedIds.length} 张图片。`);
        }

        /**
         * 更新批量操作控件状态
         */
        updateBatchControls() {
            const $checkboxes = this.$container.find('.xun-gallery-item-checkbox');
            const $checked = $checkboxes.filter(':checked');

            // 更新按钮状态
            this.$container.find('.xun-gallery-delete-selected-btn').toggleClass('opacity-50 cursor-not-allowed', $checked.length === 0);
        }

        /**
         * 预览图片
         *
         * @param {string} imageUrl 图片URL
         * @param {string} imageTitle 图片标题
         */
        previewImage(imageUrl, imageTitle) {
            // 创建模态框
            const modal = $(`
                <div class="xun-gallery-preview-modal fixed inset-0 flex items-center justify-center bg-black bg-opacity-50" style="z-index: 999999;">
                    <div class="relative max-w-4xl max-h-full p-4">
                        <button type="button" class="absolute -top-12 right-0 p-2 bg-opacity-20 text-white rounded-full hover:bg-opacity-30 transition-all duration-200">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                        <img src="${imageUrl}" alt="${imageTitle}" class="max-w-full max-h-full object-contain rounded-lg shadow-2xl">
                        ${imageTitle ? `<div class="absolute -bottom-12 left-0 right-0 p-2 text-white text-center"><p class="text-lg font-medium bg-black bg-opacity-50 rounded px-3 py-1">${imageTitle}</p></div>` : ''}
                    </div>
                </div>
            `);

            // 添加到页面
            $('body').append(modal);

            // 绑定关闭事件
            modal.on('click', function(e) {
                if (e.target === this || $(e.target).closest('button').length > 0) {
                    modal.remove();
                }
            });

            // ESC键关闭
            $(document).on('keydown.gallery-preview', function(e) {
                if (e.key === 'Escape') {
                    modal.remove();
                    $(document).off('keydown.gallery-preview');
                }
            });
        }

        /**
         * 处理文件上传（拖拽上传）
         *
         * @param {FileList} files 文件列表
         */
        handleFileUpload(files) {
            // 这里可以实现直接文件上传功能
            // 目前先打开媒体库让用户选择
            this.openMediaLibrary();
        }

        /**
         * 重新渲染图片网格
         */
        renderImages() {
            if (this.imageIds.length === 0) {
                this.$grid.empty();
                return;
            }

            // 通过AJAX获取图片信息并重新渲染
            this.loadImagesData();
        }

        /**
         * 加载图片数据并渲染
         */
        loadImagesData() {
            if (this.imageIds.length === 0) return;

            // 显示加载状态
            this.$loading.removeClass('hidden');

            // 使用WordPress的AJAX来获取图片信息
            const data = {
                action: 'xun_get_gallery_images',
                image_ids: this.imageIds,
                preview_size: this.config.previewSize || 'thumbnail',
                nonce: xunGalleryField.nonce
            };

            $.post(xunGalleryField.ajaxUrl, data)
                .done((response) => {
                    if (response.success && response.data) {
                        this.renderImageItems(response.data);
                    } else {
                        // 如果AJAX失败，使用简单的重新渲染
                        this.renderImageItemsSimple();
                    }
                })
                .fail(() => {
                    // AJAX失败时的备用方案
                    this.renderImageItemsSimple();
                })
                .always(() => {
                    this.$loading.addClass('hidden');
                });
        }

        /**
         * 渲染图片项（使用AJAX数据）
         */
        renderImageItems(imagesData) {
            this.$grid.empty();

            imagesData.forEach(imageData => {
                const $item = this.createImageItem(imageData);
                this.$grid.append($item);
            });

            // 重新初始化排序
            if (this.config.enableSorting) {
                this.initSortable();
            }
        }

        /**
         * 简单渲染图片项（备用方案）
         */
        renderImageItemsSimple() {
            this.$grid.empty();

            this.imageIds.forEach(imageId => {
                const $item = this.createImageItemSimple(imageId);
                this.$grid.append($item);
            });

            // 重新初始化排序
            if (this.config.enableSorting) {
                this.initSortable();
            }
        }

        /**
         * 创建图片项元素（使用完整数据）
         */
        createImageItem(imageData) {
            const $item = $(`
                <div class="xun-gallery-item group relative bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200" data-image-id="${imageData.id}">
                    ${this.config.enableBatch ? `
                    <div class="xun-gallery-checkbox absolute top-2 left-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                        <input type="checkbox" class="xun-gallery-item-checkbox w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-500 focus:ring-2" value="${imageData.id}">
                    </div>
                    ` : ''}

                    <div class="aspect-square relative overflow-hidden">
                        <img src="${imageData.thumbnail}" alt="${imageData.alt}" class="absolute inset-0 w-full h-full object-cover" loading="lazy">

                        <div class="absolute inset-0 bg-transparent group-hover:bg-black group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center pointer-events-none">
                            <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2 pointer-events-auto">
                                ${this.config.enablePreview ? `
                                <button type="button" class="xun-gallery-preview-btn p-2 bg-white bg-opacity-90 rounded-full text-gray-700 hover:bg-opacity-100 transition-all duration-200" data-image-url="${imageData.full}" data-image-title="${imageData.title}">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                </button>
                                ` : ''}

                                <button type="button" class="xun-gallery-remove-btn p-2 bg-red-500 bg-opacity-90 rounded-full text-white hover:bg-opacity-100 transition-all duration-200" data-image-id="${imageData.id}">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="p-3">
                        <p class="text-sm font-medium text-gray-900 truncate" title="${imageData.title}">${imageData.title}</p>
                        <p class="text-xs text-gray-500 mt-1">ID: ${imageData.id}</p>
                    </div>

                    ${this.config.enableSorting ? `
                    <div class="xun-gallery-drag-handle absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-move p-1 bg-white bg-opacity-90 rounded">
                        <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>
                        </svg>
                    </div>
                    ` : ''}
                </div>
            `);

            return $item;
        }

        /**
         * 创建简单图片项元素（备用方案）
         */
        createImageItemSimple(imageId) {
            const $item = $(`
                <div class="xun-gallery-item group relative bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200" data-image-id="${imageId}">
                    <div class="aspect-square relative overflow-hidden bg-gray-100 flex items-center justify-center">
                        <div class="absolute inset-0 flex items-center justify-center text-gray-400">
                            <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                        </div>

                        <div class="absolute inset-0 bg-transparent group-hover:bg-black group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center pointer-events-none">
                            <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2 pointer-events-auto">
                                <button type="button" class="xun-gallery-remove-btn p-2 bg-red-500 bg-opacity-90 rounded-full text-white hover:bg-opacity-100 transition-all duration-200" data-image-id="${imageId}">
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="p-3">
                        <p class="text-sm font-medium text-gray-900">图片 ID: ${imageId}</p>
                        <p class="text-xs text-gray-500 mt-1">加载中...</p>
                    </div>
                </div>
            `);

            return $item;
        }

        /**
         * 更新隐藏输入框的值
         */
        updateValue() {
            this.$input.val(this.imageIds.join(','));
            this.$input.trigger('change');
        }

        /**
         * 更新UI状态
         */
        updateUI() {
            const hasImages = this.imageIds.length > 0;

            // 切换空状态和网格显示
            this.$galleryContainer.toggleClass('xun-gallery-empty', !hasImages);
            this.$emptyState.toggle(!hasImages);
            this.$grid.toggle(hasImages);

            // 更新工具栏按钮状态
            this.$toolbar.find('.xun-gallery-edit-btn').toggle(hasImages);
            this.$toolbar.find('.xun-gallery-clear-btn').toggle(hasImages);
            this.$toolbar.find('.xun-gallery-batch-toggle-btn').toggle(hasImages && this.config.enableBatch);

            // 更新批量控件
            if (this.config.enableBatch) {
                this.updateBatchControls();
            }
        }

        /**
         * 显示成功消息
         *
         * @param {string} message 消息内容
         */
        showSuccess(message) {
            this.showNotice(message, 'success');
        }

        /**
         * 显示警告消息
         *
         * @param {string} message 消息内容
         */
        showWarning(message) {
            this.showNotice(message, 'warning');
        }

        /**
         * 显示错误消息
         *
         * @param {string} message 消息内容
         */
        showError(message) {
            this.showNotice(message, 'error');
        }

        /**
         * 显示通知消息
         *
         * @param {string} message 消息内容
         * @param {string} type 消息类型：success, warning, error
         */
        showNotice(message, type = 'info') {
            const colors = {
                success: 'bg-green-100 border-green-400 text-green-700',
                warning: 'bg-yellow-100 border-yellow-400 text-yellow-700',
                error: 'bg-red-100 border-red-400 text-red-700',
                info: 'bg-blue-100 border-blue-400 text-blue-700'
            };

            const notice = $(`
                <div class="xun-gallery-notice ${colors[type]} border px-4 py-3 rounded mb-4 relative">
                    <span class="block sm:inline">${message}</span>
                    <button type="button" class="absolute top-0 bottom-0 right-0 px-4 py-3">
                        <svg class="fill-current h-6 w-6" role="button" viewBox="0 0 20 20">
                            <path d="M14.348 14.849a1.2 1.2 0 0 1-1.697 0L10 11.819l-2.651 3.029a1.2 1.2 0 1 1-1.697-1.697l2.758-3.15-2.759-3.152a1.2 1.2 0 1 1 1.697-1.697L10 8.183l2.651-3.031a1.2 1.2 0 1 1 1.697 1.697l-2.758 3.152 2.758 3.15a1.2 1.2 0 0 1 0 1.698z"/>
                        </svg>
                    </button>
                </div>
            `);

            // 添加到容器顶部
            this.$container.prepend(notice);

            // 绑定关闭事件
            notice.find('button').on('click', function() {
                notice.remove();
            });

            // 自动消失
            setTimeout(() => {
                notice.fadeOut(300, function() {
                    $(this).remove();
                });
            }, 5000);
        }
    }

    /**
     * 初始化所有gallery字段
     */
    function initGalleryFields() {
        $('.xun-gallery-field').each(function() {
            const $container = $(this);
            if (!$container.data('xun-gallery-instance')) {
                const instance = new XunGalleryField($container);
                $container.data('xun-gallery-instance', instance);
            }
        });
    }

    /**
     * 文档就绪时初始化
     */
    $(document).ready(function() {
        initGalleryFields();

        // 监听动态添加的字段
        $(document).on('xun:field:added', function() {
            initGalleryFields();
        });
    });

    /**
     * 导出到全局作用域（用于调试）
     */
    window.XunGalleryField = XunGalleryField;

})(jQuery);
