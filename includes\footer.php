<?php
/**
 * Xun Framework 文档系统 - 通用页面底部
 *
 * 提供统一的页面底部，包括链接、版本信息和JavaScript功能。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

// 默认配置
$footer_config = array_merge([
    'site_title' => 'Xun Framework',
    'site_description' => '现代化的WordPress选项框架',
    'version' => '1.0.0',
    'total_fields' => 23,
    'show_stats' => true,
    'current_year' => date('Y')
], $footer_config ?? []);

// 快速链接配置
$quick_links = [
    ['title' => '首页', 'url' => 'index.php'],
    ['title' => '快速开始', 'url' => 'getting-started.php'],
    ['title' => '字段类型', 'url' => 'field-types.php'],
    ['title' => 'API参考', 'url' => 'api-reference.php'],
    ['title' => '示例代码', 'url' => 'examples.php'],
    ['title' => '最佳实践', 'url' => 'best-practices.php']
];

// 社区链接配置
$community_links = [
    ['title' => 'GitHub', 'url' => 'https://github.com/xuntheme/xun-framework'],
    ['title' => '问题反馈', 'url' => 'https://github.com/xuntheme/xun-framework/issues'],
    ['title' => '贡献指南', 'url' => 'https://github.com/xuntheme/xun-framework/blob/main/CONTRIBUTING.md'],
    ['title' => '更新日志', 'url' => 'https://github.com/xuntheme/xun-framework/releases'],
    ['title' => '讨论区', 'url' => 'https://github.com/xuntheme/xun-framework/discussions'],
    ['title' => '文档反馈', 'url' => 'mailto:<EMAIL>']
];

// 资源链接配置
$resource_links = [
    ['title' => 'WordPress官网', 'url' => 'https://wordpress.org'],
    ['title' => 'TailwindCSS', 'url' => 'https://tailwindcss.com'],
    ['title' => 'PHP文档', 'url' => 'https://www.php.net'],
    ['title' => '开发工具', 'url' => '#'],
    ['title' => '设计资源', 'url' => '#'],
    ['title' => '学习资料', 'url' => '#']
];
?>

        <!-- 页面底部 -->
        <footer class="bg-white border-t border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                    <!-- 框架信息 -->
                    <div class="col-span-1 md:col-span-2">
                        <div class="flex items-center space-x-3 mb-4">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                                <span class="text-white font-bold text-lg">X</span>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">
                                    <?php echo htmlspecialchars($footer_config['site_title']); ?>
                                </h3>
                                <p class="text-sm text-gray-500">
                                    <?php echo htmlspecialchars($footer_config['site_description']); ?>
                                </p>
                            </div>
                        </div>
                        <p class="text-gray-600 mb-6 max-w-md leading-relaxed">
                            基于PHP 8.1+和TailwindCSS 4.x构建，提供<?php echo $footer_config['total_fields']; ?>种字段类型，
                            支持响应式设计的WordPress选项框架。完全开源，MIT许可证。
                        </p>
                        
                        <?php if ($footer_config['show_stats']): ?>
                        <!-- 统计信息 -->
                        <div class="grid grid-cols-2 gap-4 mb-6">
                            <div class="text-center p-3 bg-blue-50 rounded-lg">
                                <div class="text-2xl font-bold text-blue-600"><?php echo $footer_config['total_fields']; ?></div>
                                <div class="text-sm text-gray-600">字段类型</div>
                            </div>
                            <div class="text-center p-3 bg-green-50 rounded-lg">
                                <div class="text-2xl font-bold text-green-600">PHP 8.1+</div>
                                <div class="text-sm text-gray-600">最低要求</div>
                            </div>
                        </div>
                        <?php endif; ?>
                        
                        <!-- 社交链接 -->
                        <div class="flex items-center space-x-4">
                            <a 
                                href="https://github.com/xuntheme/xun-framework" 
                                target="_blank"
                                rel="noopener noreferrer"
                                class="text-gray-400 hover:text-gray-600 transition-colors"
                                aria-label="GitHub"
                            >
                                <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                </svg>
                            </a>
                            <a 
                                href="mailto:<EMAIL>" 
                                class="text-gray-400 hover:text-gray-600 transition-colors"
                                aria-label="邮箱"
                            >
                                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                </svg>
                            </a>
                        </div>
                    </div>

                    <!-- 快速链接 -->
                    <div>
                        <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4">快速链接</h4>
                        <ul class="space-y-2">
                            <?php foreach ($quick_links as $link): ?>
                            <li>
                                <a 
                                    href="<?php echo $link['url']; ?>" 
                                    class="text-gray-600 hover:text-blue-600 transition-colors text-sm"
                                >
                                    <?php echo htmlspecialchars($link['title']); ?>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>

                    <!-- 社区链接 -->
                    <div>
                        <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4">社区</h4>
                        <ul class="space-y-2">
                            <?php foreach ($community_links as $link): ?>
                            <li>
                                <a 
                                    href="<?php echo $link['url']; ?>" 
                                    class="text-gray-600 hover:text-blue-600 transition-colors text-sm"
                                    <?php if (strpos($link['url'], 'http') === 0): ?>
                                    target="_blank" rel="noopener noreferrer"
                                    <?php endif; ?>
                                >
                                    <?php echo htmlspecialchars($link['title']); ?>
                                </a>
                            </li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>

                <!-- 底部信息 -->
                <div class="mt-12 pt-8 border-t border-gray-200">
                    <div class="flex flex-col md:flex-row justify-between items-center">
                        <div class="text-gray-500 text-sm mb-4 md:mb-0">
                            © <?php echo $footer_config['current_year']; ?> <?php echo htmlspecialchars($footer_config['site_title']); ?>. 保留所有权利。
                        </div>
                        
                        <!-- 版本和许可信息 -->
                        <div class="flex items-center space-x-4 text-sm text-gray-500">
                            <span class="flex items-center space-x-1">
                                <span>版本</span>
                                <code class="px-2 py-1 bg-gray-100 rounded text-xs font-mono">
                                    v<?php echo htmlspecialchars($footer_config['version']); ?>
                                </code>
                            </span>
                            <span class="hidden md:block">•</span>
                            <a href="https://opensource.org/licenses/MIT" target="_blank" rel="noopener noreferrer" class="hover:text-blue-600 transition-colors">
                                MIT 许可证
                            </a>
                            <span class="hidden md:block">•</span>
                            <a href="#" class="hover:text-blue-600 transition-colors">
                                隐私政策
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- 通用JavaScript功能 -->
    <script>
        // 平滑滚动功能
        document.addEventListener('DOMContentLoaded', function() {
            // 平滑滚动到锚点
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // 代码复制功能
            document.querySelectorAll('.copy-button').forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-copy-target');
                    const codeElement = document.getElementById(targetId);
                    if (!codeElement) return;
                    
                    const text = codeElement.textContent;
                    
                    navigator.clipboard.writeText(text).then(() => {
                        const originalText = this.innerHTML;
                        this.innerHTML = `
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            已复制
                        `;
                        this.classList.add('bg-green-600');
                        
                        setTimeout(() => {
                            this.innerHTML = originalText;
                            this.classList.remove('bg-green-600');
                        }, 2000);
                    }).catch(err => {
                        console.error('复制失败:', err);
                    });
                });
            });

            // 返回顶部按钮
            const backToTopButton = document.createElement('button');
            backToTopButton.innerHTML = `
                <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 10l7-7m0 0l7 7m-7-7v18"></path>
                </svg>
            `;
            backToTopButton.className = 'fixed bottom-8 right-8 w-12 h-12 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-all duration-300 opacity-0 pointer-events-none z-50';
            backToTopButton.setAttribute('aria-label', '返回顶部');
            document.body.appendChild(backToTopButton);

            // 显示/隐藏返回顶部按钮
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopButton.classList.remove('opacity-0', 'pointer-events-none');
                } else {
                    backToTopButton.classList.add('opacity-0', 'pointer-events-none');
                }
            });

            // 返回顶部功能
            backToTopButton.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        });
    </script>

    <!-- Prism.js 代码高亮 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</body>
</html>
