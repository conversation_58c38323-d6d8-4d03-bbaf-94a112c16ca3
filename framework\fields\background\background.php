<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问

/**
 * Background 字段类
 *
 * 提供完整的背景设置功能，包括颜色、图片、位置等所有背景相关属性。
 *
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

if ( ! class_exists( 'XUN_Field_background' ) ) {

    /**
     * XUN_Field_background 背景字段类
     *
     * 继承自XUN_Fields基类，提供完整的背景设置功能。
     * 支持背景颜色、图片、位置、重复、尺寸等所有CSS背景属性。
     *
     * @since 1.0
     */
    class XUN_Field_background extends XUN_Fields {

        /**
         * 构造函数
         *
         * 初始化background字段实例。
         *
         * @since 1.0
         *
         * @param array  $field   字段配置数组
         * @param string $value   字段值
         * @param string $unique  唯一标识符
         * @param string $where   字段位置标识
         * @param string $parent  父级标识符
         */
        public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }

        /**
         * 渲染背景字段
         * 
         * 输出背景设置字段的HTML代码。
         * 
         * @since 1.0
         */
        public function render() {

            // 字段配置参数
            $args = wp_parse_args( $this->field, array(
                'background_color'    => true,
                'background_image'    => true,
                'background_position' => true,
                'background_repeat'   => true,
                'background_size'     => true,
                'background_attachment' => false,
            ) );

            // 默认值
            $default_value = array(
                'background-color'    => '',
                'background-image'    => '',
                'background-position' => '',
                'background-repeat'   => '',
                'background-size'     => '',
                'background-attachment' => '',
            );

            $default_value = ( ! empty( $this->field['default'] ) ) ? wp_parse_args( $this->field['default'], $default_value ) : $default_value;
            $this->value = wp_parse_args( $this->value, $default_value );

            // 输出前置内容
            echo $this->field_before();

            // 开始字段容器
            echo '<div>';

            // 输出标签
            if ( ! empty( $title ) ) {
                echo '<label class="block text-sm/6 font-medium text-gray-900 mb-4">';
                echo esc_html( $title );
                
                // 如果字段是必填的，添加必填标记
                if ( $this->is_required() ) {
                    echo ' <span class="text-red-500">*</span>';
                }
                
                echo '</label>';
            }

            // 背景设置容器
            echo '<div class="space-y-6 p-6 bg-gray-50 rounded-lg border border-gray-200">';

            // 背景颜色和背景图片的容器
            echo '<div class="grid grid-cols-1 lg:grid-cols-2 gap-6">';

            // 背景颜色列
            if ( $args['background_color'] ) {
                echo '<div>';
                $this->render_color_field();
                echo '</div>';
            }

            // 背景图片列
            if ( $args['background_image'] ) {
                echo '<div>';
                $this->render_image_field();
                echo '</div>';
            }

            echo '</div>'; // 结束grid容器

            // 移除常用颜色预设，改为在color字段中使用调色板

            // 背景属性（只有在有图片时才显示）
            echo '<div class="xun-bg-attributes space-y-4">';
            
            if ( $args['background_position'] ) {
                $this->render_position_field();
            }

            if ( $args['background_repeat'] ) {
                $this->render_repeat_field();
            }

            if ( $args['background_size'] ) {
                $this->render_size_field();
            }

            if ( $args['background_attachment'] ) {
                $this->render_attachment_field();
            }

            echo '</div>'; // 结束背景属性

            echo '</div>'; // 结束背景设置容器
            echo '</div>'; // 结束字段容器

            // 输出后置内容
            echo $this->field_after();
        }

        /**
         * 渲染背景颜色字段 - 使用工厂模式复用color字段
         */
        private function render_color_field() {
            $current_color = ! empty( $this->value['background-color'] ) ? $this->value['background-color'] : '#ffffff';

            echo '<div>';
            echo '<label class="block text-sm font-medium text-gray-700 mb-2">背景颜色</label>';

            // 使用工厂模式复用color字段
            $color_field = array(
                'id'      => 'background-color', // 简化ID，避免嵌套问题
                'type'    => 'color',
                'title'   => '', // 不显示标题，因为上面已经有了
                'default' => '#ffffff',
                'alpha'   => false,
                'palette' => array(
                    // 原有的8个颜色
                    '#ffffff', '#000000', '#FF0000', '#00FF00', '#0000FF', '#FFFF00', '#FF00FF', '#00FFFF',
                    // 新增的2个预设颜色
                    '#f8fafc', '#e2e8f0'
                ),
                'class'   => 'w-full', // 添加样式类
                'name'    => $this->field_name( '[background-color]' ), // 直接指定name属性
            );

            // 获取当前颜色值 - 确保是字符串
            $color_value = (string) $current_color;

            // 使用包含父字段信息的unique，确保每个background字段的color子字段都是唯一的
            $color_unique = $this->unique . '[' . $this->field['id'] . ']';

            // 使用工厂方法渲染颜色字段
            XUN::field( $color_field, $color_value, $color_unique, 'background', $this->field['id'] );

            echo '</div>';
        }

        /**
         * 渲染背景图片字段
         */
        private function render_image_field() {
            echo '<div>';
            echo '<label class="block text-sm font-medium text-gray-700 mb-2">背景图片</label>';

            // 准备media字段的值
            $media_value = array();
            if ( ! empty( $this->value['background-image'] ) ) {
                $media_value = array(
                    'url' => $this->value['background-image']
                );
            }

            // 生成唯一ID
            $field_id = $this->field_id() . '_background_image';
            $has_media = ! empty( $this->value['background-image'] );

            // 直接渲染media字段的HTML结构，但使用background字段的名称
            echo '<div class="xun-media-field" data-field-id="' . esc_attr( $field_id ) . '" data-library="image" data-preview-size="medium" data-multiple="false">';

            // 如果有图片，显示预览
            if ( $has_media ) {
                echo '<div class="xun-media-preview relative group bg-gray-50 border-2 border-dashed border-gray-200 rounded-lg overflow-hidden transition-all duration-200 hover:border-gray-300 mb-4" style="max-width: 64px; max-height: 64px;">';
                echo '<img src="' . esc_url( $this->value['background-image'] ) . '" alt="" class="w-full h-full object-cover" />';
                echo '<button type="button" class="xun-media-remove absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2" title="移除背景图片">';
                echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>';
                echo '</button>';
                echo '</div>';
            }

            // 控制区域
            echo '<div class="xun-media-controls">';
            echo '<div class="mb-3">';
            echo '<input type="text" name="' . esc_attr( $this->field_name( '[background-image]' ) ) . '" value="' . esc_attr( $this->value['background-image'] ) . '" class="xun-media-url w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm" placeholder="未选择背景图片" readonly />';
            echo '</div>';
            echo '<div class="flex flex-wrap gap-2">';
            echo '<button type="button" class="xun-media-button inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors duration-200">';
            echo '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>';
            echo '选择背景图片';
            echo '</button>';
            echo '</div>';
            echo '</div>';

            // 隐藏字段（media字段需要的，但我们只需要URL）
            $hidden_fields = array( 'id', 'filename', 'filesize', 'width', 'height', 'thumbnail', 'alt', 'title', 'description', 'mime_type' );
            foreach ( $hidden_fields as $field ) {
                $field_value = isset( $media_value[ $field ] ) ? $media_value[ $field ] : '';
                echo '<input type="hidden" name="' . esc_attr( $this->field_name( '[background-image-' . $field . ']' ) ) . '" value="' . esc_attr( $field_value ) . '" class="xun-media-' . esc_attr( $field ) . '" />';
            }

            echo '</div>';
            echo '</div>';
        }

        /**
         * 渲染背景位置字段
         */
        private function render_position_field() {
            $positions = array(
                ''              => '选择位置',
                'left top'      => '左上',
                'center top'    => '中上',
                'right top'     => '右上',
                'left center'   => '左中',
                'center center' => '居中',
                'right center'  => '右中',
                'left bottom'   => '左下',
                'center bottom' => '中下',
                'right bottom'  => '右下',
            );

            echo '<div>';
            echo '<label class="block text-sm font-medium text-gray-700 mb-2">背景位置</label>';
            echo '<select ';
            echo 'name="' . esc_attr( $this->field_name( '[background-position]' ) ) . '" ';
            echo 'class="block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600">';
            
            foreach ( $positions as $value => $label ) {
                echo '<option value="' . esc_attr( $value ) . '"' . selected( $this->value['background-position'], $value, false ) . '>';
                echo esc_html( $label );
                echo '</option>';
            }
            
            echo '</select>';
            echo '</div>';
        }

        /**
         * 渲染背景重复字段
         */
        private function render_repeat_field() {
            $repeats = array(
                ''          => '选择重复方式',
                'no-repeat' => '不重复',
                'repeat'    => '重复',
                'repeat-x'  => '水平重复',
                'repeat-y'  => '垂直重复',
            );

            echo '<div>';
            echo '<label class="block text-sm font-medium text-gray-700 mb-2">背景重复</label>';
            echo '<select ';
            echo 'name="' . esc_attr( $this->field_name( '[background-repeat]' ) ) . '" ';
            echo 'class="block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600">';
            
            foreach ( $repeats as $value => $label ) {
                echo '<option value="' . esc_attr( $value ) . '"' . selected( $this->value['background-repeat'], $value, false ) . '>';
                echo esc_html( $label );
                echo '</option>';
            }
            
            echo '</select>';
            echo '</div>';
        }

        /**
         * 渲染背景尺寸字段
         */
        private function render_size_field() {
            $sizes = array(
                ''        => '选择尺寸',
                'auto'    => '自动',
                'cover'   => '覆盖',
                'contain' => '包含',
            );

            echo '<div>';
            echo '<label class="block text-sm font-medium text-gray-700 mb-2">背景尺寸</label>';
            echo '<select ';
            echo 'name="' . esc_attr( $this->field_name( '[background-size]' ) ) . '" ';
            echo 'class="block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600">';
            
            foreach ( $sizes as $value => $label ) {
                echo '<option value="' . esc_attr( $value ) . '"' . selected( $this->value['background-size'], $value, false ) . '>';
                echo esc_html( $label );
                echo '</option>';
            }
            
            echo '</select>';
            echo '</div>';
        }



        /**
         * 渲染背景附着字段
         */
        private function render_attachment_field() {
            $attachments = array(
                ''       => '选择附着方式',
                'scroll' => '滚动',
                'fixed'  => '固定',
            );

            echo '<div>';
            echo '<label class="block text-sm font-medium text-gray-700 mb-2">背景附着</label>';
            echo '<select ';
            echo 'name="' . esc_attr( $this->field_name( '[background-attachment]' ) ) . '" ';
            echo 'class="block w-full rounded-md bg-white px-3 py-1.5 text-sm text-gray-900 outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600">';
            
            foreach ( $attachments as $value => $label ) {
                echo '<option value="' . esc_attr( $value ) . '"' . selected( $this->value['background-attachment'], $value, false ) . '>';
                echo esc_html( $label );
                echo '</option>';
            }
            
            echo '</select>';
            echo '</div>';
        }

        /**
         * 加载字段资源
         */
        public function enqueue() {
            // 确保WordPress媒体库已加载
            if ( ! did_action( 'wp_enqueue_media' ) ) {
                wp_enqueue_media();
            }

            // 加载media字段的JavaScript来处理图片选择
            wp_enqueue_script(
                'xun-field-media',
                XUN_Setup::$url . '/assets/js/fields/media.js',
                array( 'jquery', 'media-upload', 'media-views' ),
                XUN_VERSION,
                true
            );

            // 加载jQuery
            wp_enqueue_script( 'jquery' );

            // 注意：color字段会通过工厂模式自动加载自己的资源
            // 不再需要background.js，因为图片选择功能由media字段处理
        }

        /**
         * 检查字段是否为必填
         */
        public function is_required() {
            return ! empty( $this->field['required'] ) && $this->field['required'] === true;
        }
    }
}
