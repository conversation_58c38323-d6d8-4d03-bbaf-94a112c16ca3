/**
 * Xun Framework Sortable 字段 JavaScript
 * 
 * 这个文件包含了可排序字段的所有交互逻辑，包括拖拽排序、
 * 项目管理、键盘导航、动画效果等功能。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

(function($) {
    'use strict';

    /**
     * Sortable 字段类
     */
    class XunSortable {
        
        /**
         * 构造函数
         * 
         * @param {jQuery} $container 字段容器元素
         */
        constructor($container) {
            this.$container = $container;
            this.$sortableContainer = $container.find('.xun-sortable-container');
            this.$itemsContainer = $container.find('.xun-sortable-items');
            this.$addButton = $container.find('.xun-sortable-add-item');
            this.$togglePreview = $container.find('.xun-sortable-toggle-preview');
            this.$preview = $container.find('.xun-sortable-preview');
            this.$template = $container.find('.xun-sortable-item-template');
            this.fieldId = $container.data('field-id');
            
            // 获取配置
            this.config = this.$sortableContainer.data('sortable-config') || {};
            
            // 状态管理
            this.isPreviewMode = false;
            this.draggedItem = null;
            this.itemCounter = this.getItemCount();
            
            this.init();
        }
        
        /**
         * 初始化可排序字段
         */
        init() {
            this.initSortable();
            this.initEvents();
            this.initKeyboardNavigation();
            this.updateItemNumbers();
        }
        
        /**
         * 初始化拖拽排序
         */
        initSortable() {
            if (!this.config.sortable || !this.$itemsContainer.length) return;

            const self = this;

            this.$itemsContainer.sortable({
                handle: this.config.showHandles ? '.xun-sortable-handle' : '.xun-sortable-item',
                placeholder: 'xun-sortable-placeholder bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg',
                tolerance: 'pointer',
                cursor: 'move',
                opacity: 0.9,
                distance: 5,
                scroll: true,
                scrollSensitivity: 100,
                scrollSpeed: 20,
                cursorAt: {
                    left: 10,
                    top: 10
                },
                helper: 'clone',
                start: function(event, ui) {
                    self.handleSortStart(event, ui);
                },
                update: function(event, ui) {
                    self.handleSortUpdate(event, ui);
                },
                stop: function(event, ui) {
                    self.handleSortStop(event, ui);
                }
            });
        }
        
        /**
         * 初始化事件监听
         */
        initEvents() {
            const self = this;
            
            // 添加项目按钮
            this.$addButton.on('click', function(e) {
                e.preventDefault();
                self.addItem();
            });
            
            // 删除项目按钮
            this.$container.on('click', '.xun-sortable-remove', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.removeItem($(this).closest('.xun-sortable-item'));
            });
            
            // 折叠/展开按钮
            this.$container.on('click', '.xun-sortable-toggle', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.toggleItem($(this).closest('.xun-sortable-item'));
            });
            
            // 预览切换
            this.$togglePreview.on('click', function(e) {
                e.preventDefault();
                self.togglePreview();
            });
            
            // 项目内容变化监听
            this.$container.on('change input', '.xun-sortable-item-content input, .xun-sortable-item-content select, .xun-sortable-item-content textarea', function() {
                self.updatePreview();
            });
        }
        
        /**
         * 初始化键盘导航
         */
        initKeyboardNavigation() {
            if (!this.config.keyboardNav) return;
            
            const self = this;
            
            this.$container.on('keydown', '.xun-sortable-item', function(e) {
                const $item = $(this);
                const $items = self.$itemsContainer.find('.xun-sortable-item');
                const currentIndex = $items.index($item);
                
                switch (e.key) {
                    case 'ArrowUp':
                        if (e.ctrlKey && currentIndex > 0) {
                            e.preventDefault();
                            self.moveItem($item, 'up');
                        }
                        break;
                        
                    case 'ArrowDown':
                        if (e.ctrlKey && currentIndex < $items.length - 1) {
                            e.preventDefault();
                            self.moveItem($item, 'down');
                        }
                        break;
                        
                    case 'Delete':
                        if (e.ctrlKey && self.config.removable) {
                            e.preventDefault();
                            self.removeItem($item);
                        }
                        break;
                        
                    case 'Enter':
                        if (e.ctrlKey && self.config.collapsible) {
                            e.preventDefault();
                            self.toggleItem($item);
                        }
                        break;
                }
            });
        }
        
        /**
         * 处理排序开始
         */
        handleSortStart(_, ui) {
            ui.item.addClass('xun-sortable-dragging');
            ui.placeholder.height(ui.item.outerHeight());
        }

        /**
         * 处理排序更新
         */
        handleSortUpdate() {
            this.updateItemNumbers();
            this.updatePreview();
        }

        /**
         * 处理排序停止
         */
        handleSortStop(_, ui) {
            ui.item.removeClass('xun-sortable-dragging');
        }
        
        /**
         * 添加项目
         */
        addItem() {
            if (this.config.maxItems > 0 && this.getItemCount() >= this.config.maxItems) {
                alert(window.xunSortable.strings.maxItemsReached);
                return;
            }
            
            // 这里需要根据字段定义创建新项目
            // 实际实现需要与后端配合
            const newItemHtml = this.createNewItemHtml();
            const $newItem = $(newItemHtml);
            
            this.$itemsContainer.append($newItem);
            this.itemCounter++;
            
            // 更新编号
            this.updateItemNumbers();
            
            // 初始化新项目的字段
            this.initializeNewItemFields($newItem);
            
            // 滚动到新项目
            $newItem[0].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            
            // 聚焦第一个输入字段
            const $firstInput = $newItem.find('input, select, textarea').first();
            if ($firstInput.length > 0) {
                $firstInput[0].focus();
            }
            
            // 触发事件
            this.$container.trigger('xun:sortable:add', {
                item: $newItem,
                index: $newItem.index()
            });
        }
        
        /**
         * 删除项目
         */
        removeItem($item) {
            if (this.config.minItems > 0 && this.getItemCount() <= this.config.minItems) {
                alert(window.xunSortable.strings.minItemsRequired);
                return;
            }
            
            if (!confirm(window.xunSortable.strings.confirmRemove)) {
                return;
            }
            
            const itemIndex = $item.index();
            
            // 动画移除
            if (this.config.animation) {
                $item.addClass('animate-pulse');
                setTimeout(() => {
                    $item.slideUp(300, () => {
                        $item.remove();
                        this.updateItemNumbers();
                        this.updatePreview();
                    });
                }, 150);
            } else {
                $item.remove();
                this.updateItemNumbers();
                this.updatePreview();
            }
            
            // 触发事件
            this.$container.trigger('xun:sortable:remove', {
                index: itemIndex
            });
        }
        
        /**
         * 切换项目折叠状态
         */
        toggleItem($item) {
            const $content = $item.find('.xun-sortable-item-content');
            const $toggle = $item.find('.xun-sortable-toggle svg');
            const isCollapsed = $content.is(':hidden');
            
            if (isCollapsed) {
                $content.slideDown(200);
                $toggle.removeClass('rotate-180');
                $item.removeClass('xun-collapsed');
            } else {
                $content.slideUp(200);
                $toggle.addClass('rotate-180');
                $item.addClass('xun-collapsed');
            }
            
            // 触发事件
            this.$container.trigger('xun:sortable:toggle', {
                item: $item,
                collapsed: !isCollapsed
            });
        }
        
        /**
         * 移动项目
         */
        moveItem($item, direction) {
            const $items = this.$itemsContainer.find('.xun-sortable-item');
            const currentIndex = $items.index($item);
            
            if (direction === 'up' && currentIndex > 0) {
                $item.insertBefore($items.eq(currentIndex - 1));
            } else if (direction === 'down' && currentIndex < $items.length - 1) {
                $item.insertAfter($items.eq(currentIndex + 1));
            }
            
            this.updateItemNumbers();
            this.updatePreview();
            
            // 触发事件
            this.$container.trigger('xun:sortable:move', {
                item: $item,
                direction: direction,
                newIndex: $item.index()
            });
        }
        
        /**
         * 切换预览模式
         */
        togglePreview() {
            this.isPreviewMode = !this.isPreviewMode;
            
            if (this.isPreviewMode) {
                this.$preview.removeClass('hidden');
                this.updatePreview();
            } else {
                this.$preview.addClass('hidden');
            }
            
            // 更新按钮状态
            this.$togglePreview.toggleClass('bg-blue-100 text-blue-700', this.isPreviewMode);
        }
        
        /**
         * 更新预览内容
         */
        updatePreview() {
            if (!this.isPreviewMode || !this.$preview.length) return;
            
            const items = [];
            this.$itemsContainer.find('.xun-sortable-item').each(function(index) {
                const $item = $(this);
                const title = $item.find('.xun-sortable-item-header h4').text() || `项目 ${index + 1}`;
                items.push(`${index + 1}. ${title}`);
            });
            
            const previewHtml = items.length > 0 ? items.join('<br>') : '暂无项目';
            this.$preview.find('.xun-sortable-preview-content').html(previewHtml);
        }
        
        /**
         * 更新项目编号
         */
        updateItemNumbers() {
            if (!this.config.showNumbers) return;
            
            this.$itemsContainer.find('.xun-sortable-item').each(function(index) {
                $(this).find('.xun-sortable-number').text(index + 1);
                $(this).attr('data-item-index', index);
            });
        }
        
        /**
         * 获取项目数量
         */
        getItemCount() {
            return this.$itemsContainer.find('.xun-sortable-item').length;
        }
        
        /**
         * 创建新项目HTML
         */
        createNewItemHtml() {
            // 这里应该根据字段定义生成HTML
            // 简化实现，实际需要更复杂的逻辑
            return `
                <div class="xun-sortable-item relative bg-white border border-gray-200 rounded-lg shadow-sm transition-all duration-200" data-item-key="new-${this.itemCounter}" data-item-index="${this.getItemCount()}">
                    <div class="xun-sortable-item-header flex items-center justify-between p-3 border-b border-gray-200">
                        <div class="flex items-center space-x-3">
                            ${this.config.showHandles ? '<div class="xun-sortable-handle cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600"><svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" /></svg></div>' : ''}
                            ${this.config.showNumbers ? `<span class="xun-sortable-number inline-flex items-center justify-center w-6 h-6 text-xs font-medium text-gray-500 bg-gray-100 rounded-full">${this.getItemCount() + 1}</span>` : ''}
                            <h4 class="text-sm font-medium text-gray-900">新项目</h4>
                        </div>
                        <div class="flex items-center space-x-2">
                            ${this.config.collapsible ? '<button type="button" class="xun-sortable-toggle text-gray-400 hover:text-gray-600 focus:outline-none"><svg class="w-4 h-4 transform transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" /></svg></button>' : ''}
                            ${this.config.removable ? '<button type="button" class="xun-sortable-remove text-red-400 hover:text-red-600 focus:outline-none"><svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" /></svg></button>' : ''}
                        </div>
                    </div>
                    <div class="xun-sortable-item-content p-4">
                        <input type="text" class="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" placeholder="请输入内容..." />
                    </div>
                </div>
            `;
        }
        
        /**
         * 初始化新项目的字段
         */
        initializeNewItemFields($item) {
            // 这里可以初始化新项目中的特殊字段
            // 例如颜色选择器、日期选择器等
            
            // 重新初始化框架字段
            if (window.XUN && window.XUN.init) {
                window.XUN.init($item);
            }
        }
        
        /**
         * 获取排序后的数据
         */
        getSortedData() {
            const data = {};
            
            this.$itemsContainer.find('.xun-sortable-item').each(function() {
                const $item = $(this);
                const key = $item.data('item-key');
                const itemData = {};

                // 收集项目中的字段数据
                $item.find('input, select, textarea').each(function() {
                    const $field = $(this);
                    const name = $field.attr('name');
                    if (name) {
                        itemData[name] = $field.val();
                    }
                });

                data[key] = itemData;
            });
            
            return data;
        }
        

    }

    /**
     * 初始化所有可排序字段
     */
    function initSortableFields() {
        $('.xun-sortable-field').each(function() {
            const $container = $(this);
            if (!$container.data('xun-sortable-initialized')) {
                new XunSortable($container);
                $container.data('xun-sortable-initialized', true);
            }
        });
    }

    // 文档就绪时初始化
    $(document).ready(function() {
        initSortableFields();
    });

    // 支持动态加载的字段
    $(document).on('xun:field:loaded', function() {
        initSortableFields();
    });

})(jQuery);
