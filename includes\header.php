<?php
/**
 * Xun Framework 文档系统 - 通用页面头部
 *
 * 提供统一的页面头部，包括导航栏、SEO信息和样式文件。
 * 支持动态配置页面标题、描述和当前页面状态。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

// 默认配置，可以在包含此文件前覆盖
$default_config = [
    'site_title' => 'Xun Framework',
    'site_description' => '现代化的WordPress选项框架',
    'page_title' => '',
    'page_description' => '',
    'keywords' => 'WordPress, Framework, Options, PHP, TailwindCSS',
    'current_page' => 'home', // home, getting-started, field-types, api-reference

];

// 合并配置
$config = array_merge($default_config, $header_config ?? []);

// 生成完整的页面标题
$full_title = !empty($config['page_title']) 
    ? $config['page_title'] . ' - ' . $config['site_title']
    : $config['site_title'];

// 生成页面描述
$page_description = !empty($config['page_description']) 
    ? $config['page_description'] 
    : $config['site_description'];

// 导航菜单配置
$nav_items = [
    'home' => [
        'title' => '首页',
        'url' => 'index.php',
        'icon' => 'home'
    ],
    'getting-started' => [
        'title' => '快速开始',
        'url' => 'getting-started.php',
        'icon' => 'rocket'
    ],
    'field-types' => [
        'title' => '字段类型',
        'url' => 'field-types.php',
        'icon' => 'squares'
    ],
    'api-reference' => [
        'title' => 'API参考',
        'url' => 'api-reference.php',
        'icon' => 'book'
    ]
];
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- 页面信息 -->
    <title><?php echo htmlspecialchars($full_title); ?></title>
    <meta name="description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta name="keywords" content="<?php echo htmlspecialchars($config['keywords']); ?>">
    <meta name="author" content="June">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo htmlspecialchars($full_title); ?>">
    <meta property="og:description" content="<?php echo htmlspecialchars($page_description); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI']; ?>">
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/input.css">
    
    <!-- Prism.js 代码高亮样式 -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet">
    
    <!-- 自定义代码样式 -->
    <style>
        .bg-gray-900 pre[class*="language-"] {
            background: transparent !important;
            margin: 0 !important;
            padding: 0 !important;
            font-size: 0.875rem !important;
            line-height: 1.5 !important;
        }
        
        .bg-gray-900 code[class*="language-"] {
            background: transparent !important;
            color: #e5e7eb !important;
            font-family: 'JetBrains Mono', 'Fira Code', Consolas, 'Courier New', monospace !important;
            font-size: 0.875rem !important;
        }
        
        /* 导航活动状态样式 */
        .nav-item.active {
            color: #2563eb !important;
            background-color: #eff6ff !important;
        }
        
        /* 移动端菜单样式 */
        .mobile-menu {
            transform: translateX(-100%);
            transition: transform 0.3s ease-in-out;
        }
        
        .mobile-menu.open {
            transform: translateX(0);
        }
    </style>
</head>
<body class="bg-gray-50 text-gray-900 antialiased">
    <!-- 页面容器 -->
    <div class="min-h-screen">
        <!-- 顶部导航栏 -->
        <header class="bg-white/95 backdrop-blur-sm border-b border-gray-200 sticky top-0 z-50">
            <div class="max-w-7xl mx-auto">
                <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
                    <!-- Logo和标题 -->
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">X</span>
                        </div>
                        <div>
                            <h1 class="text-xl font-bold text-gray-900">
                                <a href="index.php" class="hover:text-blue-600 transition-colors">
                                    <?php echo htmlspecialchars($config['site_title']); ?>
                                </a>
                            </h1>
                            <p class="text-xs text-gray-500 -mt-1">
                                <?php echo htmlspecialchars($config['site_description']); ?>
                            </p>
                        </div>
                    </div>
                    
                    <!-- 桌面端导航菜单 -->
                    <nav class="hidden md:flex items-center space-x-2">
                        <?php foreach ($nav_items as $key => $item): ?>
                        <a 
                            href="<?php echo $item['url']; ?>" 
                            class="nav-item px-4 py-2 rounded-lg transition-all duration-300 font-medium <?php echo $config['current_page'] === $key ? 'text-blue-600 bg-blue-50' : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'; ?>"
                        >
                            <?php echo htmlspecialchars($item['title']); ?>
                        </a>
                        <?php endforeach; ?>
                    </nav>
                    
                    <!-- 右侧操作区 -->
                    <div class="flex items-center space-x-4">
                        <!-- GitHub链接 -->
                        <a 
                            href="https://github.com/xuntheme/xun-framework" 
                            target="_blank"
                            rel="noopener noreferrer"
                            class="text-gray-500 hover:text-gray-700 transition-colors"
                            aria-label="GitHub"
                        >
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                        </a>
                        
                        <!-- 移动端菜单按钮 -->
                        <button 
                            class="md:hidden p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
                            id="mobile-menu-button"
                            aria-label="打开菜单"
                        >
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- 移动端菜单 -->
            <div class="mobile-menu md:hidden fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl" id="mobile-menu">
                <div class="flex items-center justify-between p-4 border-b border-gray-200">
                    <div class="flex items-center space-x-3">
                        <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                            <span class="text-white font-bold text-sm">X</span>
                        </div>
                        <span class="text-lg font-bold text-gray-900"><?php echo htmlspecialchars($config['site_title']); ?></span>
                    </div>
                    <button 
                        class="p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
                        id="mobile-menu-close"
                        aria-label="关闭菜单"
                    >
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
                <nav class="p-4 space-y-2">
                    <?php foreach ($nav_items as $key => $item): ?>
                    <a 
                        href="<?php echo $item['url']; ?>" 
                        class="flex items-center px-4 py-3 rounded-lg transition-colors <?php echo $config['current_page'] === $key ? 'text-blue-600 bg-blue-50' : 'text-gray-700 hover:bg-gray-50'; ?>"
                    >
                        <?php echo htmlspecialchars($item['title']); ?>
                    </a>
                    <?php endforeach; ?>
                </nav>
            </div>
            
            <!-- 移动端菜单遮罩 -->
            <div class="mobile-menu-overlay md:hidden fixed inset-0 bg-black bg-opacity-50 z-40 hidden" id="mobile-menu-overlay"></div>
        </header>

        <!-- 移动端菜单JavaScript -->
        <script>
            document.addEventListener('DOMContentLoaded', function() {
                const mobileMenuButton = document.getElementById('mobile-menu-button');
                const mobileMenuClose = document.getElementById('mobile-menu-close');
                const mobileMenu = document.getElementById('mobile-menu');
                const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
                
                function openMobileMenu() {
                    mobileMenu.classList.add('open');
                    mobileMenuOverlay.classList.remove('hidden');
                    document.body.style.overflow = 'hidden';
                }
                
                function closeMobileMenu() {
                    mobileMenu.classList.remove('open');
                    mobileMenuOverlay.classList.add('hidden');
                    document.body.style.overflow = '';
                }
                
                mobileMenuButton.addEventListener('click', openMobileMenu);
                mobileMenuClose.addEventListener('click', closeMobileMenu);
                mobileMenuOverlay.addEventListener('click', closeMobileMenu);
                
                // ESC键关闭菜单
                document.addEventListener('keydown', function(e) {
                    if (e.key === 'Escape' && mobileMenu.classList.contains('open')) {
                        closeMobileMenu();
                    }
                });
            });
        </script>
