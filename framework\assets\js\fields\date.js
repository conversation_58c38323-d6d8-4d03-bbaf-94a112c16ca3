/**
 * Date Field JavaScript - 组合式架构增强
 *
 * 保留所有date字段特有功能：日历弹窗、日期验证、格式化等
 * 基础文本输入功能由text.js处理
 *
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.1.0
 */

(function($) {
    'use strict';

    /**
     * XUN 日期选择器类
     */
    class XUNDatePicker {
        
        /**
         * 构造函数
         * 
         * @param {HTMLElement} element 日期字段容器元素
         * @param {Object} options 配置选项
         */
        constructor(element, options = {}) {
            this.element = element;
            this.options = this.mergeOptions(options);
            this.currentDate = new Date();
            this.selectedDate = null;
            this.isOpen = false;
            this.isRange = element.hasAttribute('data-range-type');
            this.rangeType = element.getAttribute('data-range-type') || null;
            
            this.init();
        }
        
        /**
         * 合并配置选项
         *
         * @param {Object} userOptions 用户配置
         * @returns {Object} 合并后的配置
         */
        mergeOptions(userOptions) {
            const defaults = {
                date_format: 'Y-m-d',
                display_format: 'Y年m月d日',
                placeholder: '请选择日期',
                min_date: null,
                max_date: null,
                disabled_dates: [],
                disabled_days: [],
                first_day: 1,
                show_today: true,
                show_clear: true,
                auto_close: true,
                locale: 'zh-CN'
            };

            // 合并用户选项，保持PHP的命名约定
            const merged = Object.assign({}, defaults, userOptions);

            // 为了兼容性，也创建驼峰命名的别名
            merged.dateFormat = merged.date_format;
            merged.displayFormat = merged.display_format;
            merged.minDate = merged.min_date;
            merged.maxDate = merged.max_date;
            merged.disabledDates = merged.disabled_dates;
            merged.disabledDays = merged.disabled_days;
            merged.firstDay = merged.first_day;
            merged.showToday = merged.show_today;
            merged.showClear = merged.show_clear;
            merged.autoClose = merged.auto_close;

            return merged;
        }
        
        /**
         * 初始化日期选择器
         */
        init() {
            this.bindElements();
            this.bindEvents();
            this.parseInitialValue();
            this.updateDisplay();
        }
        
        /**
         * 绑定DOM元素
         */
        bindElements() {
            this.wrapper = this.element;
            this.hiddenInput = this.wrapper.querySelector('.xun-date-value');
            this.displayInput = this.wrapper.querySelector('.xun-date-input');
            this.trigger = this.wrapper.querySelector('.xun-date-trigger');
            this.picker = this.wrapper.querySelector('.xun-date-picker');
            this.content = this.picker.querySelector('.xun-date-picker-content');
            this.header = this.picker.querySelector('.xun-date-header');
            this.prevBtn = this.picker.querySelector('.xun-date-prev-month');
            this.nextBtn = this.picker.querySelector('.xun-date-next-month');
            this.monthYearBtn = this.picker.querySelector('.xun-date-month-year');
            this.monthYearSpan = this.picker.querySelector('.xun-current-month-year');
            this.grid = this.picker.querySelector('.xun-date-grid');
            this.todayBtn = this.picker.querySelector('.xun-date-today');
            this.clearBtn = this.picker.querySelector('.xun-date-clear');

            // 时间选择器元素
            this.timeSection = this.picker.querySelector('.xun-time-section');
            this.hourInput = this.picker.querySelector('.xun-time-hour');
            this.minuteInput = this.picker.querySelector('.xun-time-minute');
            this.secondInput = this.picker.querySelector('.xun-time-second');
            this.ampmSelect = this.picker.querySelector('.xun-time-ampm');
            this.timeNowBtn = this.picker.querySelector('.xun-time-now');
            this.timePresetBtns = this.picker.querySelectorAll('.xun-time-preset');
        }
        
        /**
         * 绑定事件监听器
         */
        bindEvents() {
            // 触发器点击事件
            this.trigger.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggle();
            });
            
            // 输入框点击事件
            this.displayInput.addEventListener('click', (e) => {
                e.preventDefault();
                this.open();
            });
            
            // 导航按钮事件
            this.prevBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.previousMonth();
            });
            
            this.nextBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.nextMonth();
            });
            
            // 今天按钮事件
            if (this.todayBtn && (this.options.show_today !== false)) {
                this.todayBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.selectToday();
                });
            }

            // 清除按钮事件
            if (this.clearBtn && (this.options.show_clear !== false)) {
                this.clearBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.clear();
                });
            }

            // 时间选择器事件
            if (this.hourInput) {
                this.hourInput.addEventListener('input', () => this.validateAndUpdateTime());
                this.hourInput.addEventListener('blur', () => this.formatTimeInput(this.hourInput, 'hour'));
            }

            if (this.minuteInput) {
                this.minuteInput.addEventListener('input', () => this.validateAndUpdateTime());
                this.minuteInput.addEventListener('blur', () => this.formatTimeInput(this.minuteInput, 'minute'));
            }

            if (this.secondInput) {
                this.secondInput.addEventListener('input', () => this.validateAndUpdateTime());
                this.secondInput.addEventListener('blur', () => this.formatTimeInput(this.secondInput, 'second'));
            }

            if (this.ampmSelect) {
                this.ampmSelect.addEventListener('change', () => this.updateDateTime());
            }

            // 现在按钮事件
            if (this.timeNowBtn) {
                this.timeNowBtn.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.setCurrentTime();
                });
            }

            // 预设时间按钮事件
            this.timePresetBtns.forEach(btn => {
                btn.addEventListener('click', (e) => {
                    e.preventDefault();
                    const time = btn.dataset.time;
                    this.setPresetTime(time);
                });
            });
            
            // 键盘事件
            this.displayInput.addEventListener('keydown', (e) => {
                this.handleKeydown(e);
            });
            
            // 点击外部关闭
            document.addEventListener('click', (e) => {
                if (!this.wrapper.contains(e.target)) {
                    this.close();
                }
            });
            
            // 阻止选择器内部点击冒泡
            this.picker.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }
        
        /**
         * 解析初始值
         */
        parseInitialValue() {
            const value = this.hiddenInput.value;
            if (value) {
                this.selectedDate = this.parseDate(value);
                if (this.selectedDate) {
                    this.currentDate = new Date(this.selectedDate);
                }
            }
        }
        
        /**
         * 解析日期字符串
         * 
         * @param {string} dateString 日期字符串
         * @returns {Date|null} 解析后的日期对象
         */
        parseDate(dateString) {
            if (!dateString) return null;
            
            // 尝试多种格式解析
            const formats = [
                /^(\d{4})-(\d{2})-(\d{2})$/,  // YYYY-MM-DD
                /^(\d{4})\/(\d{2})\/(\d{2})$/, // YYYY/MM/DD
                /^(\d{2})\/(\d{2})\/(\d{4})$/, // MM/DD/YYYY
                /^(\d{2})-(\d{2})-(\d{4})$/   // MM-DD-YYYY
            ];
            
            for (let format of formats) {
                const match = dateString.match(format);
                if (match) {
                    let year, month, day;
                    if (format === formats[2] || format === formats[3]) {
                        // MM/DD/YYYY or MM-DD-YYYY
                        month = parseInt(match[1]) - 1;
                        day = parseInt(match[2]);
                        year = parseInt(match[3]);
                    } else {
                        // YYYY-MM-DD or YYYY/MM/DD
                        year = parseInt(match[1]);
                        month = parseInt(match[2]) - 1;
                        day = parseInt(match[3]);
                    }
                    
                    const date = new Date(year, month, day);
                    if (date.getFullYear() === year && 
                        date.getMonth() === month && 
                        date.getDate() === day) {
                        return date;
                    }
                }
            }
            
            // 尝试使用Date构造函数
            const date = new Date(dateString);
            return isNaN(date.getTime()) ? null : date;
        }
        
        /**
         * 格式化日期
         *
         * @param {Date} date 日期对象
         * @param {string} format 格式字符串
         * @returns {string} 格式化后的日期字符串
         */
        formatDate(date, format) {
            if (!date || isNaN(date.getTime())) return '';

            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            const dayOfWeek = date.getDay();

            // 星期名称
            const weekdays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
            const weekdaysShort = ['日', '一', '二', '三', '四', '五', '六'];

            return format
                .replace(/Y/g, year)
                .replace(/m/g, month.toString().padStart(2, '0'))
                .replace(/d/g, day.toString().padStart(2, '0'))
                .replace(/l/g, weekdays[dayOfWeek])
                .replace(/D/g, weekdaysShort[dayOfWeek])
                .replace(/n/g, month)
                .replace(/j/g, day);
        }
        
        /**
         * 更新显示
         */
        updateDisplay() {
            // 更新显示输入框
            if (this.selectedDate) {
                let displayValue = this.formatDate(this.selectedDate, this.options.display_format || this.options.displayFormat);
                let hiddenValue = this.formatDate(this.selectedDate, this.options.date_format || this.options.dateFormat);

                // 如果启用了时间选择，添加时间部分
                if (this.options.enable_time && this.timeSection) {
                    const timeString = this.getTimeString();
                    if (timeString) {
                        displayValue += ' ' + timeString;
                        hiddenValue += ' ' + timeString;
                    }
                }

                this.displayInput.value = displayValue;
                this.hiddenInput.value = hiddenValue;
            } else {
                this.displayInput.value = '';
                this.hiddenInput.value = '';
            }

            // 更新月年显示
            if (this.monthYearSpan) {
                const monthNames = window.xunDateL10n ? window.xunDateL10n.monthsShort :
                    ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
                this.monthYearSpan.textContent = `${this.currentDate.getFullYear()}年${monthNames[this.currentDate.getMonth()]}`;
            }

            // 重新渲染日期网格
            this.renderCalendar();

            // 触发change事件
            this.hiddenInput.dispatchEvent(new Event('change', { bubbles: true }));
        }
        
        /**
         * 渲染日历
         */
        renderCalendar() {
            if (!this.grid) return;
            
            this.grid.innerHTML = '';
            
            const year = this.currentDate.getFullYear();
            const month = this.currentDate.getMonth();
            
            // 获取当月第一天
            const firstDay = new Date(year, month, 1);
            
            // 计算开始日期（考虑一周的第一天设置）
            const startDate = new Date(firstDay);
            const firstDayOfWeek = this.options.first_day || this.options.firstDay || 1;
            const dayOffset = (firstDay.getDay() - firstDayOfWeek + 7) % 7;
            startDate.setDate(startDate.getDate() - dayOffset);
            
            // 渲染6周的日期
            for (let week = 0; week < 6; week++) {
                for (let day = 0; day < 7; day++) {
                    const currentDate = new Date(startDate);
                    currentDate.setDate(startDate.getDate() + (week * 7) + day);
                    
                    const dayElement = this.createDayElement(currentDate, month);
                    this.grid.appendChild(dayElement);
                }
            }
        }

        /**
         * 创建日期元素
         *
         * @param {Date} date 日期
         * @param {number} currentMonth 当前月份
         * @returns {HTMLElement} 日期元素
         */
        createDayElement(date, currentMonth) {
            const dayElement = document.createElement('button');
            dayElement.type = 'button';
            dayElement.textContent = date.getDate();

            // 基础样式
            let classes = ['w-8', 'h-8', 'text-sm', 'rounded-lg', 'transition-all', 'duration-200', 'focus:outline-none', 'focus:ring-2', 'focus:ring-indigo-500'];

            // 判断是否为当前月份
            const isCurrentMonth = date.getMonth() === currentMonth;
            const isToday = this.isToday(date);
            const isSelected = this.isSelected(date);
            const isDisabled = this.isDateDisabled(date);

            if (!isCurrentMonth) {
                classes.push('text-gray-300');
            } else if (isDisabled) {
                classes.push('text-gray-300', 'cursor-not-allowed');
                dayElement.disabled = true;
            } else {
                classes.push('text-gray-700', 'hover:bg-indigo-50', 'hover:text-indigo-600');
            }

            if (isToday && !isSelected) {
                classes.push('bg-indigo-100', 'text-indigo-600', 'font-medium');
            }

            if (isSelected) {
                classes.push('bg-indigo-600', 'text-white', 'font-medium');
            }

            dayElement.className = classes.join(' ');

            // 添加ARIA标签
            dayElement.setAttribute('aria-label', this.formatDate(date, 'Y年m月d日'));
            if (isSelected) {
                dayElement.setAttribute('aria-selected', 'true');
            }
            if (isToday) {
                dayElement.setAttribute('aria-current', 'date');
            }

            // 添加点击事件
            if (!isDisabled) {
                dayElement.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.selectDate(date);
                });
            }

            return dayElement;
        }

        /**
         * 检查是否为今天
         *
         * @param {Date} date 要检查的日期
         * @returns {boolean} 是否为今天
         */
        isToday(date) {
            const today = new Date();
            return date.getFullYear() === today.getFullYear() &&
                   date.getMonth() === today.getMonth() &&
                   date.getDate() === today.getDate();
        }

        /**
         * 检查是否为选中日期
         *
         * @param {Date} date 要检查的日期
         * @returns {boolean} 是否为选中日期
         */
        isSelected(date) {
            if (!this.selectedDate) return false;
            return date.getFullYear() === this.selectedDate.getFullYear() &&
                   date.getMonth() === this.selectedDate.getMonth() &&
                   date.getDate() === this.selectedDate.getDate();
        }

        /**
         * 检查日期是否被禁用
         *
         * @param {Date} date 要检查的日期
         * @returns {boolean} 是否被禁用
         */
        isDateDisabled(date) {
            // 检查最小日期
            const minDate = this.options.min_date || this.options.minDate;
            if (minDate) {
                const minDateObj = this.parseDate(minDate);
                if (minDateObj && date < minDateObj) {
                    return true;
                }
            }

            // 检查最大日期
            const maxDate = this.options.max_date || this.options.maxDate;
            if (maxDate) {
                const maxDateObj = this.parseDate(maxDate);
                if (maxDateObj && date > maxDateObj) {
                    return true;
                }
            }

            // 检查禁用的星期
            const disabledDays = this.options.disabled_days || this.options.disabledDays || [];
            if (disabledDays.length > 0) {
                if (disabledDays.includes(date.getDay())) {
                    return true;
                }
            }

            // 检查禁用的具体日期
            const disabledDates = this.options.disabled_dates || this.options.disabledDates || [];
            if (disabledDates.length > 0) {
                const dateString = this.formatDate(date, this.options.date_format || this.options.dateFormat);
                if (disabledDates.includes(dateString)) {
                    return true;
                }
            }

            return false;
        }

        /**
         * 选择日期
         *
         * @param {Date} date 要选择的日期
         */
        selectDate(date) {
            this.selectedDate = new Date(date);
            this.updateDisplay();

            const autoClose = this.options.auto_close !== undefined ? this.options.auto_close : this.options.autoClose;
            if (autoClose !== false) {
                this.close();
            }
        }

        /**
         * 选择今天
         */
        selectToday() {
            const today = new Date();
            if (!this.isDateDisabled(today)) {
                this.selectDate(today);
                this.currentDate = new Date(today);

                // 如果启用了时间选择，设置当前时间
                if (this.options.enable_time && this.timeSection) {
                    this.setCurrentTime();
                }

                this.updateDisplay();
            }
        }

        /**
         * 清除选择
         */
        clear() {
            this.selectedDate = null;
            this.updateDisplay();
            const autoClose = this.options.auto_close !== undefined ? this.options.auto_close : this.options.autoClose;
            if (autoClose !== false) {
                this.close();
            }
        }

        /**
         * 上一个月
         */
        previousMonth() {
            this.currentDate.setMonth(this.currentDate.getMonth() - 1);
            this.updateDisplay();
        }

        /**
         * 下一个月
         */
        nextMonth() {
            this.currentDate.setMonth(this.currentDate.getMonth() + 1);
            this.updateDisplay();
        }

        /**
         * 打开选择器
         */
        open() {
            if (this.isOpen) return;

            this.isOpen = true;
            this.picker.classList.remove('hidden');

            // 智能定位
            this.positionPicker();

            // 添加动画 - 使用TailwindCSS类
            requestAnimationFrame(() => {
                this.picker.classList.remove('opacity-0', 'scale-95');
                this.picker.classList.add('opacity-100', 'scale-100');
            });

            // 聚焦到选择器
            this.picker.focus();
        }

        /**
         * 关闭选择器
         */
        close() {
            if (!this.isOpen) return;

            this.isOpen = false;

            // 添加关闭动画 - 使用TailwindCSS类
            this.picker.classList.remove('opacity-100', 'scale-100');
            this.picker.classList.add('opacity-0', 'scale-95');

            setTimeout(() => {
                this.picker.classList.add('hidden');
            }, 200);
        }

        /**
         * 切换选择器显示状态
         */
        toggle() {
            if (this.isOpen) {
                this.close();
            } else {
                this.open();
            }
        }

        /**
         * 智能定位选择器
         */
        positionPicker() {
            const rect = this.displayInput.getBoundingClientRect();
            const pickerRect = this.picker.getBoundingClientRect();
            const viewportHeight = window.innerHeight;
            const viewportWidth = window.innerWidth;

            // 重置位置类
            this.picker.classList.remove('top-full', 'bottom-full', 'left-0', 'right-0');

            // 垂直定位
            const spaceBelow = viewportHeight - rect.bottom;
            const spaceAbove = rect.top;

            if (spaceBelow >= pickerRect.height + 10 || spaceBelow >= spaceAbove) {
                // 显示在下方
                this.picker.classList.add('top-full');
            } else {
                // 显示在上方
                this.picker.classList.add('bottom-full');
            }

            // 水平定位
            const spaceRight = viewportWidth - rect.left;

            if (spaceRight >= pickerRect.width) {
                this.picker.classList.add('left-0');
            } else {
                this.picker.classList.add('right-0');
            }
        }

        /**
         * 处理键盘事件
         *
         * @param {KeyboardEvent} e 键盘事件
         */
        handleKeydown(e) {
            switch (e.key) {
                case 'Enter':
                case ' ':
                    e.preventDefault();
                    this.open();
                    break;
                case 'Escape':
                    e.preventDefault();
                    this.close();
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    if (!this.isOpen) {
                        this.open();
                    }
                    break;
                case 'Tab':
                    if (this.isOpen) {
                        this.close();
                    }
                    break;
            }
        }

        /**
         * 销毁日期选择器
         */
        destroy() {
            // 移除事件监听器
            this.trigger.removeEventListener('click', this.toggle);
            this.displayInput.removeEventListener('click', this.open);
            this.displayInput.removeEventListener('keydown', this.handleKeydown);

            // 清理引用
            this.element = null;
            this.options = null;
            this.currentDate = null;
            this.selectedDate = null;
        }

        /**
         * 获取时间字符串
         *
         * @returns {string} 格式化的时间字符串
         */
        getTimeString() {
            if (!this.hourInput || !this.minuteInput) {
                return '';
            }

            const hour = parseInt(this.hourInput.value) || 0;
            const minute = parseInt(this.minuteInput.value) || 0;
            const second = this.secondInput ? (parseInt(this.secondInput.value) || 0) : 0;
            const ampm = this.ampmSelect ? this.ampmSelect.value : '';

            let timeString = '';

            if (this.options.time_format === '12') {
                // 12小时制格式
                timeString = `${hour}:${minute.toString().padStart(2, '0')}`;
                if (this.options.show_seconds && this.secondInput) {
                    timeString += `:${second.toString().padStart(2, '0')}`;
                }
                timeString += ` ${ampm}`;
            } else {
                // 24小时制格式
                let hour24 = hour;
                if (ampm === 'PM' && hour !== 12) {
                    hour24 += 12;
                } else if (ampm === 'AM' && hour === 12) {
                    hour24 = 0;
                }

                timeString = `${hour24.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
                if (this.options.show_seconds && this.secondInput) {
                    timeString += `:${second.toString().padStart(2, '0')}`;
                }
            }

            return timeString;
        }

        /**
         * 设置当前时间
         */
        setCurrentTime() {
            if (!this.hourInput || !this.minuteInput) {
                return;
            }

            const now = new Date();
            let hour = now.getHours();
            const minute = now.getMinutes();
            const second = now.getSeconds();

            if (this.options.time_format === '12') {
                const ampm = hour >= 12 ? 'PM' : 'AM';
                hour = hour % 12;
                if (hour === 0) hour = 12;

                this.hourInput.value = hour.toString().padStart(2, '0');
                if (this.ampmSelect) {
                    this.ampmSelect.value = ampm;
                }
            } else {
                this.hourInput.value = hour.toString().padStart(2, '0');
            }

            this.minuteInput.value = minute.toString().padStart(2, '0');

            // 设置秒（如果显示）
            if (this.secondInput && this.options.show_seconds) {
                this.secondInput.value = second.toString().padStart(2, '0');
            }

            this.updateDateTime();
        }

        /**
         * 更新日期时间
         */
        updateDateTime() {
            this.updateDisplay();
        }

        /**
         * 验证并更新时间
         */
        validateAndUpdateTime() {
            // 实时验证输入值
            if (this.hourInput) {
                const hour = parseInt(this.hourInput.value);
                const maxHour = this.options.time_format === '12' ? 12 : 23;
                const minHour = this.options.time_format === '12' ? 1 : 0;

                if (hour > maxHour) {
                    this.hourInput.value = maxHour;
                } else if (hour < minHour && this.hourInput.value !== '') {
                    this.hourInput.value = minHour;
                }
            }

            if (this.minuteInput) {
                const minute = parseInt(this.minuteInput.value);
                if (minute > 59) {
                    this.minuteInput.value = 59;
                } else if (minute < 0 && this.minuteInput.value !== '') {
                    this.minuteInput.value = 0;
                }
            }

            if (this.secondInput) {
                const second = parseInt(this.secondInput.value);
                if (second > 59) {
                    this.secondInput.value = 59;
                } else if (second < 0 && this.secondInput.value !== '') {
                    this.secondInput.value = 0;
                }
            }

            this.updateDateTime();
        }

        /**
         * 格式化时间输入框
         *
         * @param {HTMLElement} input 输入框元素
         * @param {string} type 类型：hour, minute, second
         */
        formatTimeInput(input, type) {
            const value = parseInt(input.value);

            if (isNaN(value)) {
                // 如果输入无效，设置默认值
                if (type === 'hour') {
                    input.value = this.options.time_format === '12' ? '12' : '00';
                } else {
                    input.value = '00';
                }
            } else {
                // 格式化为两位数
                if (type === 'hour' && this.options.time_format === '24') {
                    input.value = value.toString().padStart(2, '0');
                } else if (type === 'hour' && this.options.time_format === '12') {
                    input.value = value.toString();
                } else {
                    input.value = value.toString().padStart(2, '0');
                }
            }

            this.updateDateTime();
        }

        /**
         * 设置预设时间
         *
         * @param {string} timeString 时间字符串，格式：HH:MM
         */
        setPresetTime(timeString) {
            const [hour, minute] = timeString.split(':').map(num => parseInt(num));

            if (this.options.time_format === '12') {
                let displayHour = hour;
                let ampm = 'AM';

                if (hour === 0) {
                    displayHour = 12;
                    ampm = 'AM';
                } else if (hour === 12) {
                    displayHour = 12;
                    ampm = 'PM';
                } else if (hour > 12) {
                    displayHour = hour - 12;
                    ampm = 'PM';
                }

                this.hourInput.value = displayHour.toString();
                if (this.ampmSelect) {
                    this.ampmSelect.value = ampm;
                }
            } else {
                this.hourInput.value = hour.toString().padStart(2, '0');
            }

            this.minuteInput.value = minute.toString().padStart(2, '0');

            if (this.secondInput) {
                this.secondInput.value = '00';
            }

            this.updateDateTime();
        }
    }

    /**
     * jQuery插件封装
     */
    $.fn.xunDatePicker = function(options) {
        return this.each(function() {
            const $this = $(this);
            let instance = $this.data('xun-date-picker');

            if (!instance) {
                // 获取设置
                const settings = $this.data('settings') || {};
                const mergedOptions = $.extend({}, settings, options);

                instance = new XUNDatePicker(this, mergedOptions);
                $this.data('xun-date-picker', instance);
            }
        });
    };

    /**
     * 自动初始化
     */
    $(document).ready(function() {
        // 初始化所有日期字段
        $('.xun-date-field-wrapper').xunDatePicker();

        // 监听动态添加的字段
        $(document).on('xun:field:added', function(_, $field) {
            $field.find('.xun-date-field-wrapper').xunDatePicker();
        });
    });

    // 暴露类到全局作用域
    window.XUNDatePicker = XUNDatePicker;

})(jQuery);
