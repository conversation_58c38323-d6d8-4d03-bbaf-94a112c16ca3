/**
 * Xun Framework Slider 字段 JavaScript
 * 
 * 这个文件包含了滑块字段的所有交互逻辑，包括拖拽滑动、
 * 键盘快捷键、实时预览、数值同步等功能。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

(function($) {
    'use strict';

    /**
     * Slider 字段类
     */
    class XunSlider {
        
        /**
         * 构造函数
         * 
         * @param {jQuery} $container 字段容器元素
         */
        constructor($container) {
            this.$container = $container;
            this.$track = $container.find('.xun-slider-track');
            this.$progress = $container.find('.xun-slider-progress');
            this.$handles = $container.find('.xun-slider-handle');
            this.$inputs = $container.find('.xun-slider-input');
            this.$tooltips = $container.find('.xun-slider-tooltip');
            this.$preview = $container.find('.xun-slider-preview');
            this.fieldId = $container.data('field-id');
            
            // 获取配置
            this.config = this.$track.data('slider-config') || {};
            this.isRange = this.config.range || false;
            this.isDragging = false;
            this.activeHandle = null;
            
            // 当前值
            this.values = this.isRange ? 
                { min: this.config.min, max: this.config.max } : 
                this.config.min;
            
            this.init();
        }
        
        /**
         * 初始化滑块
         */
        init() {
            this.initValues();
            this.initEvents();
            this.initKeyboard();
            this.addDynamicStyles();
            this.updateUI();
            this.updatePreview();
        }
        
        /**
         * 初始化数值
         */
        initValues() {
            if (this.isRange) {
                const minInput = this.$inputs.filter('.xun-slider-input-min');
                const maxInput = this.$inputs.filter('.xun-slider-input-max');
                
                this.values.min = parseFloat(minInput.val()) || this.config.min;
                this.values.max = parseFloat(maxInput.val()) || this.config.max;
            } else {
                this.values = parseFloat(this.$inputs.val()) || this.config.min;
            }
        }
        
        /**
         * 初始化事件监听
         */
        initEvents() {
            const self = this;
            
            // 轨道点击事件
            this.$track.on('mousedown touchstart', function(e) {
                if ($(e.target).hasClass('xun-slider-handle')) return;
                self.handleTrackClick(e);
            });
            
            // 手柄拖拽事件
            this.$handles.on('mousedown touchstart', function(e) {
                e.preventDefault();
                self.startDrag(e, $(this));
            });
            
            // 输入框变化事件
            this.$inputs.on('input change', function() {
                self.handleInputChange($(this));
            });
            
            // 全局鼠标事件
            $(document).on('mousemove touchmove', function(e) {
                if (self.isDragging) {
                    self.handleDrag(e);
                }
            });
            
            $(document).on('mouseup touchend', function() {
                if (self.isDragging) {
                    self.stopDrag();
                }
            });
            
            // 手柄悬停事件
            this.$handles.on('mouseenter', function() {
                self.showTooltip($(this));
            });
            
            this.$handles.on('mouseleave', function() {
                if (!self.isDragging) {
                    self.hideTooltip($(this));
                }
            });
        }
        
        /**
         * 初始化键盘支持
         */
        initKeyboard() {
            if (!this.config.keyboard) return;
            
            const self = this;
            
            this.$handles.on('keydown', function(e) {
                const $handle = $(this);
                const isMin = $handle.hasClass('xun-slider-handle-min');

                let step = self.config.step;
                let currentValue;

                if (self.isRange) {
                    currentValue = isMin ? self.values.min : self.values.max;
                } else {
                    currentValue = self.values;
                }
                
                // 处理键盘事件
                switch (e.key) {
                    case 'ArrowLeft':
                    case 'ArrowDown':
                        e.preventDefault();
                        self.adjustValue($handle, currentValue - step);
                        break;
                    case 'ArrowRight':
                    case 'ArrowUp':
                        e.preventDefault();
                        self.adjustValue($handle, currentValue + step);
                        break;
                    case 'PageDown':
                        e.preventDefault();
                        self.adjustValue($handle, currentValue - step * 10);
                        break;
                    case 'PageUp':
                        e.preventDefault();
                        self.adjustValue($handle, currentValue + step * 10);
                        break;
                    case 'Home':
                        e.preventDefault();
                        self.adjustValue($handle, self.config.min);
                        break;
                    case 'End':
                        e.preventDefault();
                        self.adjustValue($handle, self.config.max);
                        break;
                }
            });
        }
        
        /**
         * 处理轨道点击
         *
         * @param {Event} e 事件对象
         */
        handleTrackClick(e) {
            e.preventDefault();

            const rect = this.$track[0].getBoundingClientRect();
            const clientX = e.clientX || (e.originalEvent && e.originalEvent.touches && e.originalEvent.touches[0] ? e.originalEvent.touches[0].clientX : 0);
            const clickX = clientX - rect.left;
            const percentage = Math.max(0, Math.min(1, clickX / rect.width));
            const value = this.config.min + (percentage * (this.config.max - this.config.min));

            if (this.isRange) {
                // 范围滑块：选择最近的手柄
                const minDistance = Math.abs(value - this.values.min);
                const maxDistance = Math.abs(value - this.values.max);

                if (minDistance < maxDistance) {
                    this.adjustValue(this.$handles.filter('.xun-slider-handle-min'), value);
                } else {
                    this.adjustValue(this.$handles.filter('.xun-slider-handle-max'), value);
                }
            } else {
                // 单值滑块
                this.adjustValue(this.$handles, value);
            }
        }
        
        /**
         * 开始拖拽
         *
         * @param {Event} e 事件对象
         * @param {jQuery} $handle 手柄元素
         */
        startDrag(e, $handle) {
            e.preventDefault();

            this.isDragging = true;
            this.activeHandle = $handle;
            
            $handle.addClass('xun-dragging');
            $('body').addClass('xun-slider-dragging');
            
            this.showTooltip($handle);
            
            // 触发拖拽开始事件
            this.$container.trigger('xun:slider:dragstart', {
                handle: $handle,
                value: this.getCurrentValue($handle)
            });
        }
        
        /**
         * 处理拖拽
         *
         * @param {Event} e 事件对象
         */
        handleDrag(e) {
            if (!this.isDragging || !this.activeHandle) return;

            e.preventDefault();

            const rect = this.$track[0].getBoundingClientRect();
            const clientX = e.clientX || (e.originalEvent && e.originalEvent.touches && e.originalEvent.touches[0] ? e.originalEvent.touches[0].clientX : 0);
            const dragX = clientX - rect.left;
            const percentage = Math.max(0, Math.min(1, dragX / rect.width));
            const value = this.config.min + (percentage * (this.config.max - this.config.min));

            this.adjustValue(this.activeHandle, value);
        }
        
        /**
         * 停止拖拽
         */
        stopDrag() {
            if (!this.isDragging) return;
            
            this.isDragging = false;
            
            if (this.activeHandle) {
                this.activeHandle.removeClass('xun-dragging');
                this.hideTooltip(this.activeHandle);
                this.activeHandle = null;
            }
            
            $('body').removeClass('xun-slider-dragging');
            
            // 触发拖拽结束事件
            this.$container.trigger('xun:slider:dragend', {
                value: this.isRange ? this.values : this.values
            });
        }
        
        /**
         * 调整数值
         * 
         * @param {jQuery} $handle 手柄元素
         * @param {number} value 新数值
         */
        adjustValue($handle, value) {
            // 限制在范围内
            value = Math.max(this.config.min, Math.min(this.config.max, value));
            
            // 应用步长
            if (this.config.step > 0) {
                value = Math.round(value / this.config.step) * this.config.step;
            }
            
            // 应用精度
            if (this.config.precision > 0) {
                value = parseFloat(value.toFixed(this.config.precision));
            }
            
            if (this.isRange) {
                const isMin = $handle.hasClass('xun-slider-handle-min');
                
                if (isMin) {
                    // 确保最小值不超过最大值
                    value = Math.min(value, this.values.max);
                    this.values.min = value;
                } else {
                    // 确保最大值不小于最小值
                    value = Math.max(value, this.values.min);
                    this.values.max = value;
                }
            } else {
                this.values = value;
            }
            
            this.updateUI();
            this.updateInputs();
            this.updateTooltips();
            this.updatePreview();
            
            // 触发变更事件
            this.$container.trigger('xun:slider:change', {
                value: this.isRange ? this.values : this.values,
                handle: $handle
            });
        }
        
        /**
         * 处理输入框变化
         * 
         * @param {jQuery} $input 输入框元素
         */
        handleInputChange($input) {
            const value = parseFloat($input.val());
            
            if (isNaN(value)) return;
            
            if (this.isRange) {
                if ($input.hasClass('xun-slider-input-min')) {
                    this.adjustValue(this.$handles.filter('.xun-slider-handle-min'), value);
                } else if ($input.hasClass('xun-slider-input-max')) {
                    this.adjustValue(this.$handles.filter('.xun-slider-handle-max'), value);
                }
            } else {
                this.adjustValue(this.$handles, value);
            }
        }
        
        /**
         * 更新UI显示
         */
        updateUI() {
            const range = this.config.max - this.config.min;
            
            if (this.isRange) {
                const minPercent = ((this.values.min - this.config.min) / range) * 100;
                const maxPercent = ((this.values.max - this.config.min) / range) * 100;
                
                // 更新进度条
                this.$progress.css({
                    left: minPercent + '%',
                    width: (maxPercent - minPercent) + '%'
                });
                
                // 更新手柄位置
                this.$handles.filter('.xun-slider-handle-min').css('left', minPercent + '%');
                this.$handles.filter('.xun-slider-handle-max').css('left', maxPercent + '%');
                
                // 更新ARIA属性
                this.$handles.filter('.xun-slider-handle-min').attr('aria-valuenow', this.values.min);
                this.$handles.filter('.xun-slider-handle-max').attr('aria-valuenow', this.values.max);
            } else {
                const percent = ((this.values - this.config.min) / range) * 100;
                
                // 更新进度条
                this.$progress.css('width', percent + '%');
                
                // 更新手柄位置
                this.$handles.css('left', percent + '%');
                
                // 更新ARIA属性
                this.$handles.attr('aria-valuenow', this.values);
            }
        }
        
        /**
         * 更新输入框数值
         */
        updateInputs() {
            if (this.isRange) {
                this.$inputs.filter('.xun-slider-input-min').val(this.values.min);
                this.$inputs.filter('.xun-slider-input-max').val(this.values.max);
            } else {
                this.$inputs.val(this.values);
            }
        }
        
        /**
         * 更新工具提示
         */
        updateTooltips() {
            if (!this.config.tooltip) return;
            
            if (this.isRange) {
                this.$tooltips.filter('.xun-slider-tooltip-min').text(this.formatValue(this.values.min));
                this.$tooltips.filter('.xun-slider-tooltip-max').text(this.formatValue(this.values.max));
            } else {
                this.$tooltips.text(this.formatValue(this.values));
            }
        }
        
        /**
         * 显示工具提示
         * 
         * @param {jQuery} $handle 手柄元素
         */
        showTooltip($handle) {
            if (!this.config.tooltip) return;
            
            let $tooltip;
            if (this.isRange) {
                if ($handle.hasClass('xun-slider-handle-min')) {
                    $tooltip = this.$tooltips.filter('.xun-slider-tooltip-min');
                } else {
                    $tooltip = this.$tooltips.filter('.xun-slider-tooltip-max');
                }
            } else {
                $tooltip = this.$tooltips;
            }
            
            $tooltip.addClass('opacity-100').removeClass('opacity-0');
        }
        
        /**
         * 隐藏工具提示
         * 
         * @param {jQuery} $handle 手柄元素
         */
        hideTooltip($handle) {
            if (!this.config.tooltip) return;
            
            let $tooltip;
            if (this.isRange) {
                if ($handle.hasClass('xun-slider-handle-min')) {
                    $tooltip = this.$tooltips.filter('.xun-slider-tooltip-min');
                } else {
                    $tooltip = this.$tooltips.filter('.xun-slider-tooltip-max');
                }
            } else {
                $tooltip = this.$tooltips;
            }
            
            $tooltip.addClass('opacity-0').removeClass('opacity-100');
        }
        
        /**
         * 更新实时预览
         */
        updatePreview() {
            if (this.$preview.length === 0) return;
            
            let previewText;
            if (this.isRange) {
                previewText = `${this.formatValue(this.values.min)} - ${this.formatValue(this.values.max)}`;
            } else {
                previewText = this.formatValue(this.values);
            }
            
            this.$preview.text(previewText);
        }
        
        /**
         * 格式化数值显示
         * 
         * @param {number} value 数值
         * @returns {string} 格式化后的字符串
         */
        formatValue(value) {
            if (typeof this.config.formatValue === 'function') {
                return this.config.formatValue(value);
            }
            
            // 默认格式化
            let formatted = value.toString();
            
            if (this.config.precision > 0) {
                formatted = value.toFixed(this.config.precision);
            }
            
            return formatted;
        }
        
        /**
         * 获取当前手柄的值
         * 
         * @param {jQuery} $handle 手柄元素
         * @returns {number} 当前值
         */
        getCurrentValue($handle) {
            if (this.isRange) {
                return $handle.hasClass('xun-slider-handle-min') ? this.values.min : this.values.max;
            }
            return this.values;
        }
        
        /**
         * 获取当前所有值
         * 
         * @returns {number|Object} 当前值
         */
        getValue() {
            return this.isRange ? { ...this.values } : this.values;
        }
        
        /**
         * 设置值
         *
         * @param {number|Object} value 新值
         */
        setValue(value) {
            if (this.isRange) {
                if (typeof value === 'object' && value !== null) {
                    if (value.min !== undefined) {
                        this.adjustValue(this.$handles.filter('.xun-slider-handle-min'), value.min);
                    }
                    if (value.max !== undefined) {
                        this.adjustValue(this.$handles.filter('.xun-slider-handle-max'), value.max);
                    }
                }
            } else {
                if (typeof value === 'number') {
                    this.adjustValue(this.$handles, value);
                }
            }
        }

        /**
         * 添加动态样式
         */
        addDynamicStyles() {
            if ($('#xun-slider-styles').length > 0) return;

            const styles = `
                <style id="xun-slider-styles">
                    .xun-slider-field {
                        user-select: none;
                    }

                    .xun-slider-track {
                        position: relative;
                    }

                    .xun-slider-handle {
                        transform: translateX(-50%);
                        z-index: 10;
                    }

                    .xun-slider-handle:hover {
                        transform: translateX(-50%) scale(1.1);
                    }

                    .xun-slider-handle:focus {
                        outline: none;
                        box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
                    }

                    .xun-slider-handle.xun-dragging {
                        transform: translateX(-50%) scale(1.2);
                        cursor: grabbing !important;
                    }

                    .xun-slider-tooltip {
                        transform: translateX(-50%);
                        white-space: nowrap;
                        z-index: 20;
                    }

                    .xun-slider-progress {
                        pointer-events: none;
                    }

                    .xun-slider-animate .xun-slider-handle {
                        transition: transform 0.2s ease;
                    }

                    .xun-slider-animate .xun-slider-progress {
                        transition: all 0.2s ease;
                    }

                    .xun-slider-dragging {
                        cursor: grabbing !important;
                    }

                    /* 移动端优化 */
                    @media (max-width: 768px) {
                        .xun-slider-handle {
                            width: 24px;
                            height: 24px;
                        }

                        .xun-slider-track {
                            height: 8px;
                        }
                    }

                    /* 触摸设备优化 */
                    @media (hover: none) and (pointer: coarse) {
                        .xun-slider-handle:hover {
                            transform: translateX(-50%);
                        }
                    }
                </style>
            `;

            $('head').append(styles);
        }
    }

    /**
     * 初始化所有滑块字段
     */
    function initSliderFields() {
        $('.xun-slider-field').each(function() {
            const $container = $(this);
            if (!$container.data('xun-slider-initialized')) {
                new XunSlider($container);
                $container.data('xun-slider-initialized', true);
            }
        });
    }

    // 文档就绪时初始化
    $(document).ready(function() {
        initSliderFields();
    });

    // 支持动态加载的字段
    $(document).on('xun:field:loaded', function() {
        initSliderFields();
    });

})(jQuery);
