<?php
/**
 * Xun Framework 文档系统 - 配置文件
 *
 * 这个文件包含了文档系统的核心配置信息，包括路径设置、
 * 主题配置、导航结构等。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    define('ABSPATH', dirname(__DIR__) . '/');
}

/**
 * 文档系统版本
 */
define('XUN_DOCS_VERSION', '1.0.0');

/**
 * 文档系统路径配置
 */
define('XUN_DOCS_PATH', ABSPATH);
define('XUN_DOCS_URL', 'http' . (isset($_SERVER['HTTPS']) ? 's' : '') . '://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']));
define('XUN_DOCS_ASSETS_URL', XUN_DOCS_URL . '/assets');

/**
 * 框架路径配置
 */
define('XUN_FRAMEWORK_PATH', dirname(ABSPATH) . '/framework');
define('XUN_FRAMEWORK_URL', dirname(XUN_DOCS_URL) . '/framework');

/**
 * 文档系统配置数组
 */
$xun_docs_config = [
    // 基本信息
    'site_title' => 'Xun Framework',
    'site_description' => '现代化的WordPress选项框架',
    'site_keywords' => 'WordPress, Framework, Options, PHP, TailwindCSS',
    'author' => 'June',
    'version' => XUN_DOCS_VERSION,
    
    // 主题设置
    'default_theme' => 'dark', // dark 或 light
    'enable_theme_switch' => true,
    
    // 功能开关
    'enable_search' => true,
    'enable_code_copy' => true,
    'enable_live_preview' => true,
    'enable_analytics' => false,
    
    // 分页设置
    'posts_per_page' => 10,
    'search_results_per_page' => 20,
    
    // 缓存设置
    'enable_cache' => true,
    'cache_duration' => 3600, // 1小时
    
    // 社交链接
    'social_links' => [
        'github' => 'https://github.com/xuntheme/xun-framework',
        'website' => 'https://www.xuntheme.com',
        'email' => '<EMAIL>'
    ]
];

/**
 * 导航菜单配置
 */
$xun_docs_navigation = [
    'main' => [
        [
            'title' => '开始使用',
            'icon' => 'rocket-launch',
            'items' => [
                ['title' => '快速开始', 'url' => 'getting-started', 'desc' => '5分钟快速上手'],
                ['title' => '安装指南', 'url' => 'installation', 'desc' => '详细的安装步骤'],
                ['title' => '基础用法', 'url' => 'basic-usage', 'desc' => '核心概念和用法'],
                ['title' => '示例代码', 'url' => 'examples', 'desc' => '完整的使用示例']
            ]
        ],
        [
            'title' => '字段类型',
            'icon' => 'squares-2x2',
            'items' => [
                ['title' => '文本字段', 'url' => 'field-types/text', 'desc' => '基础文本输入'],
                ['title' => '选择字段', 'url' => 'field-types/select', 'desc' => '下拉选择器'],
                ['title' => '媒体字段', 'url' => 'field-types/media', 'desc' => '图片和文件选择'],
                ['title' => '开关字段', 'url' => 'field-types/switch', 'desc' => '开关切换器'],
                ['title' => '颜色字段', 'url' => 'field-types/color', 'desc' => '颜色选择器'],
                ['title' => '日期字段', 'url' => 'field-types/date', 'desc' => '日期时间选择'],
                ['title' => '重复字段', 'url' => 'field-types/repeater', 'desc' => '动态重复内容'],
                ['title' => '查看全部', 'url' => 'field-types', 'desc' => '所有字段类型', 'highlight' => true]
            ]
        ],
        [
            'title' => '高级功能',
            'icon' => 'cog-6-tooth',
            'items' => [
                ['title' => 'API 参考', 'url' => 'api-reference', 'desc' => '完整的API文档'],
                ['title' => '钩子过滤器', 'url' => 'hooks-filters', 'desc' => '扩展和定制'],
                ['title' => '自定义字段', 'url' => 'customization', 'desc' => '创建自定义字段'],
                ['title' => '最佳实践', 'url' => 'best-practices', 'desc' => '开发建议'],
                ['title' => '故障排除', 'url' => 'troubleshooting', 'desc' => '常见问题解决']
            ]
        ],
        [
            'title' => '资源',
            'icon' => 'book-open',
            'items' => [
                ['title' => '更新日志', 'url' => 'changelog', 'desc' => '版本更新记录'],
                ['title' => '迁移指南', 'url' => 'migration', 'desc' => '版本升级指南'],
                ['title' => '贡献指南', 'url' => 'contributing', 'desc' => '参与项目开发'],
                ['title' => '许可证', 'url' => 'license', 'desc' => 'MIT开源许可']
            ]
        ]
    ]
];

/**
 * 字段类型配置
 */
$xun_docs_field_types = [
    // 基础字段
    'basic' => [
        'title' => '基础字段',
        'description' => '常用的基础输入字段类型',
        'fields' => [
            'text' => ['title' => '文本字段', 'icon' => 'pencil', 'color' => 'blue'],
            'textarea' => ['title' => '文本域', 'icon' => 'document-text', 'color' => 'green'],
            'number' => ['title' => '数字字段', 'icon' => 'hashtag', 'color' => 'purple'],
            'date' => ['title' => '日期字段', 'icon' => 'calendar', 'color' => 'indigo'],
            'time' => ['title' => '时间字段', 'icon' => 'clock', 'color' => 'pink']
        ]
    ],
    
    // 选择字段
    'selection' => [
        'title' => '选择字段',
        'description' => '各种选择和切换类型的字段',
        'fields' => [
            'select' => ['title' => '选择器', 'icon' => 'chevron-down', 'color' => 'blue'],
            'radio' => ['title' => '单选按钮', 'icon' => 'radio', 'color' => 'green'],
            'checkbox' => ['title' => '复选框', 'icon' => 'check-square', 'color' => 'purple'],
            'switch' => ['title' => '开关', 'icon' => 'switch-horizontal', 'color' => 'indigo']
        ]
    ],
    
    // 媒体字段
    'media' => [
        'title' => '媒体字段',
        'description' => '图片、文件和媒体相关的字段',
        'fields' => [
            'media' => ['title' => '媒体选择', 'icon' => 'photo', 'color' => 'blue'],
            'gallery' => ['title' => '图片画廊', 'icon' => 'view-grid', 'color' => 'green'],
            'icon' => ['title' => '图标选择', 'icon' => 'star', 'color' => 'yellow']
        ]
    ],
    
    // 设计字段
    'design' => [
        'title' => '设计字段',
        'description' => '颜色、样式和设计相关的字段',
        'fields' => [
            'color' => ['title' => '颜色选择', 'icon' => 'color-swatch', 'color' => 'red'],
            'background' => ['title' => '背景设置', 'icon' => 'photograph', 'color' => 'blue'],
            'border' => ['title' => '边框设置', 'icon' => 'view-grid-add', 'color' => 'green'],
            'palette' => ['title' => '调色板', 'icon' => 'color-swatch', 'color' => 'purple']
        ]
    ],
    
    // 交互字段
    'interactive' => [
        'title' => '交互字段',
        'description' => '滑块、按钮等交互式字段',
        'fields' => [
            'slider' => ['title' => '滑块', 'icon' => 'adjustments', 'color' => 'blue'],
            'button' => ['title' => '按钮', 'icon' => 'cursor-click', 'color' => 'green'],
            'accordion' => ['title' => '手风琴', 'icon' => 'menu-alt-2', 'color' => 'purple']
        ]
    ],
    
    // 高级字段
    'advanced' => [
        'title' => '高级字段',
        'description' => '复杂的高级功能字段',
        'fields' => [
            'code' => ['title' => '代码编辑', 'icon' => 'code', 'color' => 'gray'],
            'repeater' => ['title' => '重复器', 'icon' => 'duplicate', 'color' => 'blue'],
            'sortable' => ['title' => '排序列表', 'icon' => 'sort-ascending', 'color' => 'green'],
            'sorter' => ['title' => '分拣器', 'icon' => 'switch-vertical', 'color' => 'purple']
        ]
    ]
];

/**
 * 获取配置值
 *
 * @param string $key 配置键名
 * @param mixed $default 默认值
 * @return mixed
 */
function xun_docs_get_config($key, $default = null) {
    global $xun_docs_config;
    return $xun_docs_config[$key] ?? $default;
}

/**
 * 获取导航菜单
 *
 * @param string $menu 菜单名称
 * @return array
 */
function xun_docs_get_navigation($menu = 'main') {
    global $xun_docs_navigation;
    return $xun_docs_navigation[$menu] ?? [];
}

/**
 * 获取字段类型配置
 *
 * @param string $category 分类名称
 * @return array
 */
function xun_docs_get_field_types($category = null) {
    global $xun_docs_field_types;
    
    if ($category) {
        return $xun_docs_field_types[$category] ?? [];
    }
    
    return $xun_docs_field_types;
}
