/**
 * Xun Framework 文档系统 - 主要交互脚本
 *
 * 提供文档系统的核心交互功能，包括导航、滚动、
 * 移动端菜单等基础功能。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

(function() {
    'use strict';

    /**
     * 文档系统主对象
     */
    const XunDocs = {
        // 配置选项
        config: {
            scrollOffset: 80,
            animationDuration: 300,
            debounceDelay: 150
        },

        // DOM元素缓存
        elements: {},

        /**
         * 初始化
         */
        init() {
            this.cacheElements();
            this.bindEvents();
            this.initMobileMenu();
            this.initBackToTop();
            this.initSmoothScroll();
            this.initCodeCopy();
            this.updateActiveNavigation();
        },

        /**
         * 缓存DOM元素
         */
        cacheElements() {
            this.elements = {
                // 导航相关
                mobileMenuButton: document.getElementById('mobile-menu-button'),
                mobileSidebar: document.getElementById('mobile-sidebar'),
                sidebarOverlay: document.getElementById('sidebar-overlay'),
                closeMobileSidebar: document.getElementById('close-mobile-sidebar'),
                
                // 搜索相关
                mobileSearchButton: document.getElementById('mobile-search-button'),
                mobileSearch: document.getElementById('mobile-search'),
                searchInput: document.getElementById('search-input'),
                mobileSearchInput: document.getElementById('mobile-search-input'),
                
                // 其他元素
                backToTop: document.getElementById('back-to-top'),
                sidebar: document.getElementById('sidebar'),
                mainContent: document.getElementById('main-content')
            };
        },

        /**
         * 绑定事件
         */
        bindEvents() {
            // 移动端菜单
            if (this.elements.mobileMenuButton) {
                this.elements.mobileMenuButton.addEventListener('click', () => this.toggleMobileMenu());
            }
            
            if (this.elements.closeMobileSidebar) {
                this.elements.closeMobileSidebar.addEventListener('click', () => this.closeMobileMenu());
            }
            
            if (this.elements.sidebarOverlay) {
                this.elements.sidebarOverlay.addEventListener('click', () => this.closeMobileMenu());
            }

            // 移动端搜索
            if (this.elements.mobileSearchButton) {
                this.elements.mobileSearchButton.addEventListener('click', () => this.toggleMobileSearch());
            }

            // 返回顶部
            if (this.elements.backToTop) {
                this.elements.backToTop.addEventListener('click', () => this.scrollToTop());
            }

            // 滚动事件
            window.addEventListener('scroll', this.debounce(() => {
                this.handleScroll();
            }, this.config.debounceDelay));

            // 窗口大小变化
            window.addEventListener('resize', this.debounce(() => {
                this.handleResize();
            }, this.config.debounceDelay));

            // 键盘事件
            document.addEventListener('keydown', (e) => this.handleKeydown(e));

            // 链接点击事件（用于平滑滚动）
            document.addEventListener('click', (e) => this.handleLinkClick(e));
        },

        /**
         * 初始化移动端菜单
         */
        initMobileMenu() {
            // 确保移动端菜单初始状态正确
            if (this.elements.mobileSidebar) {
                this.elements.mobileSidebar.classList.add('-translate-x-full');
            }
        },

        /**
         * 切换移动端菜单
         */
        toggleMobileMenu() {
            if (!this.elements.mobileSidebar || !this.elements.sidebarOverlay) return;

            const isOpen = !this.elements.mobileSidebar.classList.contains('-translate-x-full');
            
            if (isOpen) {
                this.closeMobileMenu();
            } else {
                this.openMobileMenu();
            }
        },

        /**
         * 打开移动端菜单
         */
        openMobileMenu() {
            if (!this.elements.mobileSidebar || !this.elements.sidebarOverlay) return;

            this.elements.mobileSidebar.classList.remove('-translate-x-full');
            this.elements.sidebarOverlay.classList.remove('hidden');
            document.body.style.overflow = 'hidden';

            // 设置焦点到关闭按钮
            if (this.elements.closeMobileSidebar) {
                setTimeout(() => {
                    this.elements.closeMobileSidebar.focus();
                }, 100);
            }
        },

        /**
         * 关闭移动端菜单
         */
        closeMobileMenu() {
            if (!this.elements.mobileSidebar || !this.elements.sidebarOverlay) return;

            this.elements.mobileSidebar.classList.add('-translate-x-full');
            this.elements.sidebarOverlay.classList.add('hidden');
            document.body.style.overflow = '';

            // 恢复焦点到菜单按钮
            if (this.elements.mobileMenuButton) {
                this.elements.mobileMenuButton.focus();
            }
        },

        /**
         * 切换移动端搜索
         */
        toggleMobileSearch() {
            if (!this.elements.mobileSearch) return;

            const isHidden = this.elements.mobileSearch.classList.contains('hidden');
            
            if (isHidden) {
                this.elements.mobileSearch.classList.remove('hidden');
                if (this.elements.mobileSearchInput) {
                    setTimeout(() => {
                        this.elements.mobileSearchInput.focus();
                    }, 100);
                }
            } else {
                this.elements.mobileSearch.classList.add('hidden');
            }
        },

        /**
         * 初始化返回顶部按钮
         */
        initBackToTop() {
            this.updateBackToTopVisibility();
        },

        /**
         * 更新返回顶部按钮可见性
         */
        updateBackToTopVisibility() {
            if (!this.elements.backToTop) return;

            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            const shouldShow = scrollTop > 300;

            if (shouldShow) {
                this.elements.backToTop.classList.remove('opacity-0', 'invisible');
                this.elements.backToTop.classList.add('opacity-100', 'visible');
            } else {
                this.elements.backToTop.classList.add('opacity-0', 'invisible');
                this.elements.backToTop.classList.remove('opacity-100', 'visible');
            }
        },

        /**
         * 滚动到顶部
         */
        scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        },

        /**
         * 初始化平滑滚动
         */
        initSmoothScroll() {
            // 为所有内部锚点链接添加平滑滚动
            const anchorLinks = document.querySelectorAll('a[href^="#"]');
            anchorLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    const href = link.getAttribute('href');
                    if (href === '#') return;
                    
                    const target = document.querySelector(href);
                    if (target) {
                        e.preventDefault();
                        this.scrollToElement(target);
                    }
                });
            });
        },

        /**
         * 滚动到指定元素
         */
        scrollToElement(element) {
            const elementTop = element.getBoundingClientRect().top + window.pageYOffset;
            const offsetTop = elementTop - this.config.scrollOffset;

            window.scrollTo({
                top: offsetTop,
                behavior: 'smooth'
            });
        },

        /**
         * 初始化代码复制功能
         */
        initCodeCopy() {
            const codeBlocks = document.querySelectorAll('pre code');
            
            codeBlocks.forEach(codeBlock => {
                const pre = codeBlock.parentElement;
                if (!pre || pre.querySelector('.copy-button')) return;

                // 创建复制按钮
                const copyButton = document.createElement('button');
                copyButton.className = 'copy-button absolute top-2 right-2 p-2 bg-gray-700 hover:bg-gray-600 text-white rounded-md opacity-0 group-hover:opacity-100 transition-opacity';
                copyButton.innerHTML = `
                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                    </svg>
                `;
                copyButton.setAttribute('aria-label', '复制代码');

                // 添加相对定位和group类
                pre.style.position = 'relative';
                pre.classList.add('group');

                // 添加复制按钮
                pre.appendChild(copyButton);

                // 绑定复制事件
                copyButton.addEventListener('click', () => {
                    this.copyCodeToClipboard(codeBlock, copyButton);
                });
            });
        },

        /**
         * 复制代码到剪贴板
         */
        async copyCodeToClipboard(codeElement, button) {
            const code = codeElement.textContent;
            
            try {
                await navigator.clipboard.writeText(code);
                this.showCopySuccess(button);
            } catch (err) {
                // 降级方案
                this.fallbackCopyTextToClipboard(code, button);
            }
        },

        /**
         * 降级复制方案
         */
        fallbackCopyTextToClipboard(text, button) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                this.showCopySuccess(button);
            } catch (err) {
                console.error('复制失败:', err);
            }
            
            document.body.removeChild(textArea);
        },

        /**
         * 显示复制成功提示
         */
        showCopySuccess(button) {
            const originalHTML = button.innerHTML;
            button.innerHTML = `
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                </svg>
            `;
            button.classList.add('bg-green-600');
            
            setTimeout(() => {
                button.innerHTML = originalHTML;
                button.classList.remove('bg-green-600');
            }, 2000);
        },

        /**
         * 更新活动导航
         */
        updateActiveNavigation() {
            // 这里可以添加基于滚动位置更新导航状态的逻辑
            // 暂时保留空实现，后续可以扩展
        },

        /**
         * 处理滚动事件
         */
        handleScroll() {
            this.updateBackToTopVisibility();
            this.updateActiveNavigation();
        },

        /**
         * 处理窗口大小变化
         */
        handleResize() {
            // 在大屏幕上自动关闭移动端菜单
            if (window.innerWidth >= 1024) {
                this.closeMobileMenu();
                if (this.elements.mobileSearch) {
                    this.elements.mobileSearch.classList.add('hidden');
                }
            }
        },

        /**
         * 处理键盘事件
         */
        handleKeydown(e) {
            // ESC键关闭移动端菜单
            if (e.key === 'Escape') {
                this.closeMobileMenu();
                if (this.elements.mobileSearch && !this.elements.mobileSearch.classList.contains('hidden')) {
                    this.elements.mobileSearch.classList.add('hidden');
                }
            }
        },

        /**
         * 处理链接点击
         */
        handleLinkClick(e) {
            const link = e.target.closest('a');
            if (!link) return;

            // 如果是移动端菜单中的链接，点击后关闭菜单
            if (this.elements.mobileSidebar && this.elements.mobileSidebar.contains(link)) {
                this.closeMobileMenu();
            }
        },

        /**
         * 防抖函数
         */
        debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        },

        /**
         * 节流函数
         */
        throttle(func, limit) {
            let inThrottle;
            return function() {
                const args = arguments;
                const context = this;
                if (!inThrottle) {
                    func.apply(context, args);
                    inThrottle = true;
                    setTimeout(() => inThrottle = false, limit);
                }
            };
        },

        /**
         * 工具函数：检查元素是否在视口中
         */
        isElementInViewport(element) {
            const rect = element.getBoundingClientRect();
            return (
                rect.top >= 0 &&
                rect.left >= 0 &&
                rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
                rect.right <= (window.innerWidth || document.documentElement.clientWidth)
            );
        },

        /**
         * 工具函数：获取元素距离顶部的偏移量
         */
        getElementOffset(element) {
            let offsetTop = 0;
            while (element) {
                offsetTop += element.offsetTop;
                element = element.offsetParent;
            }
            return offsetTop;
        }
    };

    // DOM加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => XunDocs.init());
    } else {
        XunDocs.init();
    }

    // 将XunDocs暴露到全局作用域，供其他脚本使用
    window.XunDocs = XunDocs;

})();
