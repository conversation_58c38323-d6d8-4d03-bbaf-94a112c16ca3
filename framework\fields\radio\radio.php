<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework 单选按钮字段类型
 * 
 * 这个类实现了现代化的单选按钮字段功能。
 * 提供比CSF更好的用户体验，包括搜索、分组、描述等高级功能。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Field_radio' ) ) {
    
    /**
     * XUN_Field_radio 单选按钮字段类
     * 
     * 提供现代化单选按钮字段的完整功能，包括：
     * - 现代化UI设计
     * - 实时搜索过滤
     * - 选项分组
     * - 图标支持
     * - 描述信息
     * - 键盘导航
     * - 无障碍访问
     * - 自定义样式
     * 
     * @since 1.0
     */
    class XUN_Field_radio extends XUN_Fields {
        
        /**
         * 构造函数
         * 
         * 初始化单选按钮字段实例。
         * 
         * @since 1.0
         * 
         * @param array  $field  字段配置数组
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }
        
        /**
         * 渲染单选按钮字段
         *
         * 输出现代化单选按钮字段的HTML代码。
         *
         * @since 1.0
         */
        public function render() {

            // 获取字段配置
            $options = ! empty( $this->field['options'] ) ? $this->field['options'] : array();
            $inline = ! empty( $this->field['inline'] ) ? $this->field['inline'] : false;
            $searchable = ! empty( $this->field['searchable'] ) ? $this->field['searchable'] : false;
            $icons = ! empty( $this->field['icons'] ) ? $this->field['icons'] : array();
            $descriptions = ! empty( $this->field['descriptions'] ) ? $this->field['descriptions'] : array();
            $color = ! empty( $this->field['color'] ) ? $this->field['color'] : 'blue';
            $size = ! empty( $this->field['size'] ) ? $this->field['size'] : 'medium';
            $style = ! empty( $this->field['style'] ) ? $this->field['style'] : 'default';
            $show_count = ! empty( $this->field['show_count'] ) ? $this->field['show_count'] : false;

            // 获取字段标题和描述
            $title = ! empty( $this->field['title'] ) ? $this->field['title'] : '';
            $desc = ! empty( $this->field['desc'] ) ? $this->field['desc'] : '';

            // 输出前置内容
            echo $this->field_before();

            // 开始字段容器
            echo '<div class="xun-radio-field" data-field-id="' . esc_attr( $this->field_id() ) . '" data-color="' . esc_attr( $color ) . '" data-size="' . esc_attr( $size ) . '" data-style="' . esc_attr( $style ) . '">';

            // 搜索框
            if ( $searchable && count( $options ) > 5 ) {
                echo '<div class="mb-4">';
                echo '<div class="relative">';
                echo '<div class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none z-10">';
                echo '<svg class="h-4 w-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>';
                echo '</svg>';
                echo '</div>';
                echo '<input type="text" class="xun-radio-search block w-full pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-' . esc_attr( $color ) . '-500 focus:border-' . esc_attr( $color ) . '-500 sm:text-sm" style="padding-left: 2rem;" placeholder="搜索选项..." />';
                echo '</div>';
                echo '</div>';
            }

            // 选项计数
            if ( $show_count ) {
                echo '<div class="mb-3 text-sm text-gray-500">';
                echo '<span class="xun-radio-count">共 ' . count( $options ) . ' 个选项</span>';
                echo '</div>';
            }

            // 单选按钮组容器
            $container_classes = array(
                'xun-radio-group',
                'space-y-3'
            );

            if ( $inline ) {
                $container_classes = array(
                    'xun-radio-group',
                    'flex',
                    'flex-wrap',
                    'gap-4'
                );
            }

            echo '<div class="' . esc_attr( implode( ' ', $container_classes ) ) . '">';

            // 渲染选项
            $this->render_options( $options, $icons, $descriptions, $inline, $color, $size, $style );

            echo '</div>'; // 结束单选按钮组

            // 输出描述文本
            if ( ! empty( $desc ) ) {
                echo '<p class="mt-3 text-sm text-gray-500">';
                echo wp_kses_post( $desc );
                echo '</p>';
            }

            // 输出错误信息
            if ( ! empty( $this->field['_error'] ) ) {
                echo '<p class="mt-2 text-sm text-red-600">';
                echo esc_html( $this->field['_error'] );
                echo '</p>';
            }

            // 输出后置内容（不包括desc，因为我们已经输出了）
            if ( ! empty( $this->field['after'] ) ) {
                echo '<div class="mt-3 xun-after-text">' . $this->field['after'] . '</div>';
            }

            // 输出帮助信息
            if ( ! empty( $this->field['help'] ) ) {
                echo '<div class="mt-2 xun-help">';
                echo '<span class="xun-help-text">' . $this->field['help'] . '</span>';
                echo '<i class="xun-help-icon">?</i>';
                echo '</div>';
            }

            echo '</div>'; // 结束字段容器
        }

        /**
         * 渲染选项
         * 
         * @since 1.0
         * 
         * @param array  $options      选项数组
         * @param array  $icons        图标数组
         * @param array  $descriptions 描述数组
         * @param bool   $inline       是否内联显示
         * @param string $color        颜色主题
         * @param string $size         尺寸
         * @param string $style        样式
         */
        private function render_options( $options, $icons, $descriptions, $inline, $color, $size, $style ) {
            
            foreach ( $options as $option_value => $option_label ) {
                
                // 检查是否为分组选项
                if ( is_array( $option_label ) ) {
                    $this->render_option_group( $option_value, $option_label, $icons, $descriptions, $inline, $color, $size, $style );
                } else {
                    $this->render_single_option( $option_value, $option_label, $icons, $descriptions, $inline, $color, $size, $style );
                }
            }
        }

        /**
         * 渲染选项组
         * 
         * @since 1.0
         */
        private function render_option_group( $group_name, $group_options, $icons, $descriptions, $inline, $color, $size, $style ) {
            
            echo '<div class="xun-radio-group-section">';
            echo '<h4 class="text-sm font-medium text-gray-900 mb-3">' . esc_html( $group_name ) . '</h4>';
            echo '<div class="ml-4 space-y-2">';
            
            foreach ( $group_options as $option_value => $option_label ) {
                $this->render_single_option( $option_value, $option_label, $icons, $descriptions, $inline, $color, $size, $style );
            }
            
            echo '</div>';
            echo '</div>';
        }

        /**
         * 渲染单个选项
         * 
         * @since 1.0
         */
        private function render_single_option( $option_value, $option_label, $icons, $descriptions, $inline, $color, $size, $style ) {
            
            $is_checked = ( $this->value == $option_value );
            $has_icon = isset( $icons[ $option_value ] );
            $has_description = isset( $descriptions[ $option_value ] );
            
            // 选项容器类
            $option_classes = array(
                'xun-radio-option',
                'relative',
                'flex',
                'items-start',
                'cursor-pointer',
                'rounded-lg',
                'border',
                'p-4',
                'transition-all',
                'duration-200',
                'hover:bg-gray-50',
                'focus-within:ring-2',
                'focus-within:ring-offset-2',
                'focus-within:ring-' . $color . '-500'
            );

            if ( $is_checked ) {
                $option_classes[] = 'bg-' . $color . '-50';
                $option_classes[] = 'border-' . $color . '-200';
                $option_classes[] = 'ring-1';
                $option_classes[] = 'ring-' . $color . '-500';
            } else {
                $option_classes[] = 'border-gray-200';
            }

            if ( $inline && ! $has_description ) {
                $option_classes[] = 'inline-flex';
                $option_classes[] = 'mr-4';
                $option_classes[] = 'mb-2';
            }

            echo '<div class="' . esc_attr( implode( ' ', $option_classes ) ) . '" data-value="' . esc_attr( $option_value ) . '">';

            // 单选按钮输入
            echo '<div class="flex items-center h-5">';
            echo '<input ';
            echo 'id="' . esc_attr( $this->field_id() . '_' . $option_value ) . '" ';
            echo 'name="' . esc_attr( $this->field_name() ) . '" ';
            echo 'type="radio" ';
            echo 'value="' . esc_attr( $option_value ) . '" ';
            echo 'class="xun-radio-input h-4 w-4 text-' . esc_attr( $color ) . '-600 border-gray-300 focus:ring-' . esc_attr( $color ) . '-500" ';
            echo $is_checked ? 'checked ' : '';
            echo $this->field_attributes() . ' />';
            echo '</div>';

            // 标签内容
            echo '<div class="ml-3 flex-1">';
            echo '<label for="' . esc_attr( $this->field_id() . '_' . $option_value ) . '" class="block cursor-pointer">';
            
            // 图标和标题行
            echo '<div class="flex items-center">';
            
            if ( $has_icon ) {
                echo '<div class="mr-3 flex-shrink-0">';
                echo $icons[ $option_value ];
                echo '</div>';
            }
            
            echo '<div class="text-sm font-medium text-gray-900">';
            echo esc_html( $option_label );
            echo '</div>';
            
            echo '</div>';
            
            // 描述信息
            if ( $has_description ) {
                echo '<div class="mt-1 text-sm text-gray-500">';
                echo esc_html( $descriptions[ $option_value ] );
                echo '</div>';
            }
            
            echo '</label>';
            echo '</div>';

            echo '</div>'; // 结束选项容器
        }
        
        /**
         * 加载字段资源
         * 
         * 加载单选按钮字段所需的CSS和JavaScript文件。
         * 
         * @since 1.0
         */
        public function enqueue() {
            
            // 加载字段特定的脚本
            wp_enqueue_script( 
                'xun-field-radio', 
                XUN_Setup::$url . '/assets/js/fields/radio.js', 
                array( 'jquery' ), 
                XUN_VERSION, 
                true 
            );
        }
        
        /**
         * 验证单选按钮字段值
         * 
         * 验证选中的值是否在可选选项中。
         * 
         * @since 1.0
         * 
         * @param mixed $value 要验证的值
         * 
         * @return mixed 验证后的值
         */
        public function validate( $value ) {
            
            $options = ! empty( $this->field['options'] ) ? $this->field['options'] : array();
            
            // 展开分组选项
            $flat_options = array();
            foreach ( $options as $key => $option ) {
                if ( is_array( $option ) ) {
                    $flat_options = array_merge( $flat_options, $option );
                } else {
                    $flat_options[ $key ] = $option;
                }
            }
            
            // 验证值是否在选项中
            if ( ! empty( $value ) && ! array_key_exists( $value, $flat_options ) ) {
                $value = '';
            }
            
            // 应用自定义验证过滤器
            $value = apply_filters( 'xun_validate_radio_field', $value, $this->field );
            $value = apply_filters( "xun_validate_radio_field_{$this->field['id']}", $value, $this->field );
            
            return $value;
        }
        
        /**
         * 获取字段配置示例
         * 
         * 返回单选按钮字段的配置示例，用于文档和开发参考。
         * 
         * @since 1.0
         * 
         * @return array 配置示例数组
         */
        public static function get_config_example() {
            
            return array(
                'id'          => 'radio_field_example',
                'type'        => 'radio',
                'title'       => '单选按钮字段示例',
                'desc'        => '这是一个单选按钮字段的描述',
                'options'     => array(
                    'option1' => '选项一',
                    'option2' => '选项二',
                    'option3' => '选项三',
                ),
                'default'     => 'option1',
                'inline'      => false,
                'searchable'  => true,
                'color'       => 'blue',
                'size'        => 'medium',
                'style'       => 'default',
                'show_count'  => true,
                'icons'       => array(
                    'option1' => '<svg>...</svg>',
                    'option2' => '<svg>...</svg>',
                ),
                'descriptions' => array(
                    'option1' => '选项一的详细描述',
                    'option2' => '选项二的详细描述',
                ),
                'class'       => 'custom-class',
                'before'      => '前置内容',
                'after'       => '后置内容',
                'help'        => '帮助信息',
            );
        }
    }
}
