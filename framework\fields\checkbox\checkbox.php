<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问

/**
 * Checkbox 字段类 - 基础字段
 *
 * 提供复选框功能，作为基础字段供其他复杂字段组合使用。
 * 支持单选、多选、搜索、分组等高级功能。
 *
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.1.0
 */
if ( ! class_exists( 'XUN_Field_checkbox' ) ) {

    /**
     * XUN_Field_checkbox 复选框字段类
     *
     * 基础字段类，提供完整的复选框功能：
     * - 单选和多选模式
     * - 选项搜索和过滤
     * - 全选/反选功能
     * - 选项分组
     * - 数量限制验证
     * - 无障碍访问优化
     *
     * @since 1.1.0
     */
    class XUN_Field_checkbox extends XUN_Fields {

        /**
         * 构造函数
         *
         * 初始化checkbox字段实例。
         *
         * @since 1.1.0
         *
         * @param array  $field  字段配置数组
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }

        /**
         * 渲染checkbox字段 - 基础字段增强版
         *
         * 输出功能完整的复选框字段，支持搜索、分组、验证等功能。
         *
         * @since 1.1.0
         */
        public function render() {

            // 获取字段设置
            $settings = $this->get_field_settings();

            // 处理选项数据
            $options = $this->process_options( $settings['options'] );
            $value = is_array( $this->value ) ? $this->value : array();

            // 输出前置内容
            echo $this->field_before();

            // 主容器 - 增强版
            $container_id = 'xun-checkbox-' . uniqid();
            echo '<div class="xun-checkbox-field bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden" id="' . esc_attr( $container_id ) . '" data-field-id="' . esc_attr( $this->field['id'] ) . '">';

            // 选中计数器（标题由框架统一处理）
            if ( $settings['show_count'] ) {
                echo '<div class="flex items-center justify-end p-4 border-b border-gray-200">';

                echo '<div class="xun-checkbox-counter text-sm text-gray-500">';
                echo '<span class="selected-count">' . count( $value ) . '</span>';
                echo ' / <span class="total-count">' . count( $options ) . '</span> 已选择';
                echo '</div>';

                echo '</div>';
            }

            // 工具栏
            if ( $settings['searchable'] || $settings['select_all'] ) {
                $this->render_toolbar( $settings, $options, $value );
            }

            // 选项容器
            echo '<div class="xun-checkbox-options p-4">';
            $this->render_options( $options, $value, $settings );
            echo '</div>';

            // 隐藏的输入字段
            $this->render_hidden_inputs( $value );

            // 验证消息容器
            echo '<div class="xun-checkbox-validation hidden mt-3 mx-4 mb-4">';
            echo '<div class="p-3 bg-red-50 border border-red-200 rounded-md">';
            echo '<div class="flex">';
            echo '<div class="flex-shrink-0">';
            echo '<svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">';
            echo '<path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />';
            echo '</svg>';
            echo '</div>';
            echo '<div class="ml-3">';
            echo '<p class="text-sm text-red-800 xun-checkbox-validation-message"></p>';
            echo '</div>';
            echo '</div>';
            echo '</div>';
            echo '</div>';

            echo '</div>'; // 关闭主容器

            // 输出后置内容
            echo $this->field_after();
        }

        /**
         * 处理选项数据
         */
        private function process_options( $options ) {
            if ( empty( $options ) ) {
                return array();
            }

            $processed = array();

            foreach ( $options as $key => $option ) {
                if ( is_string( $option ) ) {
                    $processed[ $key ] = array(
                        'label' => $option,
                        'description' => '',
                    );
                } else {
                    $processed[ $key ] = wp_parse_args( $option, array(
                        'label' => '',
                        'description' => '',
                        'disabled' => false,
                    ) );
                }
            }

            return $processed;
        }

        /**
         * 渲染工具栏
         */
        private function render_toolbar( $args, $options, $value ) {
            echo '<div class="xun-checkbox-toolbar flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-0 p-4 bg-gray-50 border-b border-gray-200">';

            // 左侧：搜索和全选
            echo '<div class="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-3">';

            // 搜索框
            if ( $args['searchable'] && count( $options ) > 5 ) {
                echo '<div class="relative">';
                echo '<input type="text" class="xun-checkbox-search w-full sm:w-48 md:w-64 pl-8 sm:pl-10 pr-4 py-2 text-sm border border-gray-300 rounded-md sm:rounded-lg focus:ring-2 focus:ring-indigo-500 focus:border-transparent" placeholder="搜索选项...">';
                echo '<div class="absolute inset-y-0 left-0 pl-2 sm:pl-3 flex items-center pointer-events-none">';
                echo '<svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>';
                echo '</svg>';
                echo '</div>';
                echo '</div>';
            }

            // 全选按钮
            if ( $args['select_all'] && count( $options ) > 1 ) {
                echo '<div class="flex gap-2">';
                echo '<button type="button" class="xun-checkbox-select-all inline-flex items-center justify-center gap-1 sm:gap-2 px-3 py-2 text-xs sm:text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md sm:rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-1 transition-colors duration-200">';
                echo '<svg class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>';
                echo '</svg>';
                echo '<span class="ml-1">全选</span>';
                echo '</button>';

                echo '<button type="button" class="xun-checkbox-clear-all inline-flex items-center justify-center gap-1 sm:gap-2 px-3 py-2 text-xs sm:text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md sm:rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-1 transition-colors duration-200" style="display: none;">';
                echo '<svg class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';
                echo '</svg>';
                echo '<span class="ml-1">清空</span>';
                echo '</button>';
                echo '</div>';
            }

            echo '</div>';

            // 右侧：选中数量和统计
            if ( $args['show_count'] ) {
                echo '<div class="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4 text-xs sm:text-sm text-gray-600 text-center sm:text-left">';
                echo '<span class="xun-checkbox-count">已选择 <span class="font-semibold text-indigo-600">' . count( $value ) . '</span> / ' . count( $options ) . ' 项</span>';

                // 验证提示
                if ( $args['required_min'] > 0 || $args['required_max'] > 0 ) {
                    $min_text = $args['required_min'] > 0 ? '至少' . $args['required_min'] . '项' : '';
                    $max_text = $args['required_max'] > 0 ? '最多' . $args['required_max'] . '项' : '';
                    $requirement = trim( $min_text . ( $min_text && $max_text ? '，' : '' ) . $max_text );

                    if ( $requirement ) {
                        echo '<span class="text-xs text-gray-500">(' . esc_html( $requirement ) . ')</span>';
                    }
                }
                echo '</div>';
            }

            echo '</div>';
        }

        /**
         * 渲染选项 - 现代化 TailwindCSS 样式
         */
        private function render_options( $options, $value, $args ) {
            // 检测是否有任何选项包含描述
            $has_descriptions = false;
            foreach ( $options as $option ) {
                if ( ! empty( $option['description'] ) ) {
                    $has_descriptions = true;
                    break;
                }
            }

            // 使用现代fieldset结构
            echo '<fieldset class="mt-4">';
            echo '<legend class="sr-only">' . esc_html( $this->field['title'] ?? 'Options' ) . '</legend>';

            // 根据是否有描述使用不同的容器样式
            if ( $has_descriptions ) {
                echo '<div class="space-y-4">';
            } else {
                echo '<div class="bg-white rounded-lg border border-gray-200 divide-y divide-gray-200">';
            }

            foreach ( $options as $key => $option ) {
                $this->render_single_option( $key, $option, $value, $args, $has_descriptions );
            }

            echo '</div>'; // 关闭容器
            echo '</fieldset>';
        }

        /**
         * 渲染单个选项 - 无障碍增强版
         */
        private function render_single_option( $key, $option, $value, $args, $has_descriptions = false ) {
            $option_id = $this->field['id'] . '_' . $key;
            $input_name = $this->field_name( '[]' );
            $is_checked = is_array( $value ) && in_array( $key, $value );
            $is_disabled = ! empty( $option['disabled'] );

            if ( $has_descriptions ) {
                // 有描述的样式 - 卡片式布局（整个区域可点击）
                echo '<div class="xun-checkbox-option group relative bg-white rounded-lg border border-gray-200 hover:border-indigo-300 hover:bg-indigo-50 transition-all duration-200 cursor-pointer focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-offset-2" ';
                echo 'role="button" tabindex="0" ';
                echo 'aria-labelledby="' . esc_attr( $option_id . '_label' ) . '" ';
                echo 'aria-describedby="' . esc_attr( $option_id . '_desc' ) . '" ';
                echo 'aria-checked="' . ( $is_checked ? 'true' : 'false' ) . '" ';
                echo 'data-option-key="' . esc_attr( $key ) . '" ';
                echo ( $is_disabled ? 'aria-disabled="true"' : '' ) . '>';

                // 整个区域的点击标签
                echo '<label for="' . esc_attr( $option_id ) . '" class="absolute inset-0 cursor-pointer" aria-hidden="true"></label>';

                echo '<div class="p-4">';
                echo '<div class="flex items-start gap-3">';

                // Checkbox容器 - 左侧
                echo '<div class="flex h-6 shrink-0 items-center">';
                echo '<input id="' . esc_attr( $option_id ) . '" type="checkbox" name="' . esc_attr( $input_name ) . '" value="' . esc_attr( $key ) . '" ';
                echo 'class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 xun-checkbox-input transition-colors duration-200 relative z-10"';
                echo checked( $is_checked, true, false ) . ( $is_disabled ? ' disabled' : '' ) . '>';
                echo '</div>';

                // 标签和描述内容 - 右侧
                echo '<div class="flex-1 min-w-0">';
                echo '<div id="' . esc_attr( $option_id . '_label' ) . '" class="text-sm font-medium text-gray-900 group-hover:text-indigo-900 transition-colors duration-200">';
                echo esc_html( $option['label'] );
                echo '</div>';
                if ( ! empty( $option['description'] ) ) {
                    echo '<p id="' . esc_attr( $option_id . '_desc' ) . '" class="mt-1 text-sm text-gray-500 group-hover:text-indigo-700 transition-colors duration-200">' . esc_html( $option['description'] ) . '</p>';
                }
                echo '</div>';

                echo '</div>'; // 关闭flex容器
                echo '</div>'; // 关闭padding容器
                echo '</div>'; // 关闭主容器
            } else {
                // 无描述的样式 - 列表式布局（整个区域可点击）
                echo '<div class="xun-checkbox-option group relative flex items-center justify-between hover:bg-indigo-50 transition-all duration-200 cursor-pointer focus-within:bg-indigo-50 focus-within:ring-2 focus-within:ring-indigo-500 focus-within:ring-inset" ';
                echo 'role="button" tabindex="0" ';
                echo 'aria-labelledby="' . esc_attr( $option_id . '_label' ) . '" ';
                echo 'aria-checked="' . ( $is_checked ? 'true' : 'false' ) . '" ';
                echo 'data-option-key="' . esc_attr( $key ) . '" ';
                echo ( $is_disabled ? 'aria-disabled="true"' : '' ) . '>';

                // 整个区域的点击标签
                echo '<label for="' . esc_attr( $option_id ) . '" class="absolute inset-0 cursor-pointer" aria-hidden="true"></label>';

                echo '<div class="flex items-center justify-between px-4 py-3 w-full">';

                // 标签内容 - 左侧
                echo '<div class="flex-1 min-w-0">';
                echo '<div id="' . esc_attr( $option_id . '_label' ) . '" class="text-sm font-medium text-gray-900 select-none group-hover:text-indigo-900 transition-colors duration-200">';
                echo esc_html( $option['label'] );
                echo '</div>';
                echo '</div>';

                // Checkbox容器 - 右侧
                echo '<div class="flex h-6 shrink-0 items-center ml-3">';
                echo '<input id="' . esc_attr( $option_id ) . '" type="checkbox" name="' . esc_attr( $input_name ) . '" value="' . esc_attr( $key ) . '" ';
                echo 'class="h-4 w-4 rounded border-gray-300 text-indigo-600 focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 xun-checkbox-input transition-colors duration-200 relative z-10"';
                echo checked( $is_checked, true, false ) . ( $is_disabled ? ' disabled' : '' ) . '>';
                echo '</div>';

                echo '</div>'; // 关闭flex容器
                echo '</div>'; // 关闭主容器
            }
        }

        /**
         * 渲染隐藏输入字段
         */
        private function render_hidden_inputs( $value ) {
            if ( is_array( $value ) && ! empty( $value ) ) {
                foreach ( $value as $val ) {
                    echo '<input type="hidden" name="' . esc_attr( $this->field_name( '[]' ) ) . '" value="' . esc_attr( $val ) . '" class="xun-checkbox-hidden-input">';
                }
            } else {
                echo '<input type="hidden" name="' . esc_attr( $this->field_name() ) . '" value="" class="xun-checkbox-empty-input">';
            }
        }

        /**
         * 获取字段设置
         *
         * 合并默认设置和用户自定义设置。
         *
         * @since 1.1.0
         *
         * @return array 完整的字段设置
         */
        private function get_field_settings() {

            $default_settings = array(
                'desc'            => ! empty( $this->field['desc'] ) ? $this->field['desc'] : '',
                'options'         => ! empty( $this->field['options'] ) ? $this->field['options'] : array(),
                'searchable'      => ! empty( $this->field['searchable'] ) ? $this->field['searchable'] : false,
                'select_all'      => ! empty( $this->field['select_all'] ) ? $this->field['select_all'] : false,
                'show_count'      => ! empty( $this->field['show_count'] ) ? $this->field['show_count'] : false,
                'required_min'    => ! empty( $this->field['required_min'] ) ? intval( $this->field['required_min'] ) : 0,
                'required_max'    => ! empty( $this->field['required_max'] ) ? intval( $this->field['required_max'] ) : 0,
                'inline'          => ! empty( $this->field['inline'] ) ? $this->field['inline'] : false,
                'columns'         => ! empty( $this->field['columns'] ) ? intval( $this->field['columns'] ) : 1,
                'group_by'        => ! empty( $this->field['group_by'] ) ? $this->field['group_by'] : '',
            );

            return wp_parse_args( $this->field, $default_settings );
        }

        /**
         * 加载字段资源
         *
         * 加载checkbox字段需要的JavaScript文件。
         *
         * @since 1.1.0
         */
        public function enqueue() {

            // 加载checkbox增强功能的JavaScript
            wp_enqueue_script(
                'xun-field-checkbox',
                XUN_Setup::$url . '/assets/js/fields/checkbox.js',
                array( 'jquery' ),
                XUN_Setup::$version,
                true
            );

            // 本地化脚本
            wp_localize_script( 'xun-field-checkbox', 'xunCheckboxL10n', array(
                'minRequired'     => '至少需要选择 %d 项',
                'maxExceeded'     => '最多只能选择 %d 项',
                'searchPlaceholder' => '搜索选项...',
                'selectAll'       => '全选',
                'deselectAll'     => '取消全选',
                'noResults'       => '没有找到匹配的选项',
            ) );
        }

        /**
         * 验证字段值
         */
        public function validate( $value ) {
            $value = is_array( $value ) ? $value : array();
            $value = array_filter( $value ); // 移除空值

            // 检查最小选择数量
            if ( ! empty( $this->field['required_min'] ) ) {
                $min = intval( $this->field['required_min'] );
                if ( count( $value ) < $min ) {
                    return new WP_Error( 'min_required', sprintf( '至少需要选择 %d 项', $min ) );
                }
            }

            // 检查最大选择数量
            if ( ! empty( $this->field['required_max'] ) ) {
                $max = intval( $this->field['required_max'] );
                if ( count( $value ) > $max ) {
                    return new WP_Error( 'max_exceeded', sprintf( '最多只能选择 %d 项', $max ) );
                }
            }

            return $value;
        }
    }
}