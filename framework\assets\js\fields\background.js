/**
 * Background Field JavaScript
 * 
 * 处理背景字段的交互功能，包括媒体上传器和颜色选择器同步
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

(function($) {
    'use strict';

    /**
     * Background Field 类
     */
    var XunBackgroundField = {

        /**
         * 初始化
         */
        init: function() {
            this.bindEvents();
        },

        /**
         * 初始化背景字段中的颜色选择器
         */
        initBackgroundColorPickers: function() {
            $('.xun-color-config').each(function() {
                try {
                    var config = JSON.parse($(this).text());
                    if (config && config.fieldId && window.XunColorPicker) {
                        window.XunColorPicker.createColorPicker(config);
                    }
                } catch (e) {
                    // 忽略配置解析错误
                }
            });
        },

        /**
         * 绑定事件
         */
        bindEvents: function() {
            var self = this;

            // 媒体上传器按钮点击事件（使用background专用的CSS类名）
            $(document).on('click', '.xun-background-media-button', function(e) {
                e.preventDefault();
                self.openMediaUploader($(this));
            });

            // 绑定颜色预览块点击事件（更通用的选择器）
            $(document).on('click', '.xun-color-field .w-12.h-12, .xun-color-field .w-16.h-16', function() {
                var $preview = $(this);
                var previewId = $preview.attr('id');

                if (window.XunColorPicker && previewId) {
                    // 从预览ID中提取字段ID
                    var fieldId = previewId.replace('-preview', '');
                    window.XunColorPicker.openColorPicker(fieldId);
                }
            });

            // 监听现代化颜色选择器变化
            $(document).on('input change', '.xun-color-input', function() {
                // 可以在这里添加颜色变化时的逻辑
            });

            // 颜色预设功能已移至color字段的调色板中

            // 背景图片变化时显示/隐藏属性选项
            $(document).on('change', 'input[name*="background-image"]', function() {
                self.toggleBackgroundAttributes($(this));
            });

            // 页面加载时检查背景图片状态和重新初始化颜色选择器
            $(document).ready(function() {
                $('input[name*="background-image"]').each(function() {
                    self.toggleBackgroundAttributes($(this));
                });

                // 重新初始化颜色选择器（处理动态加载的内容）
                setTimeout(function() {
                    if (window.XunColorPicker) {
                        self.initBackgroundColorPickers();
                    }
                }, 100);
            });

            // 监听DOM变化，重新初始化新添加的颜色选择器
            if (window.MutationObserver) {
                var observer = new MutationObserver(function(mutations) {
                    var shouldReinit = false;
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                            for (var i = 0; i < mutation.addedNodes.length; i++) {
                                var node = mutation.addedNodes[i];
                                if (node.nodeType === 1 && (
                                    $(node).hasClass('xun-wp-color-picker') ||
                                    $(node).find('.xun-wp-color-picker').length > 0
                                )) {
                                    shouldReinit = true;
                                    break;
                                }
                            }
                        }
                    });

                    if (shouldReinit) {
                        setTimeout(function() {
                            if (window.XunColorPicker) {
                                self.initBackgroundColorPickers();
                            }
                        }, 100);
                    }
                });

                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            }
        },

        /**
         * 打开WordPress媒体上传器
         */
        openMediaUploader: function($button) {
            // 检查WordPress媒体库是否可用
            if (typeof wp === 'undefined' || typeof wp.media === 'undefined') {
                alert('媒体库不可用，请刷新页面重试');
                return;
            }

            var $container = $button.closest('div');
            var $hiddenInput = $container.find('.xun-background-media-url');
            var $preview = $container.find('img');
            
            // 创建媒体上传器实例
            var mediaUploader = wp.media({
                title: '选择背景图片',
                button: {
                    text: '选择图片'
                },
                multiple: false,
                library: {
                    type: 'image'
                }
            });

            // 当选择图片时
            mediaUploader.on('select', function() {
                var attachment = mediaUploader.state().get('selection').first().toJSON();
                
                // 更新隐藏输入框的值
                if ($hiddenInput.length) {
                    $hiddenInput.val(attachment.url).trigger('change');
                }

                // 显示预览图片
                if ($preview.length === 0) {
                    $preview = $('<img class="h-16 w-16 object-cover rounded-md border border-gray-300 ml-3">');
                    $container.append($preview);
                }
                $preview.attr('src', attachment.url);

                // 更新按钮文字
                $button.html(`
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    更换图片
                `);

                // 添加移除按钮
                if ($container.find('.xun-remove-media').length === 0) {
                    var $removeBtn = $('<button type="button" class="xun-remove-media ml-2 inline-flex items-center px-3 py-2 border border-red-300 shadow-sm text-sm font-medium rounded-md text-red-700 bg-white hover:bg-red-50 focus:outline-none">移除</button>');
                    $container.append($removeBtn);
                }
            });

            // 打开媒体上传器
            mediaUploader.open();
        },

        /**
         * 移除背景图片
         */
        removeBackgroundImage: function($button) {
            var $container = $button.closest('div');
            var $hiddenInput = $container.find('.xun-media-url');
            var $preview = $container.find('img');
            var $mediaButton = $container.find('.xun-media-button');

            // 清空值
            $hiddenInput.val('').trigger('change');
            
            // 移除预览图片
            $preview.remove();
            
            // 移除移除按钮
            $button.remove();
            
            // 恢复按钮文字
            $mediaButton.html(`
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
                选择图片
            `);
        },

        /**
         * 初始化WordPress颜色选择器
         */
        initColorPickers: function() {
            var self = this;

            // 初始化所有WordPress颜色选择器
            $('.xun-wp-color-picker').each(function() {
                var $input = $(this);

                // 如果已经初始化过，跳过
                if ($input.hasClass('wp-color-picker')) {
                    return;
                }

                $input.wpColorPicker({
                    // 颜色变化时的回调
                    change: function(event, ui) {
                        // ui.color.toString() 获取颜色值
                        var color = ui.color.toString();
                        $input.val(color).trigger('input');
                    },
                    // 清除颜色时的回调
                    clear: function() {
                        $input.val('').trigger('input');
                    },
                    // 隐藏颜色选择器时的回调
                    hide: function(event, ui) {
                        // 可以在这里添加隐藏时的逻辑
                    }
                });
            });
        },

        /**
         * 切换背景属性显示/隐藏
         */
        toggleBackgroundAttributes: function($input) {
            var $container = $input.closest('.space-y-6');
            var $attributes = $container.find('.xun-bg-attributes');
            var hasImage = $input.val().trim() !== '';

            if (hasImage) {
                $attributes.show().removeClass('opacity-50');
            } else {
                $attributes.hide().addClass('opacity-50');
            }
        }
    };

    // 绑定移除按钮事件（使用事件委托）
    $(document).on('click', '.xun-remove-media', function(e) {
        e.preventDefault();
        XunBackgroundField.removeBackgroundImage($(this));
    });

    // 文档就绪时初始化
    $(document).ready(function() {
        XunBackgroundField.init();
    });

    // 导出到全局作用域（如果需要）
    window.XunBackgroundField = XunBackgroundField;

})(jQuery);
