/**
 * Xun Framework 选择字段 JavaScript
 * 
 * 提供现代化的选择字段交互功能，包括：
 * - 下拉列表展开/收起
 * - 键盘导航支持
 * - 搜索过滤功能
 * - 多选支持
 * - 无障碍访问优化
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

(function($) {
    'use strict';

    /**
     * 选择字段类
     * 
     * 管理单个选择字段的所有交互功能
     */
    class XunSelectField {
        
        /**
         * 构造函数
         * 
         * @param {jQuery} $container 字段容器元素
         */
        constructor($container) {
            this.$container = $container;
            this.$button = $container.find('.xun-select-button');
            this.$dropdown = $container.find('.xun-select-dropdown');
            this.$search = $container.find('.xun-select-search');
            this.$options = $container.find('.xun-select-options');
            this.$nativeSelect = $container.find('.xun-select-native');
            this.$display = $container.find('.xun-select-display');
            this.$arrow = $container.find('.xun-select-arrow');
            this.$clear = $container.find('.xun-select-clear');
            this.$noResults = $container.find('.xun-select-no-results');
            this.$loading = $container.find('.xun-select-loading');
            
            // 获取配置
            this.config = {
                fieldId: $container.data('field-id'),
                multiple: $container.data('multiple') === true,
                searchable: $container.data('searchable') === true,
                clearable: $container.data('clearable') === true,
                closeOnSelect: $container.data('close-on-select') === true
            };
            
            // 状态管理
            this.isOpen = false;
            this.highlightedIndex = -1;
            this.searchTerm = '';
            this.filteredOptions = [];
            this.mouseHighlight = false; // 标记是否由鼠标触发的高亮
            
            this.init();
        }
        
        /**
         * 初始化字段
         */
        init() {
            this.bindEvents();
            this.updateFilteredOptions();
            this.updateDisplay();
        }
        
        /**
         * 绑定事件
         */
        bindEvents() {
            // 按钮点击事件
            this.$button.on('click', (e) => {
                e.preventDefault();
                this.toggle();
            });
            
            // 清空按钮事件
            this.$clear.on('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.clearSelection();
            });
            
            // 选项点击事件
            this.$options.on('click', '.xun-select-option', (e) => {
                e.preventDefault();
                const $option = $(e.currentTarget);
                this.selectOption($option);
            });
            
            // 搜索输入事件
            this.$search.on('input', (e) => {
                this.handleSearch(e.target.value);
            });
            
            // 键盘事件
            this.$button.on('keydown', (e) => {
                this.handleKeydown(e);
            });
            
            this.$search.on('keydown', (e) => {
                this.handleKeydown(e);
            });
            
            // 全选按钮事件
            this.$container.find('.xun-select-all').on('click', (e) => {
                e.preventDefault();
                this.selectAll();
            });
            
            // 点击外部关闭
            $(document).on('click', (e) => {
                if (!this.$container.is(e.target) && this.$container.has(e.target).length === 0) {
                    this.close();
                }
            });
            
            // 选项悬停事件 - 使用CSS处理悬停效果，JavaScript只处理键盘导航高亮
            this.$options.on('mouseenter', '.xun-select-option', (e) => {
                const $option = $(e.currentTarget);
                this.mouseHighlight = true;

                // 设置键盘导航高亮索引，但不添加CSS类（由CSS :hover 处理）
                this.highlightOption($option.index(), false);
            });

            // 鼠标离开选项时清除键盘导航高亮
            this.$options.on('mouseleave', '.xun-select-option', (e) => {
                this.mouseHighlight = false;
                // CSS :hover 会自动处理悬停效果的移除
            });

            // 防止鼠标滚轮事件冒泡到父容器
            this.$options.on('wheel', (e) => {
                e.stopPropagation();
            });

            // 防止鼠标移动时的意外滚动
            this.$options.on('mousemove', (e) => {
                // 如果鼠标在滚动条区域，不处理高亮
                const container = this.$options[0];
                const rect = container.getBoundingClientRect();
                const scrollbarWidth = container.offsetWidth - container.clientWidth;

                if (scrollbarWidth > 0 && e.clientX > rect.right - scrollbarWidth) {
                    return; // 鼠标在滚动条上，不处理
                }
            });
        }
        
        /**
         * 切换下拉列表显示状态
         */
        toggle() {
            if (this.isOpen) {
                this.close();
            } else {
                this.open();
            }
        }
        
        /**
         * 打开下拉列表
         */
        open() {
            if (this.isOpen) return;

            this.isOpen = true;
            this.$dropdown.removeClass('hidden');
            this.$button.attr('aria-expanded', 'true');
            // 移除箭头旋转效果
            // this.$arrow.addClass('rotate-180');

            // 修复：不自动聚焦搜索框，保持按钮焦点
            // 用户可以手动点击搜索框或使用Tab键导航到搜索框
            // if (this.config.searchable) {
            //     setTimeout(() => {
            //         this.$search.focus();
            //     }, 100);
            // }

            // 重置搜索
            this.resetSearch();

            // 高亮当前选中项
            this.highlightSelectedOption();

            // 触发事件
            this.$container.trigger('xun:select:opened');
        }
        
        /**
         * 关闭下拉列表
         */
        close() {
            if (!this.isOpen) return;

            this.isOpen = false;
            this.$dropdown.addClass('hidden');
            this.$button.attr('aria-expanded', 'false');
            // 移除箭头旋转效果
            // this.$arrow.removeClass('rotate-180');
            this.highlightedIndex = -1;
            this.mouseHighlight = false; // 重置鼠标高亮状态

            // 重置搜索
            this.resetSearch();

            // 触发事件
            this.$container.trigger('xun:select:closed');
        }
        
        /**
         * 选择选项
         *
         * @param {jQuery} $option 选项元素
         */
        selectOption($option) {
            const value = $option.data('value');
            const text = $option.data('text');

            if (this.config.multiple) {
                this.toggleMultipleSelection(value, $option);
            } else {
                this.setSingleSelection(value, $option);
                if (this.config.closeOnSelect) {
                    this.close();
                }
            }

            this.updateNativeSelect();
            this.updateDisplay();
            this.updateClearButton();

            // 触发变化事件
            this.$container.trigger('xun:select:changed', [value, text]);
        }
        
        /**
         * 设置单选值
         *
         * @param {string} value   选项值
         * @param {jQuery} $option 选项元素
         */
        setSingleSelection(value, $option) {
            // 移除所有选中状态和悬停效果
            this.$options.find('.xun-select-option').removeClass('bg-blue-600 text-white bg-gray-100').addClass('text-gray-900');
            this.$options.find('.xun-select-option span:first-child').removeClass('font-semibold').addClass('font-normal');
            this.$options.find('.xun-select-option .absolute').addClass('hidden');

            // 设置当前选中状态，确保移除所有悬停效果
            $option.removeClass('text-gray-900 bg-gray-100').addClass('bg-blue-600 text-white');
            $option.find('span:first-child').removeClass('font-normal').addClass('font-semibold');
            $option.find('.absolute').removeClass('hidden');
            $option.attr('aria-selected', 'true');

            // 更新其他选项的aria-selected
            this.$options.find('.xun-select-option').not($option).attr('aria-selected', 'false');
        }
        
        /**
         * 切换多选值
         *
         * @param {string} value   选项值
         * @param {jQuery} $option 选项元素
         */
        toggleMultipleSelection(value, $option) {
            const isSelected = $option.hasClass('bg-blue-600');

            if (isSelected) {
                // 取消选中，移除选中状态，恢复为未选中状态
                $option.removeClass('bg-blue-600 text-white').addClass('text-gray-900');
                $option.find('span:first-child').removeClass('font-semibold').addClass('font-normal');
                $option.find('.absolute').addClass('hidden');
                $option.attr('aria-selected', 'false');
            } else {
                // 选中，添加选中状态（CSS会确保优先级）
                $option.removeClass('text-gray-900').addClass('bg-blue-600 text-white');
                $option.find('span:first-child').removeClass('font-normal').addClass('font-semibold');
                $option.find('.absolute').removeClass('hidden');
                $option.attr('aria-selected', 'true');
            }
        }
        
        /**
         * 清空选择
         */
        clearSelection() {
            // 移除所有选中状态和悬停效果，恢复为未选中状态
            this.$options.find('.xun-select-option').removeClass('bg-blue-600 text-white bg-gray-100').addClass('text-gray-900');
            this.$options.find('.xun-select-option span:first-child').removeClass('font-semibold').addClass('font-normal');
            this.$options.find('.xun-select-option .absolute').addClass('hidden');
            this.$options.find('.xun-select-option').attr('aria-selected', 'false');

            this.updateNativeSelect();
            this.updateDisplay();
            this.updateClearButton();

            // 触发变化事件
            this.$container.trigger('xun:select:cleared');
        }
        
        /**
         * 全选（多选模式）
         */
        selectAll() {
            if (!this.config.multiple) return;

            this.filteredOptions.forEach(($option) => {
                if (!$option.hasClass('bg-blue-600')) {
                    // 移除悬停效果，添加选中状态
                    $option.removeClass('text-gray-900 bg-gray-100').addClass('bg-blue-600 text-white');
                    $option.find('span:first-child').removeClass('font-normal').addClass('font-semibold');
                    $option.find('.absolute').removeClass('hidden');
                    $option.attr('aria-selected', 'true');
                }
            });

            this.updateNativeSelect();
            this.updateDisplay();
            this.updateClearButton();

            // 触发变化事件
            this.$container.trigger('xun:select:select-all');
        }

        /**
         * 处理搜索
         *
         * @param {string} term 搜索词
         */
        handleSearch(term) {
            this.searchTerm = term.toLowerCase();
            this.updateFilteredOptions();
            this.updateOptionsDisplay();
            this.highlightedIndex = -1;
        }

        /**
         * 重置搜索
         */
        resetSearch() {
            this.searchTerm = '';
            this.$search.val('');
            this.updateFilteredOptions();
            this.updateOptionsDisplay();
        }

        /**
         * 更新过滤后的选项
         */
        updateFilteredOptions() {
            this.filteredOptions = [];

            this.$options.find('.xun-select-option').each((index, element) => {
                const $option = $(element);
                const text = $option.data('text').toLowerCase();

                if (!this.searchTerm || text.includes(this.searchTerm)) {
                    this.filteredOptions.push($option);
                    $option.show();
                } else {
                    $option.hide();
                }
            });
        }

        /**
         * 更新选项显示
         */
        updateOptionsDisplay() {
            const hasVisibleOptions = this.filteredOptions.length > 0;

            if (hasVisibleOptions) {
                this.$noResults.addClass('hidden');
                this.$options.removeClass('hidden');
            } else {
                this.$noResults.removeClass('hidden');
                this.$options.addClass('hidden');
            }
        }

        /**
         * 处理键盘事件
         *
         * @param {Event} e 键盘事件
         */
        handleKeydown(e) {
            switch (e.key) {
                case 'ArrowDown':
                    e.preventDefault();
                    if (!this.isOpen) {
                        this.open();
                    } else {
                        this.highlightNext();
                    }
                    break;

                case 'ArrowUp':
                    e.preventDefault();
                    if (this.isOpen) {
                        this.highlightPrevious();
                    }
                    break;

                case 'Enter':
                case ' ':
                    e.preventDefault();
                    if (!this.isOpen) {
                        this.open();
                    } else if (this.highlightedIndex >= 0) {
                        this.selectHighlightedOption();
                    }
                    break;

                case 'Escape':
                    e.preventDefault();
                    this.close();
                    this.$button.focus();
                    break;

                case 'Tab':
                    if (this.isOpen) {
                        this.close();
                    }
                    break;
            }
        }

        /**
         * 高亮下一个选项
         */
        highlightNext() {
            if (this.filteredOptions.length === 0) return;

            this.mouseHighlight = false; // 键盘导航时清除鼠标高亮标记
            this.highlightedIndex = Math.min(this.highlightedIndex + 1, this.filteredOptions.length - 1);
            this.updateHighlight(true); // 键盘导航时需要滚动
        }

        /**
         * 高亮上一个选项
         */
        highlightPrevious() {
            if (this.filteredOptions.length === 0) return;

            this.mouseHighlight = false; // 键盘导航时清除鼠标高亮标记
            this.highlightedIndex = Math.max(this.highlightedIndex - 1, 0);
            this.updateHighlight(true); // 键盘导航时需要滚动
        }

        /**
         * 高亮指定选项
         *
         * @param {number} index 选项索引
         * @param {boolean} shouldScroll 是否需要滚动到可见区域
         */
        highlightOption(index, shouldScroll = true) {
            this.highlightedIndex = index;
            this.updateHighlight(shouldScroll);
        }

        /**
         * 更新高亮显示
         *
         * @param {boolean} shouldScroll 是否需要滚动到可见区域
         */
        updateHighlight(shouldScroll = true) {
            // 移除所有非选中项的高亮
            this.$options.find('.xun-select-option').each((index, element) => {
                const $option = $(element);
                if (!$option.hasClass('bg-blue-600')) {
                    $option.removeClass('bg-gray-100');
                }
            });

            // 添加当前高亮
            if (this.highlightedIndex >= 0 && this.filteredOptions[this.highlightedIndex]) {
                const $highlighted = this.filteredOptions[this.highlightedIndex];
                // 只有未选中的项才添加悬停高亮效果
                if (!$highlighted.hasClass('bg-blue-600')) {
                    $highlighted.addClass('bg-gray-100');
                }

                // 只在需要时滚动到可见区域（键盘导航时）
                if (shouldScroll) {
                    this.scrollToHighlighted($highlighted);
                }
            }
        }

        /**
         * 滚动到高亮选项
         *
         * @param {jQuery} $option 选项元素
         */
        scrollToHighlighted($option) {
            // 只有在键盘导航时才滚动
            if (this.mouseHighlight) {
                return;
            }

            const optionTop = $option.position().top;
            const optionHeight = $option.outerHeight();
            const containerHeight = this.$options.height();
            const scrollTop = this.$options.scrollTop();

            // 添加一些缓冲区，避免选项紧贴边缘
            const buffer = 5;

            if (optionTop < buffer) {
                this.$options.scrollTop(scrollTop + optionTop - buffer);
            } else if (optionTop + optionHeight > containerHeight - buffer) {
                this.$options.scrollTop(scrollTop + optionTop + optionHeight - containerHeight + buffer);
            }
        }

        /**
         * 选择高亮的选项
         */
        selectHighlightedOption() {
            if (this.highlightedIndex >= 0 && this.filteredOptions[this.highlightedIndex]) {
                this.selectOption(this.filteredOptions[this.highlightedIndex]);
            }
        }

        /**
         * 高亮当前选中项
         */
        highlightSelectedOption() {
            const $selected = this.$options.find('.xun-select-option[aria-selected="true"]').first();
            if ($selected.length) {
                const index = this.filteredOptions.indexOf($selected);
                if (index >= 0) {
                    this.highlightOption(index);
                }
            }
        }

        /**
         * 更新原生select
         */
        updateNativeSelect() {
            const selectedValues = [];

            this.$options.find('.xun-select-option[aria-selected="true"]').each((index, element) => {
                selectedValues.push($(element).data('value'));
            });

            // 更新原生select的选中状态
            this.$nativeSelect.find('option').prop('selected', false);
            selectedValues.forEach(value => {
                this.$nativeSelect.find(`option[value="${value}"]`).prop('selected', true);
            });

            // 触发change事件
            this.$nativeSelect.trigger('change');
        }

        /**
         * 更新显示文本
         */
        updateDisplay() {
            const selectedOptions = this.$options.find('.xun-select-option[aria-selected="true"]');
            const selectedCount = selectedOptions.length;

            if (selectedCount === 0) {
                // 获取占位符文本
                const placeholder = this.$container.find('.xun-select-display span.text-gray-500').first().text() || '请选择...';
                this.$display.html('<span class="text-gray-500">' + placeholder + '</span>');
            } else if (this.config.multiple) {
                if (selectedCount === 1) {
                    const text = selectedOptions.first().data('text');
                    this.$display.text(text);
                } else {
                    this.$display.text(`已选择 ${selectedCount} 项`);
                }
            } else {
                const text = selectedOptions.first().data('text');
                this.$display.text(text);
            }
        }

        /**
         * 更新清空按钮显示
         */
        updateClearButton() {
            const hasSelection = this.$options.find('.xun-select-option[aria-selected="true"]').length > 0;

            if (this.config.clearable && hasSelection) {
                this.$clear.removeClass('hidden');
            } else {
                this.$clear.addClass('hidden');
            }
        }



        /**
         * 销毁字段实例
         */
        destroy() {
            this.$container.off('.xun-select');
            $(document).off('click.xun-select-' + this.config.fieldId);
        }
    }

    /**
     * 初始化所有选择字段
     */
    function initSelectFields() {
        $('.xun-select-field').each(function() {
            const $this = $(this);
            if (!$this.data('xun-select-initialized')) {
                const instance = new XunSelectField($this);
                $this.data('xun-select-instance', instance);
                $this.data('xun-select-initialized', true);
            }
        });
    }

    // 文档就绪时初始化
    $(document).ready(initSelectFields);

    // 支持动态添加的字段
    $(document).on('xun:field:added', initSelectFields);

})(jQuery);
