/**
 * Border Field JavaScript
 * 
 * 处理边框字段的交互功能，包括实时预览和颜色选择器
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

(function($) {
    'use strict';

    /**
     * Border Field 类
     */
    var XunBorderField = {

        /**
         * 初始化
         */
        init: function() {
            this.bindEvents();
            this.updateAllPreviews();

            // 确保颜色选择器已初始化
            if (window.XunColorPicker) {
                window.XunColorPicker.init();
                // 初始化边框字段中的颜色选择器
                this.initBorderColorPickers();
            }
        },

        /**
         * 初始化边框字段中的颜色选择器
         */
        initBorderColorPickers: function() {
            $('.xun-border-field .xun-color-config').each(function() {
                try {
                    var config = JSON.parse($(this).text());
                    if (config && config.fieldId && window.XunColorPicker) {
                        window.XunColorPicker.createColorPicker(config);
                    }
                } catch (e) {
                    // 忽略配置解析错误
                }
            });
        },

        /**
         * 绑定事件
         */
        bindEvents: function() {
            var self = this;

            // 监听边框宽度输入变化
            $(document).on('input change', '.xun-border-field input[type="number"]', function() {
                self.updatePreview($(this).closest('.xun-border-field'));
            });

            // 监听边框样式选择变化
            $(document).on('change', '.xun-border-field select', function() {
                self.updatePreview($(this).closest('.xun-border-field'));
            });

            // 监听现代化颜色选择器变化
            $(document).on('input change', '.xun-border-field .xun-color-input', function() {
                self.updatePreview($(this).closest('.xun-border-field'));
            });

            // 绑定颜色预览块点击事件
            $(document).on('click', '.xun-border-field .xun-color-field .w-12.h-12, .xun-border-field .xun-color-field .w-16.h-16', function() {
                var $preview = $(this);
                var previewId = $preview.attr('id');

                if (window.XunColorPicker && previewId) {
                    // 从预览ID中提取字段ID
                    var fieldId = previewId.replace('-preview', '');
                    window.XunColorPicker.openColorPicker(fieldId);
                }
            });
        },



        /**
         * 更新所有边框字段的预览
         */
        updateAllPreviews: function() {
            var self = this;
            $('.xun-border-field').each(function() {
                self.updatePreview($(this));
            });
        },

        /**
         * 更新边框预览
         * 
         * @param {jQuery} $field 字段容器
         */
        updatePreview: function($field) {
            var $preview = $field.find('.xun-border-preview');
            if ($preview.length === 0) return;

            // 获取边框值
            var borderData = this.getBorderData($field);
            
            // 构建CSS样式
            var styles = this.buildBorderStyles(borderData);
            
            // 应用样式到预览元素
            $preview.attr('style', styles);
            
            // 更新预览文本
            var previewText = this.generatePreviewText(borderData);
            $preview.text(previewText);
        },

        /**
         * 获取边框数据
         * 
         * @param {jQuery} $field 字段容器
         * @return {Object} 边框数据对象
         */
        getBorderData: function($field) {
            var data = {
                top: '',
                right: '',
                bottom: '',
                left: '',
                all: '',
                style: 'solid',
                color: '',
                unit: 'px'
            };

            // 获取宽度值
            $field.find('input[type="number"]').each(function() {
                var $input = $(this);
                var name = $input.attr('name');
                var value = $input.val();
                
                if (name && value) {
                    if (name.includes('[top]')) {
                        data.top = value;
                    } else if (name.includes('[right]')) {
                        data.right = value;
                    } else if (name.includes('[bottom]')) {
                        data.bottom = value;
                    } else if (name.includes('[left]')) {
                        data.left = value;
                    } else if (name.includes('[all]')) {
                        data.all = value;
                    }
                }
            });

            // 获取样式
            var $styleSelect = $field.find('select');
            if ($styleSelect.length > 0) {
                data.style = $styleSelect.val() || 'solid';
            }

            // 获取颜色（现代化颜色选择器）
            var $colorInput = $field.find('.xun-color-input');
            if ($colorInput.length > 0) {
                data.color = $colorInput.val() || '';
            }

            return data;
        },

        /**
         * 构建边框CSS样式
         * 
         * @param {Object} data 边框数据
         * @return {String} CSS样式字符串
         */
        buildBorderStyles: function(data) {
            var styles = [];
            var unit = data.unit || 'px';
            var color = data.color || '#d1d5db';
            var style = data.style || 'solid';

            if (data.all && data.all !== '') {
                // 统一设置模式
                styles.push('border: ' + data.all + unit + ' ' + style + ' ' + color);
            } else {
                // 分别设置模式
                if (data.top && data.top !== '') {
                    styles.push('border-top: ' + data.top + unit + ' ' + style + ' ' + color);
                }
                if (data.right && data.right !== '') {
                    styles.push('border-right: ' + data.right + unit + ' ' + style + ' ' + color);
                }
                if (data.bottom && data.bottom !== '') {
                    styles.push('border-bottom: ' + data.bottom + unit + ' ' + style + ' ' + color);
                }
                if (data.left && data.left !== '') {
                    styles.push('border-left: ' + data.left + unit + ' ' + style + ' ' + color);
                }
            }

            return styles.join('; ');
        },

        /**
         * 生成预览文本
         * 
         * @param {Object} data 边框数据
         * @return {String} 预览文本
         */
        generatePreviewText: function(data) {
            var parts = [];
            var unit = data.unit || 'px';

            // 样式名称中文映射
            var styleNames = {
                'solid': '实线',
                'dashed': '虚线',
                'dotted': '点线',
                'double': '双线',
                'groove': '凹槽',
                'ridge': '凸起',
                'inset': '内嵌',
                'outset': '外凸',
                'none': '无边框'
            };

            if (data.all && data.all !== '') {
                var styleName = styleNames[data.style] || data.style || '实线';
                parts.push(data.all + unit + ' ' + styleName);
            } else {
                var widths = [];
                if (data.top && data.top !== '') widths.push('上:' + data.top);
                if (data.right && data.right !== '') widths.push('右:' + data.right);
                if (data.bottom && data.bottom !== '') widths.push('下:' + data.bottom);
                if (data.left && data.left !== '') widths.push('左:' + data.left);

                if (widths.length > 0) {
                    parts.push(widths.join(' ') + unit);
                    if (data.style && data.style !== 'solid') {
                        var styleName = styleNames[data.style] || data.style;
                        parts.push(styleName);
                    }
                }
            }

            if (data.color) {
                parts.push(data.color);
            }

            return parts.length > 0 ? parts.join(' | ') : '边框预览效果';
        },

        /**
         * 重新初始化字段（用于动态添加的字段）
         */
        reinit: function() {
            this.initColorPickers();
            this.updateAllPreviews();
        }
    };

    /**
     * 文档就绪时初始化
     */
    $(document).ready(function() {
        XunBorderField.init();
    });

    /**
     * 暴露到全局，供其他脚本调用
     */
    window.XunBorderField = XunBorderField;

})(jQuery);
