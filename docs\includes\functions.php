<?php
/**
 * Xun Framework 文档系统 - 工具函数
 *
 * 这个文件包含了文档系统使用的各种工具函数，
 * 包括URL处理、模板加载、安全处理等。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

/**
 * 获取当前页面URL
 *
 * @return string
 */
function xun_docs_get_current_url(): string {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $uri = $_SERVER['REQUEST_URI'];
    
    return $protocol . '://' . $host . $uri;
}

/**
 * 生成文档页面URL
 *
 * @param string $page 页面路径
 * @param array $params 查询参数
 * @return string
 */
function xun_docs_get_page_url(string $page = '', array $params = []): string {
    $url = XUN_DOCS_URL;
    
    if (!empty($page)) {
        $url .= '/' . ltrim($page, '/');
    }
    
    if (!empty($params)) {
        $url .= '?' . http_build_query($params);
    }
    
    return $url;
}

/**
 * 获取资源文件URL
 *
 * @param string $path 资源路径
 * @param string $version 版本号
 * @return string
 */
function xun_docs_get_asset_url(string $path, string $version = ''): string {
    $url = XUN_DOCS_ASSETS_URL . '/' . ltrim($path, '/');
    
    if (empty($version)) {
        $version = XUN_DOCS_VERSION;
    }
    
    return $url . '?v=' . $version;
}

/**
 * 安全输出HTML内容
 *
 * @param string $content 内容
 * @return string
 */
function xun_docs_esc_html(string $content): string {
    return htmlspecialchars($content, ENT_QUOTES, 'UTF-8');
}

/**
 * 安全输出属性值
 *
 * @param string $attr 属性值
 * @return string
 */
function xun_docs_esc_attr(string $attr): string {
    return htmlspecialchars($attr, ENT_QUOTES, 'UTF-8');
}

/**
 * 安全输出URL
 *
 * @param string $url URL
 * @return string
 */
function xun_docs_esc_url(string $url): string {
    return filter_var($url, FILTER_SANITIZE_URL);
}

/**
 * 加载模板文件
 *
 * @param string $template 模板名称
 * @param array $vars 传递给模板的变量
 * @return void
 */
function xun_docs_load_template(string $template, array $vars = []): void {
    // 提取变量到当前作用域
    extract($vars);
    
    $template_file = XUN_DOCS_PATH . '/pages/' . $template . '.php';
    
    if (file_exists($template_file)) {
        include $template_file;
    } else {
        xun_docs_load_404();
    }
}

/**
 * 加载404页面
 *
 * @return void
 */
function xun_docs_load_404(): void {
    http_response_code(404);
    include XUN_DOCS_PATH . '/pages/404.php';
}

/**
 * 获取当前页面标识
 *
 * @return string
 */
function xun_docs_get_current_page(): string {
    $request_uri = $_SERVER['REQUEST_URI'];
    $script_name = dirname($_SERVER['SCRIPT_NAME']);
    
    // 移除脚本目录路径
    $page = str_replace($script_name, '', $request_uri);
    $page = trim($page, '/');
    
    // 移除查询参数
    if (($pos = strpos($page, '?')) !== false) {
        $page = substr($page, 0, $pos);
    }
    
    return $page ?: 'home';
}

/**
 * 检查当前页面是否激活
 *
 * @param string $page 页面路径
 * @return bool
 */
function xun_docs_is_current_page(string $page): bool {
    $current = xun_docs_get_current_page();
    return $current === $page || str_starts_with($current, $page . '/');
}

/**
 * 生成面包屑导航
 *
 * @param string $page 当前页面
 * @return array
 */
function xun_docs_get_breadcrumbs(string $page): array {
    $breadcrumbs = [
        ['title' => '首页', 'url' => '']
    ];
    
    if (empty($page) || $page === 'home') {
        return $breadcrumbs;
    }
    
    $parts = explode('/', $page);
    $path = '';
    
    foreach ($parts as $part) {
        $path .= ($path ? '/' : '') . $part;
        $title = ucfirst(str_replace('-', ' ', $part));
        
        $breadcrumbs[] = [
            'title' => $title,
            'url' => $path
        ];
    }
    
    return $breadcrumbs;
}

/**
 * 格式化文件大小
 *
 * @param int $size 文件大小（字节）
 * @return string
 */
function xun_docs_format_file_size(int $size): string {
    $units = ['B', 'KB', 'MB', 'GB'];
    $unit = 0;
    
    while ($size >= 1024 && $unit < count($units) - 1) {
        $size /= 1024;
        $unit++;
    }
    
    return round($size, 2) . ' ' . $units[$unit];
}

/**
 * 格式化时间
 *
 * @param string|int $time 时间戳或时间字符串
 * @return string
 */
function xun_docs_format_time($time): string {
    if (is_string($time)) {
        $time = strtotime($time);
    }
    
    return date('Y-m-d H:i:s', $time);
}

/**
 * 生成随机字符串
 *
 * @param int $length 长度
 * @return string
 */
function xun_docs_generate_random_string(int $length = 10): string {
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $string = '';
    
    for ($i = 0; $i < $length; $i++) {
        $string .= $characters[random_int(0, strlen($characters) - 1)];
    }
    
    return $string;
}

/**
 * 检查是否为AJAX请求
 *
 * @return bool
 */
function xun_docs_is_ajax(): bool {
    return isset($_SERVER['HTTP_X_REQUESTED_WITH']) && 
           strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) === 'xmlhttprequest';
}

/**
 * 发送JSON响应
 *
 * @param array $data 响应数据
 * @param int $status HTTP状态码
 * @return void
 */
function xun_docs_send_json(array $data, int $status = 200): void {
    http_response_code($status);
    header('Content-Type: application/json');
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

/**
 * 获取Heroicon SVG图标
 *
 * @param string $name 图标名称
 * @param string $type 图标类型 (outline|solid)
 * @param string $class CSS类名
 * @return string
 */
function xun_docs_get_icon(string $name, string $type = 'outline', string $class = 'w-5 h-5'): string {
    $icons = [
        'outline' => [
            'home' => '<svg class="' . $class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path></svg>',
            'book-open' => '<svg class="' . $class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path></svg>',
            'rocket-launch' => '<svg class="' . $class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.59 14.37a6 6 0 01-5.84 7.38v-4.8m5.84-2.58a14.98 14.98 0 006.16-12.12A14.98 14.98 0 009.631 8.41m5.96 5.96a14.926 14.926 0 01-5.841 2.58m-.119-8.54a6 6 0 00-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 00-2.58 5.84m2.58-5.84a14.98 14.98 0 012.58 5.84M15.59 14.37L9.631 8.41m5.96 5.96a14.926 14.926 0 01-5.841 2.58m-.119-8.54a6 6 0 00-7.381 5.84h4.8m2.581-5.84a14.927 14.927 0 00-2.58 5.84m2.58-5.84a14.98 14.98 0 012.58 5.84"></path></svg>',
            'squares-2x2' => '<svg class="' . $class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2 2z"></path></svg>',
            'cog-6-tooth' => '<svg class="' . $class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>',
            'search' => '<svg class="' . $class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>',
            'moon' => '<svg class="' . $class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path></svg>',
            'sun' => '<svg class="' . $class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"></path></svg>',
            'bars-3' => '<svg class="' . $class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path></svg>',
            'x-mark' => '<svg class="' . $class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>',
            'chevron-right' => '<svg class="' . $class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>',
            'chevron-down' => '<svg class="' . $class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>',
            'clipboard' => '<svg class="' . $class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path></svg>',
            'check' => '<svg class="' . $class . '" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>'
        ]
    ];
    
    return $icons[$type][$name] ?? '';
}

/**
 * 读取文件内容
 *
 * @param string $file 文件路径
 * @return string|false
 */
function xun_docs_read_file(string $file) {
    if (!file_exists($file)) {
        return false;
    }
    
    return file_get_contents($file);
}

/**
 * 写入文件内容
 *
 * @param string $file 文件路径
 * @param string $content 内容
 * @return bool
 */
function xun_docs_write_file(string $file, string $content): bool {
    $dir = dirname($file);
    
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
    }
    
    return file_put_contents($file, $content) !== false;
}

/**
 * 删除目录及其内容
 *
 * @param string $dir 目录路径
 * @return bool
 */
function xun_docs_remove_directory(string $dir): bool {
    if (!is_dir($dir)) {
        return false;
    }
    
    $files = array_diff(scandir($dir), ['.', '..']);
    
    foreach ($files as $file) {
        $path = $dir . '/' . $file;
        
        if (is_dir($path)) {
            xun_docs_remove_directory($path);
        } else {
            unlink($path);
        }
    }
    
    return rmdir($dir);
}
