<?php
/**
 * Xun Framework 文档系统 - 字段类型页面
 *
 * 展示框架支持的所有23种字段类型的详细文档和使用示例。
 * 采用分类展示、搜索过滤和实时预览的交互式设计。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

// 页面配置
$header_config = [
    'site_title' => 'Xun Framework',
    'site_description' => '现代化的WordPress选项框架',
    'page_title' => '字段类型',
    'page_description' => '探索Xun Framework提供的23种强大字段类型，从基础输入到高级交互组件',
    'keywords' => 'WordPress, Framework, 字段类型, 表单字段, UI组件, 输入控件',
    'current_page' => 'field-types'
];

$footer_config = [
    'site_title' => 'Xun Framework',
    'site_description' => '现代化的WordPress选项框架',
    'version' => '1.0.0',
    'total_fields' => 23,
    'show_stats' => true
];

// 字段类型分类配置
$field_categories = [
    'basic' => [
        'title' => '基础字段',
        'description' => '常用的基础输入字段类型',
        'icon' => 'pencil-square',
        'color' => 'blue',
        'fields' => [
            'text' => [
                'title' => '文本字段',
                'description' => '单行文本输入，支持多种输入类型',
                'icon' => 'pencil',
                'features' => ['邮箱验证', 'URL验证', '长度限制', '实时验证'],
                'use_cases' => ['用户名', '邮箱地址', '网站URL', '电话号码']
            ],
            'textarea' => [
                'title' => '文本域',
                'description' => '多行文本输入，支持自动调整高度',
                'icon' => 'document-text',
                'features' => ['自动调整高度', '字符计数', '拖拽调整', '语法高亮'],
                'use_cases' => ['文章内容', '描述信息', '代码片段', '备注说明']
            ],
            'number' => [
                'title' => '数字字段',
                'description' => '数字输入控件，支持范围限制和步长',
                'icon' => 'hashtag',
                'features' => ['最小/最大值', '步长控制', '单位显示', '格式化'],
                'use_cases' => ['价格设置', '数量输入', '百分比', '尺寸设置']
            ],
            'date' => [
                'title' => '日期字段',
                'description' => '日期选择器，支持多种日期格式',
                'icon' => 'calendar',
                'features' => ['日期选择器', '格式化显示', '范围限制', '本地化'],
                'use_cases' => ['发布日期', '截止时间', '生日设置', '事件日期']
            ],
            'time' => [
                'title' => '时间字段',
                'description' => '时间选择器，支持24小时制',
                'icon' => 'clock',
                'features' => ['时间选择器', '24小时制', '分钟间隔', '格式化'],
                'use_cases' => ['营业时间', '提醒时间', '定时发布', '时间设置']
            ]
        ]
    ],
    'selection' => [
        'title' => '选择字段',
        'description' => '各种选择和切换类型的字段',
        'icon' => 'check-circle',
        'color' => 'green',
        'fields' => [
            'select' => [
                'title' => '下拉选择',
                'description' => '下拉选择框，支持单选和多选',
                'icon' => 'chevron-down',
                'features' => ['单选/多选', '搜索功能', '分组选项', '异步加载'],
                'use_cases' => ['分类选择', '标签选择', '用户角色', '状态设置']
            ],
            'radio' => [
                'title' => '单选按钮',
                'description' => '单选按钮组，支持图片和图标',
                'icon' => 'radio',
                'features' => ['图片选项', '图标支持', '内联布局', '自定义样式'],
                'use_cases' => ['主题选择', '布局模式', '支付方式', '性别选择']
            ],
            'checkbox' => [
                'title' => '复选框',
                'description' => '复选框组，支持多项选择',
                'icon' => 'check-square',
                'features' => ['多项选择', '全选功能', '分组显示', '计数显示'],
                'use_cases' => ['功能开关', '权限设置', '兴趣选择', '条款同意']
            ],
            'switch' => [
                'title' => '开关',
                'description' => '现代化开关控件，用于布尔值设置',
                'icon' => 'switch-horizontal',
                'features' => ['动画效果', '自定义标签', '状态回调', '禁用状态'],
                'use_cases' => ['功能开关', '显示隐藏', '启用禁用', '模式切换']
            ]
        ]
    ],
    'media' => [
        'title' => '媒体字段',
        'description' => '图片、文件和媒体相关的字段',
        'icon' => 'photo',
        'color' => 'purple',
        'fields' => [
            'media' => [
                'title' => '媒体选择',
                'description' => '媒体库选择器，支持图片、视频、音频',
                'icon' => 'photograph',
                'features' => ['媒体库集成', '文件类型限制', '预览功能', '批量选择'],
                'use_cases' => ['头像上传', '背景图片', '产品图片', '文档附件']
            ],
            'gallery' => [
                'title' => '图片画廊',
                'description' => '多图片选择和管理，支持拖拽排序',
                'icon' => 'collection',
                'features' => ['多图选择', '拖拽排序', '批量操作', '预览模式'],
                'use_cases' => ['产品画廊', '作品展示', '轮播图片', '相册管理']
            ],
            'icon' => [
                'title' => '图标选择',
                'description' => '图标选择器，支持多种图标库',
                'icon' => 'star',
                'features' => ['图标库集成', '搜索功能', '分类浏览', '自定义图标'],
                'use_cases' => ['菜单图标', '功能图标', '社交图标', '装饰图标']
            ]
        ]
    ],
    'design' => [
        'title' => '设计字段',
        'description' => '颜色、样式和设计相关的字段',
        'icon' => 'color-swatch',
        'color' => 'pink',
        'fields' => [
            'color' => [
                'title' => '颜色选择',
                'description' => '颜色选择器，支持多种颜色格式',
                'icon' => 'color-swatch',
                'features' => ['颜色选择器', '透明度支持', '预设颜色', '历史记录'],
                'use_cases' => ['主题颜色', '文字颜色', '背景颜色', '边框颜色']
            ],
            'background' => [
                'title' => '背景设置',
                'description' => '完整的背景设置，包括颜色、图片、渐变',
                'icon' => 'photograph',
                'features' => ['背景图片', '背景颜色', '渐变背景', '重复模式'],
                'use_cases' => ['页面背景', '区块背景', '按钮背景', '卡片背景']
            ],
            'border' => [
                'title' => '边框设置',
                'description' => '边框样式设置，包括宽度、样式、颜色',
                'icon' => 'view-grid',
                'features' => ['边框样式', '圆角设置', '阴影效果', '分边设置'],
                'use_cases' => ['容器边框', '按钮边框', '图片边框', '表格边框']
            ],
            'palette' => [
                'title' => '调色板',
                'description' => '颜色组合管理，支持预设和自定义',
                'icon' => 'color-swatch',
                'features' => ['颜色组合', '预设方案', '自定义调色板', '导入导出'],
                'use_cases' => ['主题配色', '品牌色彩', '设计系统', '配色方案']
            ]
        ]
    ],
    'interactive' => [
        'title' => '交互字段',
        'description' => '滑块、按钮等交互式字段',
        'icon' => 'cursor-click',
        'color' => 'indigo',
        'fields' => [
            'slider' => [
                'title' => '滑块',
                'description' => '数值滑块控件，支持范围和单值',
                'icon' => 'adjustments',
                'features' => ['范围滑块', '刻度显示', '实时预览', '键盘控制'],
                'use_cases' => ['音量控制', '价格范围', '透明度', '尺寸调节']
            ],
            'button' => [
                'title' => '操作按钮',
                'description' => '操作按钮，支持AJAX回调和自定义动作',
                'icon' => 'cursor-click',
                'features' => ['AJAX回调', '加载状态', '确认对话框', '批量操作'],
                'use_cases' => ['数据导入', '缓存清理', '重置设置', '发送邮件']
            ],
            'accordion' => [
                'title' => '手风琴',
                'description' => '折叠面板，用于组织复杂内容',
                'icon' => 'menu-alt-2',
                'features' => ['折叠展开', '嵌套内容', '动画效果', '状态记忆'],
                'use_cases' => ['设置分组', '帮助文档', 'FAQ展示', '内容组织']
            ]
        ]
    ],
    'advanced' => [
        'title' => '高级字段',
        'description' => '复杂的高级功能字段',
        'icon' => 'cog',
        'color' => 'gray',
        'fields' => [
            'code' => [
                'title' => '代码编辑',
                'description' => '代码编辑器，支持语法高亮和自动完成',
                'icon' => 'code',
                'features' => ['语法高亮', '自动完成', '代码折叠', '全屏编辑'],
                'use_cases' => ['自定义CSS', 'JavaScript代码', 'HTML模板', '配置文件']
            ],
            'repeater' => [
                'title' => '重复器',
                'description' => '动态重复字段组，支持添加删除和排序',
                'icon' => 'duplicate',
                'features' => ['动态添加', '拖拽排序', '嵌套字段', '模板支持'],
                'use_cases' => ['团队成员', '产品特性', '联系方式', '社交链接']
            ],
            'sortable' => [
                'title' => '排序列表',
                'description' => '可排序的列表项，支持拖拽操作',
                'icon' => 'sort-ascending',
                'features' => ['拖拽排序', '启用禁用', '批量操作', '状态保存'],
                'use_cases' => ['菜单排序', '功能排序', '优先级设置', '显示顺序']
            ],
            'sorter' => [
                'title' => '分拣器',
                'description' => '双列表分拣器，用于启用禁用项目',
                'icon' => 'switch-vertical',
                'features' => ['双列表', '拖拽分拣', '搜索过滤', '批量操作'],
                'use_cases' => ['功能管理', '权限分配', '模块启用', '内容筛选']
            ]
        ]
    ]
];

// 引入通用头部
include 'includes/header.php';
?>



        <!-- 主要内容区域 -->
        <main class="flex-1">
            <!-- Hero区域 -->
            <section class="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-indigo-50 py-20">
                <!-- 背景装饰 -->
                <div class="absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
                <div class="absolute bottom-0 left-0 -mb-20 -ml-20 w-80 h-80 bg-gradient-to-tr from-green-400/20 to-blue-400/20 rounded-full blur-3xl"></div>

                <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center">
                        <h1 class="text-3xl lg:text-5xl font-bold text-gray-900 mb-6">
                            <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                字段类型
                            </span>
                        </h1>
                        
                        <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                            探索Xun Framework提供的<strong>23种强大字段类型</strong>，
                            从基础输入控件到高级交互组件，满足所有开发需求。
                        </p>

                        <!-- 统计信息 -->
                        <div class="flex flex-wrap justify-center gap-6 mb-12">
                            <div class="bg-white rounded-xl px-6 py-4 shadow-sm border border-gray-200">
                                <div class="text-2xl font-bold text-blue-600">23</div>
                                <div class="text-sm text-gray-600">字段类型</div>
                            </div>
                            <div class="bg-white rounded-xl px-6 py-4 shadow-sm border border-gray-200">
                                <div class="text-2xl font-bold text-green-600">6</div>
                                <div class="text-sm text-gray-600">功能分类</div>
                            </div>
                            <div class="bg-white rounded-xl px-6 py-4 shadow-sm border border-gray-200">
                                <div class="text-2xl font-bold text-purple-600">100%</div>
                                <div class="text-sm text-gray-600">响应式</div>
                            </div>
                        </div>

                        <!-- 搜索框 -->
                        <div class="max-w-md mx-auto">
                            <div class="relative">
                                <input 
                                    type="text" 
                                    id="field-search"
                                    placeholder="搜索字段类型..."
                                    class="w-full px-4 py-3 pl-12 pr-4 text-gray-900 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300"
                                >
                                <div class="absolute inset-y-0 left-0 flex items-center pl-4">
                                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 字段分类导航 -->
            <section class="py-12 bg-white border-b border-gray-200">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">字段分类</h2>
                        <p class="text-gray-600 max-w-2xl mx-auto">
                            按功能分类浏览所有字段类型，快速找到适合您需求的组件
                        </p>
                    </div>

                    <!-- 分类标签 -->
                    <div class="flex flex-wrap justify-center gap-4 mb-8">
                        <button
                            class="category-tab active px-6 py-3 rounded-xl font-medium transition-all duration-300 bg-blue-100 text-blue-700 border-2 border-blue-200"
                            data-category="all"
                        >
                            全部字段
                        </button>
                        <?php foreach ($field_categories as $key => $category): ?>
                        <button
                            class="category-tab px-6 py-3 rounded-xl font-medium transition-all duration-300 bg-gray-50 text-gray-600 border-2 border-transparent hover:bg-gray-100"
                            data-category="<?php echo $key; ?>"
                        >
                            <span class="inline-flex items-center">
                                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <?php echo htmlspecialchars($category['title']); ?>
                            </span>
                        </button>
                        <?php endforeach; ?>
                    </div>

                    <!-- 分类卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                        <?php foreach ($field_categories as $key => $category): ?>
                        <div class="category-card bg-white rounded-2xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1" data-category="<?php echo $key; ?>">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-<?php echo $category['color']; ?>-100 rounded-xl flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-<?php echo $category['color']; ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        <?php echo htmlspecialchars($category['title']); ?>
                                    </h3>
                                    <p class="text-sm text-gray-500">
                                        <?php echo count($category['fields']); ?> 个字段
                                    </p>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4">
                                <?php echo htmlspecialchars($category['description']); ?>
                            </p>
                            <div class="flex flex-wrap gap-2">
                                <?php foreach ($category['fields'] as $field_key => $field): ?>
                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-<?php echo $category['color']; ?>-50 text-<?php echo $category['color']; ?>-700">
                                    <?php echo htmlspecialchars($field['title']); ?>
                                </span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </section>

            <!-- 字段详情展示区域 -->
            <section class="py-20 bg-gray-50">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-16">
                        <h2 class="text-3xl font-bold text-gray-900 mb-4">字段详情</h2>
                        <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                            每个字段类型的详细说明、配置参数和使用示例
                        </p>
                    </div>

                    <!-- 字段列表 -->
                    <div id="fields-container" class="space-y-12">
                        <?php foreach ($field_categories as $category_key => $category): ?>
                        <div class="field-category" data-category="<?php echo $category_key; ?>">
                            <!-- 分类标题 -->
                            <div class="flex items-center mb-8">
                                <div class="w-10 h-10 bg-<?php echo $category['color']; ?>-100 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-5 h-5 text-<?php echo $category['color']; ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-2xl font-bold text-gray-900">
                                        <?php echo htmlspecialchars($category['title']); ?>
                                    </h3>
                                    <p class="text-gray-600">
                                        <?php echo htmlspecialchars($category['description']); ?>
                                    </p>
                                </div>
                            </div>

                            <!-- 字段卡片 -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <?php foreach ($category['fields'] as $field_key => $field): ?>
                                <div class="field-card bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300" data-field="<?php echo $field_key; ?>">
                                    <!-- 字段头部 -->
                                    <div class="p-6 border-b border-gray-100">
                                        <div class="flex items-start justify-between">
                                            <div class="flex items-center">
                                                <div class="w-12 h-12 bg-<?php echo $category['color']; ?>-100 rounded-xl flex items-center justify-center mr-4">
                                                    <svg class="w-6 h-6 text-<?php echo $category['color']; ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                </div>
                                                <div>
                                                    <h4 class="text-xl font-semibold text-gray-900 mb-1">
                                                        <?php echo htmlspecialchars($field['title']); ?>
                                                    </h4>
                                                    <p class="text-gray-600">
                                                        <?php echo htmlspecialchars($field['description']); ?>
                                                    </p>
                                                </div>
                                            </div>
                                            <button
                                                class="field-expand-btn text-gray-400 hover:text-gray-600 transition-colors"
                                                aria-expanded="false"
                                                aria-controls="field-content-<?php echo $category_key; ?>-<?php echo $field_key; ?>"
                                                aria-label="展开 <?php echo htmlspecialchars($field['title']); ?> 字段详情"
                                            >
                                                <svg class="w-5 h-5 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                </svg>
                                            </button>
                                        </div>
                                    </div>

                                    <!-- 字段内容 -->
                                    <div
                                        class="field-content hidden"
                                        id="field-content-<?php echo $category_key; ?>-<?php echo $field_key; ?>"
                                        aria-hidden="true"
                                    >
                                        <!-- 特性列表 -->
                                        <div class="p-6 border-b border-gray-100">
                                            <h5 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-3">主要特性</h5>
                                            <div class="grid grid-cols-2 gap-2">
                                                <?php foreach ($field['features'] as $feature): ?>
                                                <div class="flex items-center text-sm text-gray-600">
                                                    <svg class="w-4 h-4 text-green-500 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                    </svg>
                                                    <?php echo htmlspecialchars($feature); ?>
                                                </div>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>

                                        <!-- 使用场景 -->
                                        <div class="p-6 border-b border-gray-100">
                                            <h5 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-3">使用场景</h5>
                                            <div class="flex flex-wrap gap-2">
                                                <?php foreach ($field['use_cases'] as $use_case): ?>
                                                <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-50 text-blue-700">
                                                    <?php echo htmlspecialchars($use_case); ?>
                                                </span>
                                                <?php endforeach; ?>
                                            </div>
                                        </div>

                                        <!-- 代码示例 -->
                                        <div class="p-6">
                                            <h5 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-3">配置示例</h5>
                                            <div class="bg-gray-900 rounded-xl p-4 overflow-hidden">
                                                <div class="flex items-center justify-between mb-3">
                                                    <span class="text-gray-400 text-sm">PHP</span>
                                                    <button class="copy-button text-gray-400 hover:text-white text-sm" data-copy-target="code-<?php echo $category_key; ?>-<?php echo $field_key; ?>">
                                                        复制
                                                    </button>
                                                </div>
                                                <pre id="code-<?php echo $category_key; ?>-<?php echo $field_key; ?>" class="text-sm overflow-x-auto"><code class="language-php"><?php echo htmlspecialchars("array(
    'id'    => '{$field_key}_example',
    'type'  => '{$field_key}',
    'title' => '{$field['title']}示例',
    'desc'  => '这是一个{$field['title']}字段的配置示例',
    // 更多配置参数...
)"); ?></code></pre>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </section>
        </main>

    <!-- JavaScript交互功能 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 搜索功能
            const searchInput = document.getElementById('field-search');
            const fieldCards = document.querySelectorAll('.field-card');
            const categoryCards = document.querySelectorAll('.field-category');

            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                fieldCards.forEach(card => {
                    const fieldName = card.dataset.field;
                    const fieldTitle = card.querySelector('h4').textContent.toLowerCase();
                    const fieldDesc = card.querySelector('p').textContent.toLowerCase();

                    if (fieldTitle.includes(searchTerm) || fieldDesc.includes(searchTerm) || fieldName.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });

                // 隐藏空的分类
                categoryCards.forEach(category => {
                    const visibleCards = category.querySelectorAll('.field-card[style="display: block"], .field-card:not([style*="display: none"])');
                    if (visibleCards.length === 0 && searchTerm !== '') {
                        category.style.display = 'none';
                    } else {
                        category.style.display = 'block';
                    }
                });
            });

            // 分类过滤功能
            const categoryTabs = document.querySelectorAll('.category-tab');

            categoryTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const category = this.dataset.category;

                    // 更新标签状态
                    categoryTabs.forEach(t => {
                        t.classList.remove('active', 'bg-blue-100', 'text-blue-700', 'border-blue-200');
                        t.classList.add('bg-gray-50', 'text-gray-600', 'border-transparent');
                    });

                    this.classList.add('active', 'bg-blue-100', 'text-blue-700', 'border-blue-200');
                    this.classList.remove('bg-gray-50', 'text-gray-600', 'border-transparent');

                    // 过滤字段
                    if (category === 'all') {
                        categoryCards.forEach(card => {
                            card.style.display = 'block';
                        });
                    } else {
                        categoryCards.forEach(card => {
                            if (card.dataset.category === category) {
                                card.style.display = 'block';
                            } else {
                                card.style.display = 'none';
                            }
                        });
                    }

                    // 清空搜索
                    searchInput.value = '';
                    fieldCards.forEach(card => {
                        card.style.display = 'block';
                    });
                });
            });

            // 字段展开/收起功能 - 无障碍版本
            const expandButtons = document.querySelectorAll('.field-expand-btn');

            expandButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const contentId = this.getAttribute('aria-controls');
                    const content = document.getElementById(contentId);

                    if (!content) {
                        console.error('Content element not found:', contentId);
                        return;
                    }

                    const icon = this.querySelector('svg');
                    const isExpanded = this.getAttribute('aria-expanded') === 'true';

                    console.log('Button clicked:', contentId, 'isExpanded:', isExpanded);

                    if (isExpanded) {
                        // 收起
                        content.classList.add('hidden');
                        content.setAttribute('aria-hidden', 'true');
                        this.setAttribute('aria-expanded', 'false');
                        if (icon) icon.style.transform = 'rotate(0deg)';
                        console.log('Collapsed:', contentId);
                    } else {
                        // 展开
                        content.classList.remove('hidden');
                        content.setAttribute('aria-hidden', 'false');
                        this.setAttribute('aria-expanded', 'true');
                        if (icon) icon.style.transform = 'rotate(180deg)';
                        console.log('Expanded:', contentId);
                    }
                });

                // 键盘支持
                button.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' || e.key === ' ') {
                        e.preventDefault();
                        this.click();
                    }
                });
            });

            // 代码复制功能
            document.querySelectorAll('.copy-button').forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-copy-target');
                    const codeElement = document.getElementById(targetId);
                    const text = codeElement.textContent;

                    navigator.clipboard.writeText(text).then(() => {
                        const originalText = this.textContent;
                        this.textContent = '已复制';
                        this.classList.add('text-green-400');

                        setTimeout(() => {
                            this.textContent = originalText;
                            this.classList.remove('text-green-400');
                        }, 2000);
                    }).catch(err => {
                        console.error('复制失败:', err);
                    });
                });
            });

            // 平滑滚动到锚点
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>

<?php
// 引入通用底部
include 'includes/footer.php';
?>
