<?php
/**
 * Xun Framework 文档系统 - 侧边栏导航
 *
 * 包含主要的导航菜单，支持多级菜单结构和响应式设计。
 * 在移动端可以折叠显示。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

$current_page = xun_docs_get_current_page();
$navigation = xun_docs_get_navigation('main');
?>

<!-- 侧边栏容器 -->
<aside class="w-80 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 overflow-y-auto lg:block hidden" id="sidebar">
    <div class="p-6">
        <!-- 版本信息 -->
        <div class="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold">v</span>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">当前版本</h3>
                    <p class="text-sm text-blue-600 dark:text-blue-400 font-mono"><?php echo xun_docs_esc_html(XUN_DOCS_VERSION); ?></p>
                </div>
            </div>
        </div>

        <!-- 导航菜单 -->
        <nav class="space-y-6" role="navigation" aria-label="主导航">
            <?php foreach ($navigation as $section): ?>
            <div class="space-y-3">
                <!-- 分组标题 -->
                <div class="flex items-center space-x-2 px-3 py-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <?php if (!empty($section['icon'])): ?>
                        <div class="text-blue-600 dark:text-blue-400">
                            <?php echo xun_docs_get_icon($section['icon'], 'outline', 'w-5 h-5'); ?>
                        </div>
                    <?php endif; ?>
                    <h3 class="font-semibold text-gray-900 dark:text-white text-sm uppercase tracking-wide">
                        <?php echo xun_docs_esc_html($section['title']); ?>
                    </h3>
                </div>

                <!-- 菜单项 -->
                <ul class="space-y-1 ml-2">
                    <?php foreach ($section['items'] as $item): ?>
                    <?php 
                    $is_active = xun_docs_is_current_page($item['url']);
                    $is_highlight = !empty($item['highlight']);
                    ?>
                    <li>
                        <a 
                            href="<?php echo xun_docs_get_page_url($item['url']); ?>"
                            class="group flex items-center justify-between px-3 py-2 rounded-lg text-sm transition-all duration-200 <?php 
                                if ($is_active) {
                                    echo 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 font-medium';
                                } elseif ($is_highlight) {
                                    echo 'bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800 font-medium';
                                } else {
                                    echo 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white';
                                }
                            ?>"
                            <?php if ($is_active): ?>aria-current="page"<?php endif; ?>
                        >
                            <div class="flex-1">
                                <div class="font-medium">
                                    <?php echo xun_docs_esc_html($item['title']); ?>
                                </div>
                                <?php if (!empty($item['desc'])): ?>
                                <div class="text-xs <?php echo $is_active ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'; ?> mt-0.5">
                                    <?php echo xun_docs_esc_html($item['desc']); ?>
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($is_highlight): ?>
                            <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300">
                                    全部
                                </span>
                            </div>
                            <?php endif; ?>
                            
                            <div class="ml-2 opacity-0 group-hover:opacity-100 transition-opacity">
                                <?php echo xun_docs_get_icon('chevron-right', 'outline', 'w-4 h-4'); ?>
                            </div>
                        </a>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endforeach; ?>
        </nav>

        <!-- 快速链接 -->
        <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <h3 class="font-semibold text-gray-900 dark:text-white text-sm uppercase tracking-wide mb-3 px-3">
                快速链接
            </h3>
            <div class="space-y-2">
                <?php 
                $quick_links = [
                    ['title' => 'GitHub 仓库', 'url' => xun_docs_get_config('social_links')['github'] ?? '', 'icon' => 'code-bracket', 'external' => true],
                    ['title' => '官方网站', 'url' => xun_docs_get_config('social_links')['website'] ?? '', 'icon' => 'globe-alt', 'external' => true],
                    ['title' => '问题反馈', 'url' => 'mailto:' . (xun_docs_get_config('social_links')['email'] ?? ''), 'icon' => 'envelope', 'external' => true]
                ];
                
                foreach ($quick_links as $link):
                    if (empty($link['url'])) continue;
                ?>
                <a 
                    href="<?php echo xun_docs_esc_url($link['url']); ?>"
                    <?php if (!empty($link['external'])): ?>target="_blank" rel="noopener noreferrer"<?php endif; ?>
                    class="flex items-center space-x-3 px-3 py-2 rounded-lg text-sm text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white transition-colors group"
                >
                    <div class="text-gray-400 group-hover:text-gray-600 dark:group-hover:text-gray-300">
                        <?php if ($link['icon'] === 'code-bracket'): ?>
                            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                        <?php elseif ($link['icon'] === 'globe-alt'): ?>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"></path>
                            </svg>
                        <?php elseif ($link['icon'] === 'envelope'): ?>
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                            </svg>
                        <?php endif; ?>
                    </div>
                    <span><?php echo xun_docs_esc_html($link['title']); ?></span>
                    <?php if (!empty($link['external'])): ?>
                    <div class="ml-auto opacity-0 group-hover:opacity-100 transition-opacity">
                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                        </svg>
                    </div>
                    <?php endif; ?>
                </a>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- 底部信息 -->
        <div class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
            <div class="text-center">
                <p class="text-xs text-gray-500 dark:text-gray-400">
                    Made with ❤️ by <?php echo xun_docs_esc_html(xun_docs_get_config('author')); ?>
                </p>
                <p class="text-xs text-gray-400 dark:text-gray-500 mt-1">
                    © 2025 Xun Framework
                </p>
            </div>
        </div>
    </div>
</aside>

<!-- 移动端侧边栏遮罩 -->
<div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-30 lg:hidden hidden"></div>

<!-- 移动端侧边栏 -->
<aside id="mobile-sidebar" class="fixed inset-y-0 left-0 w-80 bg-white dark:bg-gray-900 border-r border-gray-200 dark:border-gray-700 transform -translate-x-full transition-transform duration-300 ease-in-out z-40 lg:hidden overflow-y-auto">
    <div class="p-6">
        <!-- 关闭按钮 -->
        <div class="flex items-center justify-between mb-6">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">导航菜单</h2>
            <button 
                type="button" 
                id="close-mobile-sidebar"
                class="p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-800 transition-colors"
                aria-label="关闭导航菜单"
            >
                <?php echo xun_docs_get_icon('x-mark', 'outline', 'w-6 h-6'); ?>
            </button>
        </div>

        <!-- 版本信息 -->
        <div class="mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div class="flex items-center space-x-3">
                <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center">
                    <span class="text-white font-bold">v</span>
                </div>
                <div>
                    <h3 class="font-semibold text-gray-900 dark:text-white">当前版本</h3>
                    <p class="text-sm text-blue-600 dark:text-blue-400 font-mono"><?php echo xun_docs_esc_html(XUN_DOCS_VERSION); ?></p>
                </div>
            </div>
        </div>

        <!-- 导航菜单（复制桌面端内容） -->
        <nav class="space-y-6" role="navigation" aria-label="移动端主导航">
            <?php foreach ($navigation as $section): ?>
            <div class="space-y-3">
                <!-- 分组标题 -->
                <div class="flex items-center space-x-2 px-3 py-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <?php if (!empty($section['icon'])): ?>
                        <div class="text-blue-600 dark:text-blue-400">
                            <?php echo xun_docs_get_icon($section['icon'], 'outline', 'w-5 h-5'); ?>
                        </div>
                    <?php endif; ?>
                    <h3 class="font-semibold text-gray-900 dark:text-white text-sm uppercase tracking-wide">
                        <?php echo xun_docs_esc_html($section['title']); ?>
                    </h3>
                </div>

                <!-- 菜单项 -->
                <ul class="space-y-1 ml-2">
                    <?php foreach ($section['items'] as $item): ?>
                    <?php 
                    $is_active = xun_docs_is_current_page($item['url']);
                    $is_highlight = !empty($item['highlight']);
                    ?>
                    <li>
                        <a 
                            href="<?php echo xun_docs_get_page_url($item['url']); ?>"
                            class="group flex items-center justify-between px-3 py-2 rounded-lg text-sm transition-all duration-200 <?php 
                                if ($is_active) {
                                    echo 'bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 font-medium';
                                } elseif ($is_highlight) {
                                    echo 'bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 text-green-700 dark:text-green-300 border border-green-200 dark:border-green-800 font-medium';
                                } else {
                                    echo 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white';
                                }
                            ?>"
                            <?php if ($is_active): ?>aria-current="page"<?php endif; ?>
                        >
                            <div class="flex-1">
                                <div class="font-medium">
                                    <?php echo xun_docs_esc_html($item['title']); ?>
                                </div>
                                <?php if (!empty($item['desc'])): ?>
                                <div class="text-xs <?php echo $is_active ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500 dark:text-gray-400'; ?> mt-0.5">
                                    <?php echo xun_docs_esc_html($item['desc']); ?>
                                </div>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($is_highlight): ?>
                            <div class="ml-2">
                                <span class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-300">
                                    全部
                                </span>
                            </div>
                            <?php endif; ?>
                        </a>
                    </li>
                    <?php endforeach; ?>
                </ul>
            </div>
            <?php endforeach; ?>
        </nav>
    </div>
</aside>
