<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问

/**
 * Xun Framework 图片库字段类型
 * 
 * 这个类实现了现代化的图片库管理功能，提供比Codestar Framework更优秀的用户体验。
 * 支持拖拽排序、批量操作、响应式设计等高级功能。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Field_gallery' ) ) {
    
    /**
     * XUN_Field_gallery 图片库字段类
     * 
     * 提供完整的图片库管理功能，包括：
     * - 多图片上传和管理
     * - 拖拽排序功能
     * - 图片预览和编辑
     * - 批量操作（删除、选择等）
     * - 响应式网格布局
     * - 现代化用户界面
     * - 键盘导航支持
     * - 无障碍访问优化
     * - 图片懒加载
     * - 文件大小和格式验证
     * 
     * @since 1.0
     */
    class XUN_Field_gallery extends XUN_Fields {
        
        /**
         * 构造函数
         * 
         * 初始化图片库字段实例。
         * 
         * @since 1.0
         * 
         * @param array  $field  字段配置数组
         * @param mixed  $value  字段值（图片ID数组，逗号分隔的字符串）
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }
        
        /**
         * 渲染图片库字段
         *
         * 输出现代化图片库字段的HTML代码，包括上传区域、图片网格、操作按钮等。
         *
         * @since 1.0
         */
        public function render() {
            
            // 解析字段配置
            $args = wp_parse_args( $this->field, array(
                'add_title'       => '添加图片',
                'edit_title'      => '编辑图片库',
                'clear_title'     => '清空图片库',
                'remove_title'    => '移除图片',
                'select_all'      => '全选',
                'deselect_all'    => '取消全选',
                'delete_selected' => '删除选中',
                'max_files'       => 0, // 0表示无限制
                'min_files'       => 0,
                'allowed_types'   => array( 'image' ),
                'max_file_size'   => 0, // 0表示使用WordPress默认限制
                'preview_size'    => 'thumbnail',
                'grid_columns'    => array( 'sm' => 2, 'md' => 3, 'lg' => 4, 'xl' => 5 ),
                'enable_sorting'  => true,
                'enable_batch'    => true,
                'enable_preview'  => true,
                'upload_text'     => '点击上传或拖拽图片到此处',
                'upload_hint'     => '支持 JPG、PNG、GIF 格式',
                'empty_text'      => '暂无图片，点击上传按钮添加图片',
                'loading_text'    => '加载中...',
                'error_max_files' => '最多只能上传 {max} 张图片',
                'error_min_files' => '至少需要上传 {min} 张图片',
                'error_file_type' => '不支持的文件类型',
                'error_file_size' => '文件大小超出限制'
            ) );
            
            // 处理图片ID值
            $image_ids = array();
            if ( ! empty( $this->value ) ) {
                if ( is_string( $this->value ) ) {
                    $image_ids = array_filter( explode( ',', $this->value ) );
                } elseif ( is_array( $this->value ) ) {
                    $image_ids = array_filter( $this->value );
                }
                $image_ids = array_map( 'intval', $image_ids );
            }
            
            // 生成唯一ID
            $field_id = $this->field_id();
            $field_name = $this->field_name();
            
            // 输出前置内容
            echo $this->field_before();
            
            // 开始字段容器
            echo '<div class="xun-gallery-field" data-field-id="' . esc_attr( $field_id ) . '" data-max-files="' . esc_attr( $args['max_files'] ) . '" data-min-files="' . esc_attr( $args['min_files'] ) . '">';
            
            // 隐藏输入框存储图片ID
            echo '<input type="hidden" name="' . esc_attr( $field_name ) . '" value="' . esc_attr( implode( ',', $image_ids ) ) . '" class="xun-gallery-input" />';
            
            // 主容器
            echo '<div class="xun-gallery-container' . ( empty( $image_ids ) ? ' xun-gallery-empty' : '' ) . '">';
            
            // 工具栏
            $this->render_toolbar( $args );
            
            // 上传区域
            $this->render_upload_area( $args );
            
            // 图片网格
            $this->render_image_grid( $image_ids, $args );
            
            // 空状态
            $this->render_empty_state( $args );
            
            echo '</div>'; // 结束主容器
            
            // 加载状态
            echo '<div class="xun-gallery-loading hidden">';
            echo '<div class="flex items-center justify-center py-8">';
            echo '<div class="animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>';
            echo '<span class="ml-3 text-sm text-gray-600">' . esc_html( $args['loading_text'] ) . '</span>';
            echo '</div>';
            echo '</div>';
            
            echo '</div>'; // 结束字段容器
            
            // 输出后置内容
            echo $this->field_after();
            
            // 输出JavaScript配置
            $this->render_script_config( $args );
        }
        
        /**
         * 渲染工具栏
         * 
         * @since 1.0
         * 
         * @param array $args 字段配置参数
         */
        private function render_toolbar( $args ) {
            echo '<div class="xun-gallery-toolbar flex flex-wrap items-center justify-between gap-3 mb-4 p-3 bg-gray-50 rounded-lg border border-gray-200">';
            
            // 左侧按钮组
            echo '<div class="flex flex-wrap items-center gap-2">';
            
            // 添加图片按钮
            echo '<button type="button" class="xun-gallery-add-btn inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">';
            echo '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>';
            echo '</svg>';
            echo esc_html( $args['add_title'] );
            echo '</button>';
            
            // 编辑按钮（有图片时显示）
            echo '<button type="button" class="xun-gallery-edit-btn hidden inline-flex items-center px-3 py-2 border border-gray-300 text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">';
            echo '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>';
            echo '</svg>';
            echo esc_html( $args['edit_title'] );
            echo '</button>';
            
            echo '</div>';
            
            // 右侧按钮组
            echo '<div class="flex flex-wrap items-center gap-2">';
            
            if ( $args['enable_batch'] ) {
                // 批量操作按钮
                echo '<div class="xun-gallery-batch-controls hidden">';
                
                // 全选按钮
                echo '<button type="button" class="xun-gallery-select-all-btn text-sm text-indigo-600 hover:text-indigo-800 font-medium">';
                echo esc_html( $args['select_all'] );
                echo '</button>';
                
                echo '<span class="text-gray-300 mx-2">|</span>';
                
                // 取消全选按钮
                echo '<button type="button" class="xun-gallery-deselect-all-btn text-sm text-gray-600 hover:text-gray-800 font-medium">';
                echo esc_html( $args['deselect_all'] );
                echo '</button>';
                
                echo '<span class="text-gray-300 mx-2">|</span>';
                
                // 删除选中按钮
                echo '<button type="button" class="xun-gallery-delete-selected-btn text-sm text-red-600 hover:text-red-800 font-medium">';
                echo esc_html( $args['delete_selected'] );
                echo '</button>';
                
                echo '</div>';
                
                // 批量模式切换按钮
                echo '<button type="button" class="xun-gallery-batch-toggle-btn hidden text-sm text-gray-600 hover:text-gray-800 font-medium">';
                echo '批量操作';
                echo '</button>';
            }
            
            // 清空按钮（有图片时显示）
            echo '<button type="button" class="xun-gallery-clear-btn hidden text-sm text-red-600 hover:text-red-800 font-medium">';
            echo esc_html( $args['clear_title'] );
            echo '</button>';
            
            echo '</div>';
            
            echo '</div>';
        }
        
        /**
         * 渲染上传区域
         * 
         * @since 1.0
         * 
         * @param array $args 字段配置参数
         */
        private function render_upload_area( $args ) {
            echo '<div class="xun-gallery-upload-area hidden border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-gray-400 transition-colors duration-200 mb-4">';
            
            echo '<div class="space-y-4">';
            
            // 上传图标
            echo '<div class="mx-auto w-12 h-12 text-gray-400">';
            echo '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>';
            echo '</svg>';
            echo '</div>';
            
            // 上传文本
            echo '<div>';
            echo '<p class="text-lg font-medium text-gray-900">' . esc_html( $args['upload_text'] ) . '</p>';
            echo '<p class="text-sm text-gray-500 mt-1">' . esc_html( $args['upload_hint'] ) . '</p>';
            echo '</div>';
            
            echo '</div>';
            
            echo '</div>';
        }

        /**
         * 渲染图片网格
         *
         * @since 1.0
         *
         * @param array $image_ids 图片ID数组
         * @param array $args 字段配置参数
         */
        private function render_image_grid( $image_ids, $args ) {
            // 响应式网格类
            $grid_classes = array(
                'xun-gallery-grid',
                'grid',
                'gap-4',
                'grid-cols-' . $args['grid_columns']['sm'],
                'md:grid-cols-' . $args['grid_columns']['md'],
                'lg:grid-cols-' . $args['grid_columns']['lg'],
                'xl:grid-cols-' . $args['grid_columns']['xl']
            );

            if ( $args['enable_sorting'] ) {
                $grid_classes[] = 'xun-gallery-sortable';
            }

            echo '<div class="' . esc_attr( implode( ' ', $grid_classes ) ) . '">';

            foreach ( $image_ids as $image_id ) {
                $this->render_image_item( $image_id, $args );
            }

            echo '</div>';
        }

        /**
         * 渲染单个图片项
         *
         * @since 1.0
         *
         * @param int   $image_id 图片ID
         * @param array $args 字段配置参数
         */
        private function render_image_item( $image_id, $args ) {
            $image = wp_get_attachment_image_src( $image_id, $args['preview_size'] );
            $image_full = wp_get_attachment_image_src( $image_id, 'full' );
            $image_alt = get_post_meta( $image_id, '_wp_attachment_image_alt', true );
            $image_title = get_the_title( $image_id );

            if ( ! $image ) {
                return; // 图片不存在，跳过
            }

            echo '<div class="xun-gallery-item group relative bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200" data-image-id="' . esc_attr( $image_id ) . '">';

            // 批量选择复选框
            if ( $args['enable_batch'] ) {
                echo '<div class="xun-gallery-checkbox absolute top-2 left-2 z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200">';
                echo '<input type="checkbox" class="xun-gallery-item-checkbox w-4 h-4 text-indigo-600 bg-white border-gray-300 rounded focus:ring-indigo-500 focus:ring-2" value="' . esc_attr( $image_id ) . '">';
                echo '</div>';
            }

            // 图片容器
            echo '<div class="aspect-square relative overflow-hidden">';

            // 图片
            echo '<img src="' . esc_url( $image[0] ) . '" alt="' . esc_attr( $image_alt ) . '" class="absolute inset-0 w-full h-full object-cover" loading="lazy">';

            // 悬停遮罩
            echo '<div class="absolute inset-0 bg-transparent group-hover:bg-black group-hover:bg-opacity-30 transition-all duration-200 flex items-center justify-center pointer-events-none">';

            // 操作按钮
            echo '<div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2 pointer-events-auto">';

            if ( $args['enable_preview'] ) {
                // 预览按钮
                echo '<button type="button" class="xun-gallery-preview-btn p-2 bg-white bg-opacity-90 rounded-full text-gray-700 hover:bg-opacity-100 transition-all duration-200" data-image-url="' . esc_url( $image_full[0] ) . '" data-image-title="' . esc_attr( $image_title ) . '">';
                echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>';
                echo '</svg>';
                echo '</button>';
            }

            // 删除按钮
            echo '<button type="button" class="xun-gallery-remove-btn p-2 bg-red-500 bg-opacity-90 rounded-full text-white hover:bg-opacity-100 transition-all duration-200" data-image-id="' . esc_attr( $image_id ) . '">';
            echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>';
            echo '</svg>';
            echo '</button>';

            echo '</div>';

            echo '</div>';

            echo '</div>';

            // 图片信息
            echo '<div class="p-3">';
            echo '<p class="text-sm font-medium text-gray-900 truncate" title="' . esc_attr( $image_title ) . '">' . esc_html( $image_title ) . '</p>';
            echo '<p class="text-xs text-gray-500 mt-1">ID: ' . esc_html( $image_id ) . '</p>';
            echo '</div>';

            // 拖拽手柄（如果启用排序）
            if ( $args['enable_sorting'] ) {
                echo '<div class="xun-gallery-drag-handle absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity duration-200 cursor-move p-1 bg-white bg-opacity-90 rounded">';
                echo '<svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4"></path>';
                echo '</svg>';
                echo '</div>';
            }

            echo '</div>';
        }

        /**
         * 渲染空状态
         *
         * @since 1.0
         *
         * @param array $args 字段配置参数
         */
        private function render_empty_state( $args ) {
            echo '<div class="xun-gallery-empty-state text-center py-12">';

            // 空状态图标
            echo '<div class="mx-auto w-16 h-16 text-gray-300 mb-4">';
            echo '<svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>';
            echo '</svg>';
            echo '</div>';

            // 空状态文本
            echo '<p class="text-lg font-medium text-gray-900 mb-2">图片库为空</p>';
            echo '<p class="text-sm text-gray-500 mb-6">' . esc_html( $args['empty_text'] ) . '</p>';

            // 上传按钮
            echo '<button type="button" class="xun-gallery-add-btn inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-colors duration-200">';
            echo '<svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>';
            echo '</svg>';
            echo esc_html( $args['add_title'] );
            echo '</button>';

            echo '</div>';
        }

        /**
         * 渲染JavaScript配置
         *
         * @since 1.0
         *
         * @param array $args 字段配置参数
         */
        private function render_script_config( $args ) {
            $config = array(
                'maxFiles'      => $args['max_files'],
                'minFiles'      => $args['min_files'],
                'allowedTypes'  => $args['allowed_types'],
                'maxFileSize'   => $args['max_file_size'],
                'enableSorting' => $args['enable_sorting'],
                'enableBatch'   => $args['enable_batch'],
                'enablePreview' => $args['enable_preview'],
                'previewSize'   => $args['preview_size'],
                'gridColumns'   => $args['grid_columns'],
                'messages'      => array(
                    'errorMaxFiles' => $args['error_max_files'],
                    'errorMinFiles' => $args['error_min_files'],
                    'errorFileType' => $args['error_file_type'],
                    'errorFileSize' => $args['error_file_size'],
                    'confirmClear'  => '确定要清空图片库吗？此操作不可撤销。',
                    'confirmDelete' => '确定要删除选中的图片吗？此操作不可撤销。',
                    'confirmRemove' => '确定要移除这张图片吗？'
                )
            );

            echo '<script type="application/json" class="xun-gallery-config">' . wp_json_encode( $config ) . '</script>';
        }

        /**
         * 初始化AJAX处理器
         *
         * @since 1.0
         */
        public static function init_ajax() {
            add_action( 'wp_ajax_xun_get_gallery_images', array( __CLASS__, 'ajax_get_gallery_images' ) );
        }

        /**
         * AJAX处理器：获取图片库图片数据
         *
         * @since 1.0
         */
        public static function ajax_get_gallery_images() {
            // 验证nonce
            if ( ! wp_verify_nonce( $_POST['nonce'], 'xun_gallery_nonce' ) ) {
                wp_die( 'Security check failed' );
            }

            // 获取图片ID数组
            $image_ids = isset( $_POST['image_ids'] ) ? array_map( 'intval', $_POST['image_ids'] ) : array();
            $preview_size = isset( $_POST['preview_size'] ) ? sanitize_text_field( $_POST['preview_size'] ) : 'thumbnail';

            if ( empty( $image_ids ) ) {
                wp_send_json_error( 'No image IDs provided' );
            }

            $images_data = array();

            foreach ( $image_ids as $image_id ) {
                $image_data = self::get_image_data( $image_id, $preview_size );
                if ( $image_data ) {
                    $images_data[] = $image_data;
                }
            }

            wp_send_json_success( $images_data );
        }

        /**
         * 获取单张图片的数据
         *
         * @since 1.0
         *
         * @param int    $image_id 图片ID
         * @param string $preview_size 预览尺寸
         *
         * @return array|false 图片数据数组或false
         */
        public static function get_image_data( $image_id, $preview_size = 'thumbnail' ) {
            $image_id = intval( $image_id );

            if ( ! $image_id || ! wp_attachment_is_image( $image_id ) ) {
                return false;
            }

            $thumbnail = wp_get_attachment_image_src( $image_id, $preview_size );
            $full = wp_get_attachment_image_src( $image_id, 'full' );
            $alt = get_post_meta( $image_id, '_wp_attachment_image_alt', true );
            $title = get_the_title( $image_id );

            if ( ! $thumbnail || ! $full ) {
                return false;
            }

            return array(
                'id'        => $image_id,
                'thumbnail' => esc_url( $thumbnail[0] ),
                'full'      => esc_url( $full[0] ),
                'alt'       => esc_attr( $alt ),
                'title'     => esc_html( $title ),
                'width'     => $thumbnail[1],
                'height'    => $thumbnail[2],
            );
        }

        /**
         * 加载字段资源
         *
         * @since 1.0
         */
        public function enqueue() {

            // 加载WordPress媒体库
            if ( ! did_action( 'wp_enqueue_media' ) ) {
                wp_enqueue_media();
            }


            // 加载字段JavaScript
            wp_enqueue_script(
                'xun-gallery-field',
                XUN_Setup::$url . '/assets/js/fields/gallery.js',
                array( 'jquery', 'jquery-ui-sortable', 'wp-util' ),
                XUN_VERSION,
                true
            );

            // 本地化脚本
            wp_localize_script( 'xun-gallery-field', 'xunGalleryField', array(
                'ajaxUrl'    => admin_url( 'admin-ajax.php' ),
                'nonce'      => wp_create_nonce( 'xun_gallery_nonce' ),
                'wpVersion'  => get_bloginfo( 'version' ),
                'i18n'       => array(
                    'mediaTitle'       => '选择图片',
                    'mediaButtonText'  => '选择图片',
                    'mediaLibrary'     => '媒体库',
                    'uploadFiles'      => '上传文件',
                    'selectFiles'      => '选择文件',
                    'editGallery'      => '编辑图片库',
                    'insertGallery'    => '插入图片库',
                    'updateGallery'    => '更新图片库',
                    'loading'          => '加载中...',
                    'error'            => '发生错误',
                    'success'          => '操作成功'
                )
            ) );
        }
    }
}
