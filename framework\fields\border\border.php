<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问

/**
 * Border 字段类 - 组合式架构
 *
 * 使用组合式设计，通过XUN::field()方法组合基础字段来构建完整的边框设置功能。
 * 支持边框宽度、样式、颜色等所有CSS边框属性。
 *
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.1.0
 */

if ( ! class_exists( 'XUN_Field_border' ) ) {

    /**
     * XUN_Field_border 边框字段类
     *
     * 采用组合式架构，通过组合基础字段实现复杂的边框设置功能。
     * 支持边框宽度、样式、颜色等所有CSS边框属性。
     *
     * @since 1.1.0
     */
    class XUN_Field_border extends XUN_Fields {

        /**
         * 构造函数
         *
         * 初始化border字段实例。
         *
         * @since 1.1.0
         *
         * @param array  $field   字段配置数组
         * @param string $value   字段值
         * @param string $unique  唯一标识符
         * @param string $where   字段位置标识
         * @param string $parent  父级标识符
         */
        public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }

        /**
         * 安全获取颜色值
         *
         * 处理可能的数组格式，确保返回字符串
         *
         * @since 1.1.0
         *
         * @param mixed $color_value 颜色值（可能是字符串或数组）
         *
         * @return string 处理后的颜色字符串
         */
        private function get_safe_color_value( $color_value ) {
            if ( is_array( $color_value ) ) {
                // 如果是数组，尝试获取主要颜色值
                if ( isset( $color_value['color'] ) && is_string( $color_value['color'] ) ) {
                    return $color_value['color'];
                } elseif ( isset( $color_value[0] ) && is_string( $color_value[0] ) ) {
                    return $color_value[0];
                } else {
                    return '';
                }
            } elseif ( is_string( $color_value ) ) {
                return $color_value;
            } else {
                return '';
            }
        }

        /**
         * 渲染边框字段 - 组合式架构
         *
         * 使用XUN::field()方法组合基础字段来构建边框设置界面。
         *
         * @since 1.1.0
         */
        public function render() {

            // 字段配置参数
            $args = wp_parse_args( $this->field, array(
                'top'    => true,
                'right'  => true,
                'bottom' => true,
                'left'   => true,
                'all'    => false,
                'color'  => true,
                'style'  => true,
                'unit'   => 'px',
            ) );

            // 边框样式选项
            $border_styles = array(
                'solid'  => '实线',
                'dashed' => '虚线',
                'dotted' => '点线',
                'double' => '双线',
                'groove' => '凹槽',
                'ridge'  => '凸起',
                'inset'  => '内嵌',
                'outset' => '外凸',
                'none'   => '无边框',
            );

            // 默认值
            $default_value = array(
                'top'    => '',
                'right'  => '',
                'bottom' => '',
                'left'   => '',
                'color'  => '',
                'style'  => 'solid',
                'all'    => '',
            );

            $default_value = ( ! empty( $this->field['default'] ) ) ? wp_parse_args( $this->field['default'], $default_value ) : $default_value;
            $this->value = wp_parse_args( $this->value, $default_value );

            // 输出前置内容
            echo $this->field_before();

            // 边框设置容器
            echo '<div class="xun-border-field">';
            echo '<div class="space-y-6 p-6 bg-gray-50 rounded-lg border border-gray-200">';

            // 边框宽度设置 - 使用组合的基础字段
            echo '<div>';
            echo '<h4 class="text-sm font-medium text-gray-700 mb-4">边框宽度</h4>';

            if ( ! empty( $args['all'] ) ) {
                // 统一设置模式
                echo '<div class="flex justify-center">';

                // 使用text字段，避免HTML5验证冲突
                $width_field = array(
                    'id'          => 'all',
                    'type'        => 'text',
                    'placeholder' => '全部边框宽度',
                    'attributes'  => array(
                        'inputmode' => 'numeric',
                        'pattern'   => '[0-9]*\.?[0-9]*',
                        'class'     => 'text-right',
                    ),
                );

                // 为all字段创建唯一的字段配置
                $width_field['id'] = $this->field_id() . '_all';
                $width_field['name'] = $this->field_name( '[all]' );
                XUN::field( $width_field, $this->value['all'], '', '' );
                echo '</div>';
            } else {
                // 分别设置四个方向
                echo '<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3">';

                $directions = array(
                    'top'    => '上边框',
                    'right'  => '右边框',
                    'bottom' => '下边框',
                    'left'   => '左边框',
                );

                foreach ( $directions as $direction => $label ) {
                    if ( ! empty( $args[$direction] ) ) {
                        echo '<div>';
                        echo '<label class="block text-xs font-medium text-gray-600 mb-1">' . esc_html( $label ) . '</label>';

                        // 使用text字段，避免HTML5验证冲突
                        $width_field = array(
                            'id'          => $direction,
                            'type'        => 'text',
                            'placeholder' => $label,
                            'attributes'  => array(
                                'inputmode' => 'numeric',
                                'pattern'   => '[0-9]*\.?[0-9]*',
                                'class'     => 'text-right',
                            ),
                        );

                        // 为每个方向创建唯一的字段配置
                        $width_field['id'] = $this->field_id() . '_' . $direction;
                        $width_field['name'] = $this->field_name( '[' . $direction . ']' );
                        XUN::field( $width_field, $this->value[$direction], '', '' );
                        echo '</div>';
                    }
                }

                echo '</div>';
            }

            echo '</div>';

            // 边框样式和颜色设置 - 使用组合的基础字段
            echo '<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mt-6">';

            // 边框样式 - 使用组合的select字段
            if ( ! empty( $args['style'] ) ) {
                echo '<div>';
                echo '<h4 class="text-sm font-medium text-gray-700 mb-3">边框样式</h4>';

                // 临时使用内联select实现，等select字段完成后替换为：
                // $style_field = array(
                //     'id'      => 'style',
                //     'type'    => 'select',
                //     'options' => $border_styles,
                // );
                // XUN::field( $style_field, $this->value['style'], $this->field_name(), 'field/border' );

                echo '<select name="' . esc_attr( $this->field_name( '[style]' ) ) . '" class="w-full px-4 py-3 border border-gray-300 rounded-lg shadow-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 bg-white text-sm transition-all duration-200">';
                foreach ( $border_styles as $style_key => $style_label ) {
                    $selected = ( $this->value['style'] === $style_key ) ? ' selected' : '';
                    echo '<option value="' . esc_attr( $style_key ) . '"' . $selected . '>' . esc_html( $style_label ) . '</option>';
                }
                echo '</select>';
                echo '</div>';
            }

            // 边框颜色 - 使用组合的color字段
            if ( ! empty( $args['color'] ) ) {
                echo '<div>';
                echo '<h4 class="text-sm font-medium text-gray-700 mb-3">边框颜色</h4>';

                $color_field = array(
                    'id'      => 'color',
                    'type'    => 'color',
                    'default' => '#000000',
                );

                // 确保传递给color字段的值是字符串格式
                $color_value = $this->get_safe_color_value( isset( $this->value['color'] ) ? $this->value['color'] : '' );

                // 为color字段创建唯一的字段配置
                $color_field['id'] = $this->field_id() . '_color';
                $color_field['name'] = $this->field_name( '[color]' );
                XUN::field( $color_field, $color_value, '', '' );
                echo '</div>';
            }

            echo '</div>'; // 关闭grid容器

            // 边框预览区域
            echo '<div class="mt-6">';
            echo '<h4 class="text-sm font-medium text-gray-700 mb-3">边框预览</h4>';
            echo '<div class="border-preview p-4 bg-white rounded-lg border border-gray-200 min-h-24 flex items-center justify-center text-sm text-gray-500">';
            echo '边框预览效果';
            echo '</div>';
            echo '</div>';

            echo '</div>'; // 关闭背景设置容器
            echo '</div>'; // 关闭主容器

            // 输出后置内容
            echo $this->field_after();
        }



        /**
         * 加载字段资源 - 组合式架构
         *
         * 在组合式架构中，资源加载主要由基础字段负责。
         * 这里只加载border字段特有的资源。
         *
         * @since 1.1.0
         */
        public function enqueue() {
            // 调用父类方法，注册到资源管理器
            parent::enqueue();

            // 直接加载border字段的JavaScript
            wp_enqueue_script(
                'xun-field-border',
                XUN_Setup::$url . '/assets/js/fields/border.js',
                array( 'jquery' ),
                XUN_Setup::$version,
                true
            );
        }

        /**
         * 输出CSS样式
         *
         * 根据字段值生成对应的CSS代码。
         *
         * @since 1.0
         *
         * @return string CSS代码
         */
        public function output() {
            $output = '';
            $unit = ! empty( $this->field['unit'] ) ? $this->field['unit'] : 'px';
            $important = ! empty( $this->field['output_important'] ) ? '!important' : '';
            $element = ( is_array( $this->field['output'] ) ) ? join( ',', $this->field['output'] ) : $this->field['output'];

            if ( empty( $element ) ) {
                return $output;
            }

            // 获取边框属性值
            $top    = ( isset( $this->value['top'] )    && $this->value['top']    !== '' ) ? $this->value['top']    : '';
            $right  = ( isset( $this->value['right'] )  && $this->value['right']  !== '' ) ? $this->value['right']  : '';
            $bottom = ( isset( $this->value['bottom'] ) && $this->value['bottom'] !== '' ) ? $this->value['bottom'] : '';
            $left   = ( isset( $this->value['left'] )   && $this->value['left']   !== '' ) ? $this->value['left']   : '';
            $style  = ( isset( $this->value['style'] )  && $this->value['style']  !== '' ) ? $this->value['style']  : '';
            // 处理color值，确保是字符串格式
            $color = $this->get_safe_color_value( isset( $this->value['color'] ) ? $this->value['color'] : '' );
            $all    = ( isset( $this->value['all'] )    && $this->value['all']    !== '' ) ? $this->value['all']    : '';

            if ( ! empty( $this->field['all'] ) && ( $all !== '' || $color !== '' || $style !== '' ) ) {
                // 统一设置模式
                $output  = $element . '{';
                $output .= ( $all   !== '' ) ? 'border-width:' . $all . $unit . $important . ';' : '';
                $output .= ( $color !== '' ) ? 'border-color:' . $color . $important . ';' : '';
                $output .= ( $style !== '' ) ? 'border-style:' . $style . $important . ';' : '';
                $output .= '}';
            } else if ( $top !== '' || $right !== '' || $bottom !== '' || $left !== '' || $color !== '' || $style !== '' ) {
                // 分别设置模式
                $output  = $element . '{';
                $output .= ( $top    !== '' ) ? 'border-top-width:' . $top . $unit . $important . ';' : '';
                $output .= ( $right  !== '' ) ? 'border-right-width:' . $right . $unit . $important . ';' : '';
                $output .= ( $bottom !== '' ) ? 'border-bottom-width:' . $bottom . $unit . $important . ';' : '';
                $output .= ( $left   !== '' ) ? 'border-left-width:' . $left . $unit . $important . ';' : '';
                $output .= ( $color  !== '' ) ? 'border-color:' . $color . $important . ';' : '';
                $output .= ( $style  !== '' ) ? 'border-style:' . $style . $important . ';' : '';
                $output .= '}';
            }

            return $output;
        }
    }
}
