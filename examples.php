<?php
/**
 * Xun Framework 文档系统 - 示例代码页面
 *
 * 提供完整的实战示例，包括主题选项、插件设置、高级用法等。
 * 采用分类展示和实时预览的方式，帮助开发者快速上手。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

// 页面配置
$header_config = [
    'site_title' => 'Xun Framework',
    'site_description' => '现代化的WordPress选项框架',
    'page_title' => '示例代码',
    'page_description' => '完整的实战示例和最佳实践，从基础配置到高级用法的完整代码示例',
    'keywords' => 'WordPress, Framework, 示例, 代码, 实战, 最佳实践, 主题开发',
    'current_page' => 'examples'
];

$footer_config = [
    'site_title' => 'Xun Framework',
    'site_description' => '现代化的WordPress选项框架',
    'version' => '1.0.0',
    'total_fields' => 23,
    'show_stats' => true
];

// 示例分类配置
$example_categories = [
    'basic' => [
        'title' => '基础示例',
        'description' => '快速上手的基础配置示例',
        'icon' => 'academic-cap',
        'color' => 'blue',
        'examples' => [
            'simple_theme_options' => [
                'title' => '简单主题选项',
                'description' => '创建一个包含基本字段的主题选项页面',
                'difficulty' => '初级',
                'time' => '5分钟',
                'features' => ['基础字段', '简单配置', '快速上手'],
                'code' => '<?php
// 在主题的 functions.php 文件中添加

// 1. 引入框架
require_once get_template_directory() . \'/xun-framework/xun-framework.php\';

// 2. 创建主题选项页面
XUN::createOptions( \'my_theme_options\', array(
    \'menu_title\'      => \'主题设置\',
    \'menu_slug\'       => \'my-theme-options\',
    \'menu_capability\' => \'manage_options\',
    \'framework_title\' => \'我的主题设置\',
    \'framework_class\' => \'wrap\',
    \'theme\'           => \'dark\',
    \'ajax_save\'       => true,
    \'show_reset\'      => true,
) );

// 3. 添加基本设置区块
XUN::createSection( \'my_theme_options\', array(
    \'title\'  => \'基本设置\',
    \'icon\'   => \'dashicons-admin-generic\',
    \'fields\' => array(
        array(
            \'id\'          => \'site_logo\',
            \'type\'        => \'media\',
            \'title\'       => \'网站Logo\',
            \'desc\'        => \'上传您的网站Logo图片\',
            \'library\'     => \'image\',
            \'preview\'     => true,
        ),
        array(
            \'id\'          => \'theme_color\',
            \'type\'        => \'color\',
            \'title\'       => \'主题颜色\',
            \'desc\'        => \'选择网站的主要颜色\',
            \'default\'     => \'#2563eb\',
            \'alpha\'       => true,
        ),
        array(
            \'id\'          => \'site_description\',
            \'type\'        => \'textarea\',
            \'title\'       => \'网站描述\',
            \'desc\'        => \'输入网站的简短描述\',
            \'rows\'        => 4,
            \'placeholder\' => \'请输入网站描述...\',
        ),
    ),
) );

// 4. 在主题中使用选项值
function get_theme_option( $field_id, $default = \'\' ) {
    return XUN::get_option( \'my_theme_options\', $field_id, $default );
}

// 5. 在模板中使用
// $logo = get_theme_option( \'site_logo\' );
// $color = get_theme_option( \'theme_color\', \'#2563eb\' );
// $description = get_theme_option( \'site_description\' );'
            ],
            'contact_form_settings' => [
                'title' => '联系表单设置',
                'description' => '创建联系表单的配置选项',
                'difficulty' => '初级',
                'time' => '10分钟',
                'features' => ['表单配置', '邮件设置', '验证选项'],
                'code' => '<?php
// 联系表单设置示例

XUN::createOptions( \'contact_form_settings\', array(
    \'menu_title\'      => \'联系表单\',
    \'menu_slug\'       => \'contact-form-settings\',
    \'framework_title\' => \'联系表单设置\',
    \'menu_parent\'     => \'options-general.php\',
) );

XUN::createSection( \'contact_form_settings\', array(
    \'title\'  => \'邮件设置\',
    \'fields\' => array(
        array(
            \'id\'          => \'admin_email\',
            \'type\'        => \'text\',
            \'title\'       => \'管理员邮箱\',
            \'desc\'        => \'接收联系表单的邮箱地址\',
            \'validate\'    => \'email\',
            \'placeholder\' => \'<EMAIL>\',
        ),
        array(
            \'id\'          => \'email_subject\',
            \'type\'        => \'text\',
            \'title\'       => \'邮件主题\',
            \'desc\'        => \'联系表单邮件的主题前缀\',
            \'default\'     => \'[网站联系] \',
        ),
        array(
            \'id\'          => \'auto_reply\',
            \'type\'        => \'switch\',
            \'title\'       => \'自动回复\',
            \'desc\'        => \'是否向用户发送自动回复邮件\',
            \'default\'     => true,
        ),
        array(
            \'id\'          => \'reply_message\',
            \'type\'        => \'textarea\',
            \'title\'       => \'回复内容\',
            \'desc\'        => \'自动回复邮件的内容\',
            \'default\'     => \'感谢您的留言，我们会尽快回复您。\',
            \'dependency\'  => array( \'auto_reply\', \'==\', true ),
        ),
    ),
) );

XUN::createSection( \'contact_form_settings\', array(
    \'title\'  => \'表单设置\',
    \'fields\' => array(
        array(
            \'id\'          => \'required_fields\',
            \'type\'        => \'checkbox\',
            \'title\'       => \'必填字段\',
            \'desc\'        => \'选择表单中的必填字段\',
            \'options\'     => array(
                \'name\'    => \'姓名\',
                \'email\'   => \'邮箱\',
                \'phone\'   => \'电话\',
                \'subject\' => \'主题\',
                \'message\' => \'留言内容\',
            ),
            \'default\'     => array( \'name\', \'email\', \'message\' ),
        ),
        array(
            \'id\'          => \'enable_captcha\',
            \'type\'        => \'switch\',
            \'title\'       => \'启用验证码\',
            \'desc\'        => \'防止垃圾邮件的验证码功能\',
            \'default\'     => false,
        ),
        array(
            \'id\'          => \'success_message\',
            \'type\'        => \'text\',
            \'title\'       => \'成功提示\',
            \'desc\'        => \'表单提交成功后的提示信息\',
            \'default\'     => \'感谢您的留言，我们已收到您的信息。\',
        ),
    ),
) );'
            ]
        ]
    ],
    'advanced' => [
        'title' => '高级示例',
        'description' => '复杂功能和高级用法示例',
        'icon' => 'cog',
        'color' => 'purple',
        'examples' => [
            'complete_theme_options' => [
                'title' => '完整主题选项',
                'description' => '包含多个区块和高级字段的完整主题选项系统',
                'difficulty' => '高级',
                'time' => '30分钟',
                'features' => ['多区块', '高级字段', '条件显示', '数据验证'],
                'code' => '<?php
// 完整的主题选项系统示例

// 创建主题选项页面
XUN::createOptions( \'advanced_theme_options\', array(
    \'menu_title\'      => \'主题设置\',
    \'menu_slug\'       => \'advanced-theme-options\',
    \'framework_title\' => \'高级主题设置\',
    \'framework_class\' => \'wrap\',
    \'theme\'           => \'dark\',
    \'ajax_save\'       => true,
    \'show_reset\'      => true,
    \'show_search\'     => true,
    \'show_footer\'     => true,
    \'footer_text\'     => \'感谢使用我们的主题\',
) );

// 1. 基本设置区块
XUN::createSection( \'advanced_theme_options\', array(
    \'title\'  => \'基本设置\',
    \'icon\'   => \'dashicons-admin-generic\',
    \'fields\' => array(
        array(
            \'id\'          => \'site_logo\',
            \'type\'        => \'media\',
            \'title\'       => \'网站Logo\',
            \'desc\'        => \'上传网站Logo，建议尺寸：200x60px\',
            \'library\'     => \'image\',
            \'preview\'     => true,
        ),
        array(
            \'id\'          => \'favicon\',
            \'type\'        => \'media\',
            \'title\'       => \'网站图标\',
            \'desc\'        => \'上传网站图标，建议尺寸：32x32px\',
            \'library\'     => \'image\',
            \'preview\'     => true,
        ),
        array(
            \'id\'          => \'site_title_display\',
            \'type\'        => \'radio\',
            \'title\'       => \'标题显示方式\',
            \'desc\'        => \'选择网站标题的显示方式\',
            \'options\'     => array(
                \'logo_only\'  => \'仅显示Logo\',
                \'text_only\'  => \'仅显示文字\',
                \'logo_text\'  => \'Logo + 文字\',
            ),
            \'default\'     => \'logo_text\',
        ),
        array(
            \'id\'          => \'custom_title\',
            \'type\'        => \'text\',
            \'title\'       => \'自定义标题\',
            \'desc\'        => \'自定义网站标题文字\',
            \'dependency\'  => array( \'site_title_display\', \'any\', \'text_only,logo_text\' ),
        ),
    ),
) );

// 2. 颜色和样式区块
XUN::createSection( \'advanced_theme_options\', array(
    \'title\'  => \'颜色和样式\',
    \'icon\'   => \'dashicons-art\',
    \'fields\' => array(
        array(
            \'id\'          => \'color_scheme\',
            \'type\'        => \'palette\',
            \'title\'       => \'配色方案\',
            \'desc\'        => \'选择或自定义网站配色方案\',
            \'options\'     => array(
                \'blue\'   => array( \'#2563eb\', \'#1d4ed8\', \'#1e40af\' ),
                \'green\'  => array( \'#10b981\', \'#059669\', \'#047857\' ),
                \'purple\' => array( \'#8b5cf6\', \'#7c3aed\', \'#6d28d9\' ),
                \'red\'    => array( \'#ef4444\', \'#dc2626\', \'#b91c1c\' ),
            ),
            \'default\'     => \'blue\',
        ),
        array(
            \'id\'          => \'primary_color\',
            \'type\'        => \'color\',
            \'title\'       => \'主要颜色\',
            \'desc\'        => \'网站的主要品牌颜色\',
            \'default\'     => \'#2563eb\',
            \'alpha\'       => true,
        ),
        array(
            \'id\'          => \'secondary_color\',
            \'type\'        => \'color\',
            \'title\'       => \'次要颜色\',
            \'desc\'        => \'网站的次要颜色\',
            \'default\'     => \'#6b7280\',
            \'alpha\'       => true,
        ),
        array(
            \'id\'          => \'custom_css\',
            \'type\'        => \'code\',
            \'title\'       => \'自定义CSS\',
            \'desc\'        => \'添加自定义CSS代码\',
            \'settings\'    => array(
                \'theme\' => \'monokai\',
                \'mode\'  => \'css\',
            ),
        ),
    ),
) );'
            ],
            'plugin_settings' => [
                'title' => '插件设置页面',
                'description' => '为WordPress插件创建完整的设置页面',
                'difficulty' => '中级',
                'time' => '20分钟',
                'features' => ['插件集成', '多页面', '数据导入导出', '权限控制'],
                'code' => '<?php
// 插件设置页面示例

class MyPlugin_Settings {

    public function __construct() {
        add_action( \'plugins_loaded\', array( $this, \'init\' ) );
    }

    public function init() {
        // 确保框架已加载
        if ( ! class_exists( \'XUN\' ) ) {
            return;
        }

        $this->create_settings_page();
    }

    private function create_settings_page() {
        // 主设置页面
        XUN::createOptions( \'myplugin_settings\', array(
            \'menu_title\'      => \'我的插件\',
            \'menu_slug\'       => \'myplugin-settings\',
            \'menu_capability\' => \'manage_options\',
            \'framework_title\' => \'我的插件设置\',
            \'menu_type\'       => \'menu\',
            \'menu_icon\'       => \'dashicons-admin-plugins\',
            \'menu_position\'   => 30,
            \'ajax_save\'       => true,
            \'show_reset\'      => true,
        ) );

        // 基本设置区块
        XUN::createSection( \'myplugin_settings\', array(
            \'title\'  => \'基本设置\',
            \'fields\' => array(
                array(
                    \'id\'          => \'enable_plugin\',
                    \'type\'        => \'switch\',
                    \'title\'       => \'启用插件\',
                    \'desc\'        => \'开启或关闭插件功能\',
                    \'default\'     => true,
                ),
                array(
                    \'id\'          => \'api_key\',
                    \'type\'        => \'text\',
                    \'title\'       => \'API密钥\',
                    \'desc\'        => \'输入第三方服务的API密钥\',
                    \'attributes\'  => array( \'type\' => \'password\' ),
                    \'dependency\'  => array( \'enable_plugin\', \'==\', true ),
                ),
                array(
                    \'id\'          => \'cache_duration\',
                    \'type\'        => \'slider\',
                    \'title\'       => \'缓存时间\',
                    \'desc\'        => \'设置数据缓存时间（小时）\',
                    \'min\'         => 1,
                    \'max\'         => 24,
                    \'step\'        => 1,
                    \'unit\'        => \'小时\',
                    \'default\'     => 6,
                ),
            ),
        ) );
    }
}

// 初始化插件设置
new MyPlugin_Settings();

// 获取插件选项的辅助函数
function myplugin_get_option( $key, $default = \'\' ) {
    return XUN::get_option( \'myplugin_settings\', $key, $default );
}'
            ],
            'conditional_fields' => [
                'title' => '条件字段显示',
                'description' => '基于其他字段值动态显示/隐藏字段',
                'difficulty' => '高级',
                'time' => '15分钟',
                'features' => ['条件逻辑', '动态显示', '复杂依赖', '用户体验'],
                'code' => '<?php
// 条件字段显示示例

XUN::createOptions( \'conditional_demo\', array(
    \'menu_title\'      => \'条件字段演示\',
    \'menu_slug\'       => \'conditional-demo\',
    \'framework_title\' => \'条件字段演示\',
) );

XUN::createSection( \'conditional_demo\', array(
    \'title\'  => \'条件显示示例\',
    \'fields\' => array(
        // 主控制字段
        array(
            \'id\'          => \'layout_type\',
            \'type\'        => \'radio\',
            \'title\'       => \'布局类型\',
            \'desc\'        => \'选择页面布局类型\',
            \'options\'     => array(
                \'default\' => \'默认布局\',
                \'custom\'  => \'自定义布局\',
                \'grid\'    => \'网格布局\',
            ),
            \'default\'     => \'default\',
        ),

        // 自定义布局相关字段
        array(
            \'id\'          => \'custom_width\',
            \'type\'        => \'slider\',
            \'title\'       => \'自定义宽度\',
            \'desc\'        => \'设置自定义布局的宽度\',
            \'min\'         => 800,
            \'max\'         => 1400,
            \'step\'        => 50,
            \'unit\'        => \'px\',
            \'default\'     => 1200,
            \'dependency\'  => array( \'layout_type\', \'==\', \'custom\' ),
        ),
        array(
            \'id\'          => \'enable_animation\',
            \'type\'        => \'switch\',
            \'title\'       => \'启用动画\',
            \'desc\'        => \'为布局添加动画效果\',
            \'default\'     => false,
            \'dependency\'  => array( \'layout_type\', \'!=\', \'default\' ),
        ),
    ),
) );'
            ]
        ]
    ],
    'utilities' => [
        'title' => '实用工具',
        'description' => '常用的实用工具和代码片段',
        'icon' => 'wrench-screwdriver',
        'color' => 'orange',
        'examples' => [
            'data_import_export' => [
                'title' => '数据导入导出',
                'description' => '实现选项数据的导入和导出功能',
                'difficulty' => '中级',
                'time' => '25分钟',
                'features' => ['数据备份', 'JSON格式', '批量操作', '错误处理'],
                'code' => '<?php
// 数据导入导出功能示例

XUN::createOptions( \'backup_demo\', array(
    \'menu_title\'      => \'数据备份\',
    \'menu_slug\'       => \'backup-demo\',
    \'framework_title\' => \'数据导入导出演示\',
) );

XUN::createSection( \'backup_demo\', array(
    \'title\'  => \'导出设置\',
    \'fields\' => array(
        array(
            \'id\'          => \'export_data\',
            \'type\'        => \'button\',
            \'title\'       => \'导出所有设置\',
            \'desc\'        => \'将当前所有设置导出为JSON文件\',
            \'button_text\' => \'导出设置\',
            \'callback\'    => \'export_settings_callback\',
        ),
        array(
            \'id\'          => \'export_options\',
            \'type\'        => \'checkbox\',
            \'title\'       => \'导出选项\',
            \'desc\'        => \'选择要导出的设置类型\',
            \'options\'     => array(
                \'theme_options\'  => \'主题设置\',
                \'plugin_options\' => \'插件设置\',
                \'user_data\'      => \'用户数据\',
                \'custom_fields\'  => \'自定义字段\',
            ),
            \'default\'     => array( \'theme_options\', \'plugin_options\' ),
        ),
    ),
) );

XUN::createSection( \'backup_demo\', array(
    \'title\'  => \'导入设置\',
    \'fields\' => array(
        array(
            \'id\'          => \'import_file\',
            \'type\'        => \'media\',
            \'title\'       => \'选择备份文件\',
            \'desc\'        => \'选择之前导出的JSON备份文件\',
            \'library\'     => \'application/json\',
            \'button_title\' => \'选择JSON文件\',
        ),
        array(
            \'id\'          => \'import_mode\',
            \'type\'        => \'radio\',
            \'title\'       => \'导入模式\',
            \'desc\'        => \'选择导入数据的处理方式\',
            \'options\'     => array(
                \'merge\'     => \'合并数据（保留现有设置）\',
                \'overwrite\' => \'覆盖数据（替换所有设置）\',
                \'selective\' => \'选择性导入\',
            ),
            \'default\'     => \'merge\',
        ),
        array(
            \'id\'          => \'import_data\',
            \'type\'        => \'button\',
            \'title\'       => \'开始导入\',
            \'desc\'        => \'执行数据导入操作\',
            \'button_text\' => \'导入设置\',
            \'callback\'    => \'import_settings_callback\',
        ),
    ),
) );

// 导出回调函数
function export_settings_callback() {
    $export_options = XUN::get_option( \'backup_demo\', \'export_options\', array() );
    $export_data = array();

    foreach ( $export_options as $option_type ) {
        switch ( $option_type ) {
            case \'theme_options\':
                $export_data[\'theme_options\'] = get_option( \'my_theme_options\', array() );
                break;
            case \'plugin_options\':
                $export_data[\'plugin_options\'] = get_option( \'myplugin_settings\', array() );
                break;
            // 添加更多选项类型...
        }
    }

    $filename = \'settings_backup_\' . date( \'Y-m-d_H-i-s\' ) . \'.json\';

    header( \'Content-Type: application/json\' );
    header( \'Content-Disposition: attachment; filename="\' . $filename . \'"\' );

    echo json_encode( $export_data, JSON_PRETTY_PRINT );
    exit;
}

// 导入回调函数
function import_settings_callback() {
    $import_file = XUN::get_option( \'backup_demo\', \'import_file\' );
    $import_mode = XUN::get_option( \'backup_demo\', \'import_mode\', \'merge\' );

    if ( empty( $import_file ) ) {
        wp_send_json_error( \'请先选择备份文件\' );
    }

    $file_path = get_attached_file( $import_file );
    $file_content = file_get_contents( $file_path );
    $import_data = json_decode( $file_content, true );

    if ( json_last_error() !== JSON_ERROR_NONE ) {
        wp_send_json_error( \'无效的JSON文件\' );
    }

    foreach ( $import_data as $option_name => $option_data ) {
        if ( $import_mode === \'overwrite\' ) {
            update_option( $option_name, $option_data );
        } elseif ( $import_mode === \'merge\' ) {
            $existing_data = get_option( $option_name, array() );
            $merged_data = array_merge( $existing_data, $option_data );
            update_option( $option_name, $merged_data );
        }
    }

    wp_send_json_success( \'设置导入成功\' );
}'
            ],
            'custom_validation' => [
                'title' => '自定义验证规则',
                'description' => '为字段添加自定义的验证逻辑',
                'difficulty' => '高级',
                'time' => '20分钟',
                'features' => ['数据验证', '错误处理', '安全性', '用户反馈'],
                'code' => '<?php
// 自定义验证规则示例

XUN::createOptions( \'validation_demo\', array(
    \'menu_title\'      => \'验证演示\',
    \'menu_slug\'       => \'validation-demo\',
    \'framework_title\' => \'自定义验证演示\',
) );

XUN::createSection( \'validation_demo\', array(
    \'title\'  => \'验证示例\',
    \'fields\' => array(
        array(
            \'id\'          => \'username\',
            \'type\'        => \'text\',
            \'title\'       => \'用户名\',
            \'desc\'        => \'只能包含字母、数字和下划线，长度3-20字符\',
            \'validate\'    => \'custom_username_validation\',
        ),
        array(
            \'id\'          => \'email_list\',
            \'type\'        => \'textarea\',
            \'title\'       => \'邮箱列表\',
            \'desc\'        => \'每行一个邮箱地址\',
            \'validate\'    => \'custom_email_list_validation\',
        ),
        array(
            \'id\'          => \'secure_token\',
            \'type\'        => \'text\',
            \'title\'       => \'安全令牌\',
            \'desc\'        => \'32位十六进制字符串\',
            \'validate\'    => \'custom_token_validation\',
        ),
    ),
) );

// 用户名验证
add_filter( \'xun_validate_custom_username_validation\', function( $value, $field ) {
    // 检查长度
    if ( strlen( $value ) < 3 || strlen( $value ) > 20 ) {
        return new WP_Error( \'invalid_length\', \'用户名长度必须在3-20字符之间\' );
    }

    // 检查字符
    if ( ! preg_match( \'/^[a-zA-Z0-9_]+$/\', $value ) ) {
        return new WP_Error( \'invalid_chars\', \'用户名只能包含字母、数字和下划线\' );
    }

    // 检查是否已存在
    if ( username_exists( $value ) ) {
        return new WP_Error( \'username_exists\', \'用户名已存在\' );
    }

    return $value;
}, 10, 2 );

// 邮箱列表验证
add_filter( \'xun_validate_custom_email_list_validation\', function( $value, $field ) {
    $emails = array_filter( array_map( \'trim\', explode( "\\n", $value ) ) );
    $valid_emails = array();
    $errors = array();

    foreach ( $emails as $email ) {
        if ( is_email( $email ) ) {
            $valid_emails[] = $email;
        } else {
            $errors[] = "无效邮箱: {$email}";
        }
    }

    if ( ! empty( $errors ) ) {
        return new WP_Error( \'invalid_emails\', implode( \'<br>\', $errors ) );
    }

    return implode( "\\n", $valid_emails );
}, 10, 2 );

// 令牌验证
add_filter( \'xun_validate_custom_token_validation\', function( $value, $field ) {
    if ( ! preg_match( \'/^[a-fA-F0-9]{32}$/\', $value ) ) {
        return new WP_Error( \'invalid_token\', \'令牌必须是32位十六进制字符串\' );
    }

    return strtolower( $value );
}, 10, 2 );'
            ]
        ]
    ]
];

// 引入通用头部
include 'includes/header.php';
?>

        <!-- 主要内容区域 -->
        <main class="flex-1">
            <!-- Hero区域 -->
            <section class="relative overflow-hidden bg-gradient-to-br from-green-50 via-white to-blue-50 py-20">
                <!-- 背景装饰 -->
                <div class="absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 bg-gradient-to-br from-green-400/20 to-blue-400/20 rounded-full blur-3xl"></div>
                <div class="absolute bottom-0 left-0 -mb-20 -ml-20 w-80 h-80 bg-gradient-to-tr from-purple-400/20 to-green-400/20 rounded-full blur-3xl"></div>

                <div class="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center">
                        <h1 class="text-3xl lg:text-5xl font-bold text-gray-900 mb-6">
                            <span class="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                                示例代码
                            </span>
                        </h1>
                        
                        <p class="text-xl text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                            从<strong>基础配置</strong>到<strong>高级用法</strong>的完整代码示例，
                            帮助您快速掌握Xun Framework的强大功能。
                        </p>

                        <!-- 统计信息 -->
                        <div class="flex flex-wrap justify-center gap-6 mb-12">
                            <div class="bg-white rounded-xl px-6 py-4 shadow-sm border border-gray-200">
                                <div class="text-2xl font-bold text-green-600">8</div>
                                <div class="text-sm text-gray-600">完整示例</div>
                            </div>
                            <div class="bg-white rounded-xl px-6 py-4 shadow-sm border border-gray-200">
                                <div class="text-2xl font-bold text-blue-600">3</div>
                                <div class="text-sm text-gray-600">分类</div>
                            </div>
                            <div class="bg-white rounded-xl px-6 py-4 shadow-sm border border-gray-200">
                                <div class="text-2xl font-bold text-purple-600">100%</div>
                                <div class="text-sm text-gray-600">可运行代码</div>
                            </div>
                        </div>

                        <!-- 搜索框 -->
                        <div class="max-w-md mx-auto">
                            <div class="relative">
                                <input 
                                    type="text" 
                                    id="example-search"
                                    placeholder="搜索示例代码..."
                                    class="w-full px-4 py-3 pl-12 pr-4 text-gray-900 bg-white border border-gray-300 rounded-xl focus:ring-2 focus:ring-green-500 focus:border-transparent transition-all duration-300"
                                >
                                <div class="absolute inset-y-0 left-0 flex items-center pl-4">
                                    <svg class="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- 示例分类导航 -->
            <section class="py-12 bg-white border-b border-gray-200">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="text-center mb-8">
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">示例分类</h2>
                        <p class="text-gray-600 max-w-2xl mx-auto">
                            按难度和功能分类的完整代码示例，从入门到精通
                        </p>
                    </div>

                    <!-- 分类卡片 -->
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
                        <?php foreach ($example_categories as $key => $category): ?>
                        <div class="example-category-card bg-white rounded-2xl p-6 border border-gray-200 hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1" data-category="<?php echo $key; ?>">
                            <div class="flex items-center mb-4">
                                <div class="w-12 h-12 bg-<?php echo $category['color']; ?>-100 rounded-xl flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-<?php echo $category['color']; ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h3 class="text-lg font-semibold text-gray-900">
                                        <?php echo htmlspecialchars($category['title']); ?>
                                    </h3>
                                    <p class="text-sm text-gray-500">
                                        <?php echo count($category['examples']); ?> 个示例
                                    </p>
                                </div>
                            </div>
                            <p class="text-gray-600 mb-4">
                                <?php echo htmlspecialchars($category['description']); ?>
                            </p>
                            <a href="#<?php echo $key; ?>" class="inline-flex items-center text-<?php echo $category['color']; ?>-600 hover:text-<?php echo $category['color']; ?>-700 font-medium">
                                查看示例
                                <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </section>

            <!-- 示例详细内容 -->
            <section class="py-20 bg-gray-50">
                <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div class="space-y-16">
                        <?php foreach ($example_categories as $category_key => $category): ?>
                        <div id="<?php echo $category_key; ?>" class="example-section">
                            <!-- 分类标题 -->
                            <div class="flex items-center mb-12">
                                <div class="w-10 h-10 bg-<?php echo $category['color']; ?>-100 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-5 h-5 text-<?php echo $category['color']; ?>-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <div>
                                    <h2 class="text-3xl font-bold text-gray-900">
                                        <?php echo htmlspecialchars($category['title']); ?>
                                    </h2>
                                    <p class="text-gray-600 mt-1">
                                        <?php echo htmlspecialchars($category['description']); ?>
                                    </p>
                                </div>
                            </div>

                            <!-- 示例列表 -->
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <?php foreach ($category['examples'] as $example_key => $example): ?>
                                <div class="example-card bg-white rounded-2xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-lg transition-all duration-300" data-example="<?php echo $example_key; ?>">
                                    <!-- 示例头部 -->
                                    <div class="p-6 border-b border-gray-100">
                                        <div class="flex items-start justify-between mb-4">
                                            <div class="flex-1">
                                                <h3 class="text-xl font-bold text-gray-900 mb-2">
                                                    <?php echo htmlspecialchars($example['title']); ?>
                                                </h3>
                                                <p class="text-gray-600 mb-4">
                                                    <?php echo htmlspecialchars($example['description']); ?>
                                                </p>
                                            </div>
                                            <button class="example-expand-btn text-gray-400 hover:text-gray-600 transition-colors ml-4">
                                                <svg class="w-5 h-5 transform transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                                </svg>
                                            </button>
                                        </div>

                                        <!-- 示例信息 -->
                                        <div class="flex flex-wrap gap-4">
                                            <div class="flex items-center text-sm">
                                                <svg class="w-4 h-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                                </svg>
                                                <span class="text-gray-600"><?php echo htmlspecialchars($example['difficulty']); ?></span>
                                            </div>
                                            <div class="flex items-center text-sm">
                                                <svg class="w-4 h-4 text-gray-400 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                </svg>
                                                <span class="text-gray-600"><?php echo htmlspecialchars($example['time']); ?></span>
                                            </div>
                                        </div>

                                        <!-- 特性标签 -->
                                        <div class="flex flex-wrap gap-2 mt-4">
                                            <?php foreach ($example['features'] as $feature): ?>
                                            <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-<?php echo $category['color']; ?>-50 text-<?php echo $category['color']; ?>-700">
                                                <?php echo htmlspecialchars($feature); ?>
                                            </span>
                                            <?php endforeach; ?>
                                        </div>
                                    </div>

                                    <!-- 代码内容 -->
                                    <div class="example-content hidden">
                                        <div class="p-6">
                                            <div class="bg-gray-900 rounded-xl overflow-hidden">
                                                <div class="flex items-center justify-between p-4 border-b border-gray-700">
                                                    <div class="flex space-x-2">
                                                        <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                                        <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                                        <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                                                    </div>
                                                    <div class="flex items-center space-x-4">
                                                        <span class="text-gray-400 text-sm">functions.php</span>
                                                        <button class="copy-button text-gray-400 hover:text-white text-sm transition-colors" data-copy-target="code-<?php echo $category_key; ?>-<?php echo $example_key; ?>">
                                                            <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                                            </svg>
                                                            复制代码
                                                        </button>
                                                    </div>
                                                </div>
                                                <div class="p-4">
                                                    <pre id="code-<?php echo $category_key; ?>-<?php echo $example_key; ?>" class="text-sm overflow-x-auto"><code class="language-php"><?php echo htmlspecialchars($example['code']); ?></code></pre>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- 使用说明 -->
                                        <div class="px-6 pb-6">
                                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                                <div class="flex items-start">
                                                    <svg class="w-5 h-5 text-blue-600 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                    </svg>
                                                    <div>
                                                        <h4 class="text-sm font-semibold text-blue-900 mb-1">使用说明</h4>
                                                        <p class="text-sm text-blue-800">
                                                            将上述代码复制到您的主题 <code class="bg-blue-100 px-1 rounded">functions.php</code> 文件中，
                                                            然后在WordPress后台即可看到新的选项页面。
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <?php endforeach; ?>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
            </section>
        </main>

    <!-- JavaScript交互功能 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Examples page loaded, initializing...');

            // 搜索功能
            const searchInput = document.getElementById('example-search');
            const exampleCards = document.querySelectorAll('.example-card');
            const exampleSections = document.querySelectorAll('.example-section');

            searchInput.addEventListener('input', function() {
                const searchTerm = this.value.toLowerCase();

                exampleCards.forEach(card => {
                    const title = card.querySelector('h3').textContent.toLowerCase();
                    const description = card.querySelector('p').textContent.toLowerCase();
                    const features = Array.from(card.querySelectorAll('.inline-flex')).map(el => el.textContent.toLowerCase()).join(' ');

                    if (title.includes(searchTerm) || description.includes(searchTerm) || features.includes(searchTerm)) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });

                // 隐藏空的分类
                exampleSections.forEach(section => {
                    const visibleCards = section.querySelectorAll('.example-card[style="display: block"], .example-card:not([style*="display: none"])');
                    if (visibleCards.length === 0 && searchTerm !== '') {
                        section.style.display = 'none';
                    } else {
                        section.style.display = 'block';
                    }
                });
            });

            // 示例展开/收起功能
            const expandButtons = document.querySelectorAll('.example-expand-btn');

            expandButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const card = this.closest('.example-card');
                    const content = card.querySelector('.example-content');
                    const icon = this.querySelector('svg');

                    if (content.classList.contains('hidden')) {
                        content.classList.remove('hidden');
                        icon.style.transform = 'rotate(180deg)';

                        // 滚动到卡片顶部
                        setTimeout(() => {
                            card.scrollIntoView({
                                behavior: 'smooth',
                                block: 'start'
                            });
                        }, 100);
                    } else {
                        content.classList.add('hidden');
                        icon.style.transform = 'rotate(0deg)';
                    }
                });
            });

            // 代码复制功能
            document.querySelectorAll('.copy-button').forEach(button => {
                button.addEventListener('click', function() {
                    const targetId = this.getAttribute('data-copy-target');
                    const codeElement = document.getElementById(targetId);
                    const text = codeElement.textContent;

                    navigator.clipboard.writeText(text).then(() => {
                        const originalHTML = this.innerHTML;
                        this.innerHTML = `
                            <svg class="w-4 h-4 mr-1 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            已复制
                        `;
                        this.classList.add('text-green-400');

                        setTimeout(() => {
                            this.innerHTML = originalHTML;
                            this.classList.remove('text-green-400');
                        }, 2000);
                    }).catch(err => {
                        console.error('复制失败:', err);
                        // 降级方案：选择文本
                        const range = document.createRange();
                        range.selectNode(codeElement);
                        window.getSelection().removeAllRanges();
                        window.getSelection().addRange(range);
                    });
                });
            });

            // 平滑滚动到锚点
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // 分类卡片点击效果
            document.querySelectorAll('.example-category-card').forEach(card => {
                card.addEventListener('click', function(e) {
                    if (e.target.tagName !== 'A') {
                        const link = this.querySelector('a[href^="#"]');
                        if (link) {
                            link.click();
                        }
                    }
                });
            });

            // 键盘快捷键支持
            document.addEventListener('keydown', function(e) {
                // Ctrl/Cmd + K 聚焦搜索框
                if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                    e.preventDefault();
                    searchInput.focus();
                }

                // ESC 清空搜索
                if (e.key === 'Escape' && document.activeElement === searchInput) {
                    searchInput.value = '';
                    searchInput.dispatchEvent(new Event('input'));
                }
            });

            // 添加搜索快捷键提示
            const searchContainer = searchInput.parentElement;
            const shortcutHint = document.createElement('div');
            shortcutHint.className = 'absolute right-3 top-1/2 transform -translate-y-1/2 text-xs text-gray-400';
            shortcutHint.innerHTML = '<kbd class="px-1 py-0.5 bg-gray-100 rounded text-xs">Ctrl+K</kbd>';
            searchContainer.appendChild(shortcutHint);

            // 隐藏/显示快捷键提示
            searchInput.addEventListener('focus', () => shortcutHint.style.display = 'none');
            searchInput.addEventListener('blur', () => {
                if (!searchInput.value) {
                    shortcutHint.style.display = 'block';
                }
            });
        });
    </script>

<?php
// 引入通用底部
include 'includes/footer.php';
?>
