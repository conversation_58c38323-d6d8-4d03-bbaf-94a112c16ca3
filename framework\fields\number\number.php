<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework 数字输入字段类型
 * 
 * 这个类实现了功能强大的数字输入字段，提供比Codestar Framework更优秀的用户体验。
 * 支持整数和小数输入、数值范围限制、步长控制、格式化显示、增减按钮等高级功能。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Field_number' ) ) {
    
    /**
     * XUN_Field_number 数字输入字段类
     * 
     * 提供现代化数字输入字段的完整功能，包括：
     * - 整数和小数输入支持
     * - 最小值/最大值限制
     * - 步长控制（step）
     * - 数字格式化显示
     * - 增减按钮（spinner controls）
     * - 实时验证和错误提示
     * - 键盘快捷键支持（上下箭头调整数值）
     * - 无障碍访问优化
     * - 多种输入模式（基础、货币、百分比等）
     * - 单位显示支持（px、%、em等）
     * - 数值范围指示器
     * - 自动格式化和千分位分隔符
     * 
     * @since 1.0
     */
    class XUN_Field_number extends XUN_Fields {
        
        /**
         * 构造函数
         * 
         * 初始化数字字段实例。
         * 
         * @since 1.0
         * 
         * @param array  $field  字段配置数组
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }
        
        /**
         * 渲染数字输入字段
         *
         * 输出功能完整的数字输入字段HTML代码，包含所有高级功能。
         *
         * @since 1.0
         */
        public function render() {
            
            // 解析字段配置，设置默认值
            $args = wp_parse_args( $this->field, array(
                'min'              => '',           // 最小值
                'max'              => '',           // 最大值
                'step'             => '1',          // 步长
                'unit'             => '',           // 单位
                'unit_position'    => 'right',      // 单位位置：left/right
                'mode'             => 'basic',      // 输入模式：basic/currency/percentage
                'precision'        => 0,           // 小数位数
                'thousand_sep'     => false,       // 是否显示千分位分隔符
                'spinner'          => true,        // 是否显示增减按钮
                'range_indicator'  => false,       // 是否显示范围指示器
                'format_display'   => true,        // 是否格式化显示
                'width'            => 'auto',       // 字段宽度：auto/small/medium/large/full
                'placeholder'      => '',          // 占位符文本
                'readonly'         => false,       // 是否只读
                'disabled'         => false,       // 是否禁用
                'required'         => false,       // 是否必填
                'validation'       => true,        // 是否启用实时验证
                'keyboard_nav'     => true,        // 是否启用键盘导航
                'auto_select'      => false,       // 聚焦时是否自动选中
                'currency_symbol'  => '$',         // 货币符号
                'currency_position' => 'left',     // 货币符号位置
            ) );
            
            // 处理字段值
            $value = $this->value;
            if ( $value === '' || $value === null ) {
                $value = isset( $this->field['default'] ) ? $this->field['default'] : '';
            }
            
            // 生成唯一ID
            $field_id = 'xun-number-' . uniqid();
            
            // 开始输出字段HTML
            echo $this->field_before();
            
            // 字段容器
            echo '<div class="xun-number-field-container relative" data-field-id="' . esc_attr( $field_id ) . '">';
            
            // 主输入容器 - TailwindCSS v4标准输入框样式
            $container_classes = array(
                'xun-number-input-container',
                'relative',
                'inline-flex',
                'items-center',
                'bg-white',
                'rounded-md',
                'outline-1',
                '-outline-offset-1',
                'outline-gray-300',
                'focus-within:outline-2',
                'focus-within:-outline-offset-2',
                'focus-within:outline-indigo-600'
            );
            
            // 根据宽度设置添加相应类
            switch ( $args['width'] ) {
                case 'small':
                    $container_classes[] = 'w-28'; // 增加到7rem，约112px，更适合带单位的数字
                    break;
                case 'medium':
                    $container_classes[] = 'w-32';
                    break;
                case 'large':
                    $container_classes[] = 'w-48';
                    break;
                case 'full':
                    $container_classes[] = 'w-full';
                    break;
                default:
                    $container_classes[] = 'w-auto min-w-28'; // 默认最小宽度也增加
                    break;
            }
            
            // 如果禁用，添加禁用样式
            if ( $args['disabled'] ) {
                $container_classes[] = 'bg-gray-50 border-gray-200 cursor-not-allowed opacity-60';
            }
            
            echo '<div class="' . esc_attr( implode( ' ', $container_classes ) ) . '">';
            
            // 左侧单位或货币符号
            if ( ( $args['unit'] && $args['unit_position'] === 'left' ) || 
                 ( $args['mode'] === 'currency' && $args['currency_position'] === 'left' ) ) {
                $symbol = $args['mode'] === 'currency' ? $args['currency_symbol'] : $args['unit'];
                echo '<span class="xun-number-prefix flex items-center px-3 h-full text-sm text-gray-500 rounded-l-md select-none">';
                echo esc_html( $symbol );
                echo '</span>';
            }
            
            // 主输入框
            $input_classes = array(
                'xun-number-input',
                'flex-1',
                'min-w-0',
                'px-3',
                'py-2',
                'text-sm',
                'border-0',
                'bg-transparent',
                'focus:outline-none',
                'focus:ring-0',
                'appearance-none' // 确保隐藏浏览器默认spinner
            );
            
            // 根据单位位置调整圆角
            if ( $args['unit'] && $args['unit_position'] === 'left' ) {
                $input_classes[] = 'rounded-r-md';
            } elseif ( $args['unit'] && $args['unit_position'] === 'right' ) {
                $input_classes[] = 'rounded-l-md';
            } else {
                $input_classes[] = 'rounded-md';
            }
            
            if ( $args['disabled'] ) {
                $input_classes[] = 'cursor-not-allowed text-gray-400';
            }

            // 如果有spinner按钮，给输入框添加右边距
            if ( $args['spinner'] && ! $args['disabled'] && ! $args['readonly'] ) {
                $has_right_unit = ( $args['unit'] && $args['unit_position'] === 'right' ) ||
                                  ( $args['mode'] === 'currency' && $args['currency_position'] === 'right' );

                if ( $has_right_unit ) {
                    // 有右侧单位时，spinner在输入框内，需要更多右边距
                    $input_classes[] = 'pr-8';
                } else {
                    // 没有右侧单位时，正常的右边距
                    $input_classes[] = 'pr-8';
                }
            }
            
            // 构建输入框属性
            $input_attributes = array(
                'type'         => 'number',
                'id'           => $field_id,
                'name'         => $this->field_name(),
                'value'        => esc_attr( $value ),
                'class'        => implode( ' ', $input_classes ),
                'data-mode'    => esc_attr( $args['mode'] ),
                'data-precision' => esc_attr( $args['precision'] ),
                'data-thousand-sep' => $args['thousand_sep'] ? 'true' : 'false',
                'data-format-display' => $args['format_display'] ? 'true' : 'false',
                'data-validation' => $args['validation'] ? 'true' : 'false',
                'data-keyboard-nav' => $args['keyboard_nav'] ? 'true' : 'false',
                'data-auto-select' => $args['auto_select'] ? 'true' : 'false',
            );
            
            // 添加数值限制属性
            if ( $args['min'] !== '' ) {
                $input_attributes['min'] = esc_attr( $args['min'] );
                $input_attributes['data-min'] = esc_attr( $args['min'] );
            }
            
            if ( $args['max'] !== '' ) {
                $input_attributes['max'] = esc_attr( $args['max'] );
                $input_attributes['data-max'] = esc_attr( $args['max'] );
            }
            
            if ( $args['step'] !== '' ) {
                $input_attributes['step'] = esc_attr( $args['step'] );
                $input_attributes['data-step'] = esc_attr( $args['step'] );
            }
            
            // 添加其他属性
            if ( $args['placeholder'] ) {
                $input_attributes['placeholder'] = esc_attr( $args['placeholder'] );
            }
            
            if ( $args['readonly'] ) {
                $input_attributes['readonly'] = 'readonly';
            }
            
            if ( $args['disabled'] ) {
                $input_attributes['disabled'] = 'disabled';
            }
            
            if ( $args['required'] ) {
                $input_attributes['required'] = 'required';
                $input_attributes['aria-required'] = 'true';
            }
            
            // 无障碍访问属性
            $input_attributes['aria-label'] = isset( $this->field['title'] ) ? esc_attr( $this->field['title'] ) : '数字输入';
            
            if ( $args['min'] !== '' || $args['max'] !== '' ) {
                $range_desc = '';
                if ( $args['min'] !== '' && $args['max'] !== '' ) {
                    $range_desc = sprintf( '数值范围：%s 到 %s', $args['min'], $args['max'] );
                } elseif ( $args['min'] !== '' ) {
                    $range_desc = sprintf( '最小值：%s', $args['min'] );
                } elseif ( $args['max'] !== '' ) {
                    $range_desc = sprintf( '最大值：%s', $args['max'] );
                }
                $input_attributes['aria-describedby'] = $field_id . '-range-desc';
            }
            
            // 输出输入框
            echo '<input';
            foreach ( $input_attributes as $attr => $attr_value ) {
                echo ' ' . esc_attr( $attr ) . '="' . esc_attr( $attr_value ) . '"';
            }
            echo ' />';
            
            // 右侧单位或货币符号
            if ( ( $args['unit'] && $args['unit_position'] === 'right' ) ||
                 ( $args['mode'] === 'currency' && $args['currency_position'] === 'right' ) ) {
                $symbol = $args['mode'] === 'currency' ? $args['currency_symbol'] : $args['unit'];

                // 如果有spinner按钮，需要调整单位的位置和样式
                $suffix_classes = array(
                    'xun-number-suffix',
                    'flex',
                    'items-center',
                    'px-3',
                    'h-full', // 使用h-full确保与输入框高度完全一致
                    'text-sm',
                    'text-gray-500',
                    'select-none'
                );

                // 单位始终保持右圆角，因为spinner现在在输入框内
                $suffix_classes[] = 'rounded-r-md';

                echo '<span class="' . esc_attr( implode( ' ', $suffix_classes ) ) . '">';
                echo esc_html( $symbol );
                echo '</span>';
            }
            
            // 增减按钮（spinner controls）
            if ( $args['spinner'] && ! $args['disabled'] && ! $args['readonly'] ) {
                // 检查是否有右侧单位，调整spinner位置
                $has_right_unit = ( $args['unit'] && $args['unit_position'] === 'right' ) ||
                                  ( $args['mode'] === 'currency' && $args['currency_position'] === 'right' );

                if ( $has_right_unit ) {
                    // 有右侧单位时，spinner定位在输入框内，单位左侧
                    echo '<div class="xun-number-spinner absolute right-12 top-1/2 transform -translate-y-1/2 flex flex-col">';
                } else {
                    // 没有右侧单位时，spinner定位在输入框右侧
                    echo '<div class="xun-number-spinner absolute right-0 top-1/2 transform -translate-y-1/2 flex flex-col mr-1">';
                }
                
                // 增加按钮
                echo '<button type="button" class="xun-number-increment w-5 h-3 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-t transition-colors duration-150 text-xs" aria-label="增加数值">';
                echo '<svg class="w-2 h-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path>';
                echo '</svg>';
                echo '</button>';

                // 减少按钮
                echo '<button type="button" class="xun-number-decrement w-5 h-3 flex items-center justify-center text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-b transition-colors duration-150 text-xs" aria-label="减少数值">';
                echo '<svg class="w-2 h-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>';
                echo '</svg>';
                echo '</button>';
                
                echo '</div>';
            }
            
            echo '</div>'; // 结束主输入容器

            // 可拖动的范围滑块
            if ( $args['range_indicator'] && $args['min'] !== '' && $args['max'] !== '' ) {
                $slider_id = 'xun-range-slider-' . uniqid();

                echo '<div class="xun-number-range-slider mt-3" data-slider-id="' . esc_attr( $slider_id ) . '">';

                // 范围标签
                echo '<div class="flex justify-between text-xs text-gray-500 mb-2">';
                echo '<span class="font-medium">' . esc_html( $args['min'] ) . '</span>';
                echo '<span class="text-center text-gray-600">拖动调整数值</span>';
                echo '<span class="font-medium">' . esc_html( $args['max'] ) . '</span>';
                echo '</div>';

                // 滑块容器
                echo '<div class="relative w-full h-6 flex items-center">';

                // 滑块轨道
                echo '<div class="xun-slider-track absolute w-full h-2 bg-gray-200 rounded-full cursor-pointer">';

                // 已填充部分
                echo '<div class="xun-slider-fill absolute h-2 bg-blue-500 rounded-full transition-all duration-150" style="width: 0%"></div>';

                // 滑块手柄
                echo '<div class="xun-slider-handle absolute w-5 h-5 bg-white border-2 border-blue-500 rounded-full shadow-md cursor-grab active:cursor-grabbing transform -translate-x-1/2 -translate-y-1/2 transition-all duration-150 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50" ';
                echo 'style="left: 0%; top: 50%;" ';
                echo 'tabindex="0" ';
                echo 'role="slider" ';
                echo 'aria-valuemin="' . esc_attr( $args['min'] ) . '" ';
                echo 'aria-valuemax="' . esc_attr( $args['max'] ) . '" ';
                echo 'aria-valuenow="' . esc_attr( $value ) . '" ';
                echo 'aria-label="拖动调整数值">';
                echo '</div>';

                echo '</div>'; // 结束轨道
                echo '</div>'; // 结束滑块容器

                // 当前值显示
                echo '<div class="text-center mt-1">';
                echo '<span class="xun-slider-value text-sm font-medium text-gray-700">' . esc_html( $value ) . '</span>';
                if ( $args['unit'] ) {
                    echo '<span class="text-sm text-gray-500 ml-1">' . esc_html( $args['unit'] ) . '</span>';
                }
                echo '</div>';

                echo '</div>'; // 结束范围滑块
            }

            // 验证错误消息容器
            echo '<div class="xun-number-error-message mt-1 text-sm text-red-600 hidden" role="alert" aria-live="polite"></div>';

            // 范围描述（用于无障碍访问）
            if ( $args['min'] !== '' || $args['max'] !== '' ) {
                echo '<div id="' . esc_attr( $field_id ) . '-range-desc" class="sr-only">';
                if ( $args['min'] !== '' && $args['max'] !== '' ) {
                    echo sprintf( '数值范围：%s 到 %s', esc_html( $args['min'] ), esc_html( $args['max'] ) );
                } elseif ( $args['min'] !== '' ) {
                    echo sprintf( '最小值：%s', esc_html( $args['min'] ) );
                } elseif ( $args['max'] !== '' ) {
                    echo sprintf( '最大值：%s', esc_html( $args['max'] ) );
                }
                echo '</div>';
            }

            echo '</div>'; // 结束字段容器

            echo $this->field_after();
        }

        /**
         * 加载字段所需的JavaScript和CSS资源
         *
         * 在需要时加载number字段的前端资源。
         *
         * @since 1.0
         */
        public function enqueue() {

            // 加载number字段的JavaScript文件
            if ( ! wp_script_is( 'xun-number-field', 'enqueued' ) ) {
                wp_enqueue_script(
                    'xun-number-field',
                    XUN_Setup::$url . '/assets/js/fields/number.js',
                    array( 'jquery' ),
                    XUN_Setup::$version,
                    true
                );

                // 本地化脚本变量
                wp_localize_script( 'xun-number-field', 'xunNumberField', array(
                    'i18n' => array(
                        'invalid_number'    => __( '请输入有效的数字', 'xun' ),
                        'out_of_range'      => __( '数值超出允许范围', 'xun' ),
                        'below_minimum'     => __( '数值不能小于 %s', 'xun' ),
                        'above_maximum'     => __( '数值不能大于 %s', 'xun' ),
                        'required_field'    => __( '此字段为必填项', 'xun' ),
                        'invalid_step'      => __( '数值必须是 %s 的倍数', 'xun' ),
                        'decimal_places'    => __( '小数位数不能超过 %d 位', 'xun' ),
                    ),
                    'settings' => array(
                        'thousand_separator' => ',',
                        'decimal_separator'  => '.',
                        'currency_symbol'    => '$',
                        'percentage_symbol'  => '%',
                    ),
                ) );
            }
        }

        /**
         * 验证和清理字段值
         *
         * 对用户输入的数字进行验证和清理，确保数据安全性。
         *
         * @since 1.0
         *
         * @param mixed $value 要验证的值
         *
         * @return mixed 验证后的值
         */
        public function validate( $value ) {

            // 如果值为空，检查是否必填
            if ( $value === '' || $value === null ) {
                if ( ! empty( $this->field['required'] ) ) {
                    return new WP_Error( 'required', __( '此字段为必填项', 'xun' ) );
                }
                return '';
            }

            // 清理输入值
            $value = sanitize_text_field( $value );

            // 移除千分位分隔符
            $value = str_replace( ',', '', $value );

            // 验证是否为有效数字
            if ( ! is_numeric( $value ) ) {
                return new WP_Error( 'invalid_number', __( '请输入有效的数字', 'xun' ) );
            }

            // 转换为数字
            $numeric_value = floatval( $value );

            // 检查最小值限制
            if ( isset( $this->field['min'] ) && $this->field['min'] !== '' ) {
                $min = floatval( $this->field['min'] );
                if ( $numeric_value < $min ) {
                    return new WP_Error( 'below_minimum', sprintf( __( '数值不能小于 %s', 'xun' ), $min ) );
                }
            }

            // 检查最大值限制
            if ( isset( $this->field['max'] ) && $this->field['max'] !== '' ) {
                $max = floatval( $this->field['max'] );
                if ( $numeric_value > $max ) {
                    return new WP_Error( 'above_maximum', sprintf( __( '数值不能大于 %s', 'xun' ), $max ) );
                }
            }

            // 检查步长限制
            if ( isset( $this->field['step'] ) && $this->field['step'] !== '' && $this->field['step'] !== 'any' ) {
                $step = floatval( $this->field['step'] );
                $min = isset( $this->field['min'] ) && $this->field['min'] !== '' ? floatval( $this->field['min'] ) : 0;

                if ( $step > 0 ) {
                    $remainder = fmod( $numeric_value - $min, $step );
                    if ( abs( $remainder ) > 0.0001 ) { // 允许浮点数精度误差
                        return new WP_Error( 'invalid_step', sprintf( __( '数值必须是 %s 的倍数', 'xun' ), $step ) );
                    }
                }
            }

            // 检查小数位数限制
            if ( isset( $this->field['precision'] ) && $this->field['precision'] >= 0 ) {
                $precision = intval( $this->field['precision'] );
                $decimal_places = strlen( substr( strrchr( $value, '.' ), 1 ) );

                if ( $decimal_places > $precision ) {
                    return new WP_Error( 'decimal_places', sprintf( __( '小数位数不能超过 %d 位', 'xun' ), $precision ) );
                }

                // 格式化到指定精度
                $numeric_value = round( $numeric_value, $precision );
            }

            return $numeric_value;
        }

        /**
         * 格式化数字显示
         *
         * 根据字段配置格式化数字的显示方式。
         *
         * @since 1.0
         *
         * @param mixed $value 要格式化的值
         *
         * @return string 格式化后的字符串
         */
        public function format_value( $value ) {

            if ( $value === '' || $value === null || ! is_numeric( $value ) ) {
                return '';
            }

            $numeric_value = floatval( $value );
            $args = wp_parse_args( $this->field, array(
                'precision'     => 0,
                'thousand_sep'  => false,
                'mode'          => 'basic',
                'unit'          => '',
                'currency_symbol' => '$',
            ) );

            // 格式化到指定精度
            $formatted = number_format( $numeric_value, $args['precision'], '.', $args['thousand_sep'] ? ',' : '' );

            // 根据模式添加符号
            switch ( $args['mode'] ) {
                case 'currency':
                    $formatted = $args['currency_symbol'] . $formatted;
                    break;
                case 'percentage':
                    $formatted = $formatted . '%';
                    break;
                default:
                    if ( $args['unit'] ) {
                        $formatted = $formatted . $args['unit'];
                    }
                    break;
            }

            return $formatted;
        }
    }
}
