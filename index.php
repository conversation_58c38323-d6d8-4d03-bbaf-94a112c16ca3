<?php
/**
 * Xun Framework 文档系统 - 完整首页
 *
 * 这是文档系统的入口文件，展示框架特性和快速导航。
 * 采用现代化设计，突出框架的主要优势。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

// 页面配置
$header_config = [
    'site_title' => 'Xun Framework',
    'site_description' => '现代化的WordPress选项框架',
    'page_title' => '',
    'page_description' => '基于PHP 8.1+和TailwindCSS 4.x构建的现代化WordPress选项框架，提供23种字段类型，支持响应式设计',
    'keywords' => 'WordPress, Framework, Options, PHP, TailwindCSS, 选项框架, 主题开发',
    'current_page' => 'home'
];

$footer_config = [
    'site_title' => 'Xun Framework',
    'site_description' => '现代化的WordPress选项框架',
    'version' => '1.0.0',
    'total_fields' => 23,
    'show_stats' => true
];

// 基础配置
$version = '1.0.0';

// 字段类型配置
$field_types = [
    'basic' => [
        'title' => '基础字段',
        'description' => '常用的基础输入字段类型',
        'fields' => ['text', 'textarea', 'number', 'date', 'time'],
        'color' => 'blue'
    ],
    'selection' => [
        'title' => '选择字段',
        'description' => '各种选择和切换类型的字段',
        'fields' => ['select', 'radio', 'checkbox', 'switch'],
        'color' => 'green'
    ],
    'media' => [
        'title' => '媒体字段',
        'description' => '图片、文件和媒体相关的字段',
        'fields' => ['media', 'gallery', 'icon'],
        'color' => 'purple'
    ],
    'design' => [
        'title' => '设计字段',
        'description' => '颜色、样式和设计相关的字段',
        'fields' => ['color', 'background', 'border', 'palette'],
        'color' => 'pink'
    ],
    'interactive' => [
        'title' => '交互字段',
        'description' => '滑块、按钮等交互式字段',
        'fields' => ['slider', 'button', 'accordion'],
        'color' => 'indigo'
    ],
    'advanced' => [
        'title' => '高级字段',
        'description' => '复杂的高级功能字段',
        'fields' => ['code', 'repeater', 'sortable', 'sorter'],
        'color' => 'gray'
    ]
];

$total_fields = 0;
foreach ($field_types as $category) {
    $total_fields += count($category['fields']);
}

// 特性列表
$features = [
    [
        'title' => '现代化设计',
        'description' => '基于 TailwindCSS 4.x 构建，提供美观的用户界面和完全响应式设计，适配所有设备。',
        'icon' => 'sparkles',
        'color' => 'blue'
    ],
    [
        'title' => $total_fields . ' 种字段类型',
        'description' => '从基础的文本输入到复杂的重复器字段，涵盖所有常见的选项类型，满足各种开发需求。',
        'icon' => 'squares',
        'color' => 'purple'
    ],
    [
        'title' => '简单易用',
        'description' => '简洁的 API 设计，只需几行代码即可创建功能完整的选项页面，大大提高开发效率。',
        'icon' => 'bolt',
        'color' => 'green'
    ],
    [
        'title' => '极速性能',
        'description' => '优化的代码结构和懒加载机制，确保框架运行高效，不会影响网站性能。',
        'icon' => 'bolt',
        'color' => 'green'
    ],
    [
        'title' => '高度可扩展',
        'description' => '丰富的钩子和过滤器系统，支持自定义字段类型，轻松扩展框架功能。',
        'icon' => 'sparkles',
        'color' => 'blue'
    ],
    [
        'title' => '开源免费',
        'description' => 'MIT 开源许可证，完全免费使用，活跃的社区支持和持续的功能更新。',
        'icon' => 'heart',
        'color' => 'red'
    ]
];

// 快速开始步骤
$quick_start_steps = [
    [
        'step' => '1',
        'title' => '下载框架',
        'description' => '从 GitHub 下载最新版本，或通过 Composer 安装到你的项目中。',
        'code' => 'composer require xuntheme/xun-framework'
    ],
    [
        'step' => '2',
        'title' => '引入框架',
        'description' => '在主题的 functions.php 文件中引入框架，只需一行代码。',
        'code' => 'require_once get_template_directory() . \'/xun-framework/xun-framework.php\';'
    ],
    [
        'step' => '3',
        'title' => '创建选项',
        'description' => '使用简洁的 API 创建选项页面和字段，支持所有常见类型。',
        'code' => 'XUN::createOptions( \'my_options\', array( \'menu_title\' => \'主题设置\' ) );'
    ],
    [
        'step' => '4',
        'title' => '获取数据',
        'description' => '在主题中轻松获取和使用选项数据，实现动态内容展示。',
        'code' => '$value = XUN::get_option( \'my_options\', \'field_id\' );'
    ]
];

// 统计数据
$stats = [
    [
        'number' => $total_fields,
        'label' => '字段类型',
        'icon' => 'squares'
    ],
    [
        'number' => 'PHP 8.1+',
        'label' => '最低版本要求',
        'icon' => 'shield'
    ],
    [
        'number' => 'MIT',
        'label' => '开源许可证',
        'icon' => 'heart'
    ],
    [
        'number' => 'v' . $version,
        'label' => '当前版本',
        'icon' => 'star'
    ]
];

// 引入通用头部
include 'includes/header.php';
?>


        <!-- Hero区域 -->
        <section class="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-indigo-50 flex items-center" style="min-height: calc(100vh - 4rem);">
            <!-- 背景装饰 -->
            <div class="absolute top-0 right-0 -mt-20 -mr-20 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl"></div>
            <div class="absolute bottom-0 left-0 -mb-20 -ml-20 w-80 h-80 bg-gradient-to-tr from-green-400/20 to-blue-400/20 rounded-full blur-3xl"></div>

            <div class="relative w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center">
                    <!-- Logo和标题 -->
                    <div class="flex justify-center mb-8">
                        <div class="w-16 h-16 lg:w-20 lg:h-20 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-xl">
                            <span class="text-white font-bold text-xl lg:text-2xl">X</span>
                        </div>
                    </div>

                    <h1 class="text-3xl lg:text-5xl font-bold text-gray-900 mb-6">
                        <span class="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                            Xun Framework
                        </span>
                    </h1>

                    <p class="text-base lg:text-lg text-gray-600 mb-8 max-w-3xl mx-auto leading-relaxed">
                        现代化的 <strong class="text-blue-600">WordPress 选项框架</strong>，
                        基于 PHP 8.1+ 和 TailwindCSS 4.x 构建，
                        提供 <strong class="text-purple-600"><?php echo $total_fields; ?> 种字段类型</strong>
                    </p>

                    <!-- 特性标签 -->
                    <div class="flex flex-wrap justify-center gap-4 mb-16">
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-blue-100 text-blue-800">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                            </svg>
                            PHP 8.1+
                        </span>
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-purple-100 text-purple-800">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                            </svg>
                            TailwindCSS 4.x
                        </span>
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-green-100 text-green-800">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 18h.01M8 21h8a2 2 0 002-2V5a2 2 0 00-2-2H8a2 2 0 00-2 2v14a2 2 0 002 2z"></path>
                            </svg>
                            响应式设计
                        </span>
                        <span class="inline-flex items-center px-4 py-2 rounded-full text-sm font-medium bg-indigo-100 text-indigo-800">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                            开源免费
                        </span>
                    </div>

                    <!-- 行动按钮 -->
                    <div class="flex flex-col sm:flex-row gap-6 justify-center">
                        <a
                            href="#getting-started"
                            class="inline-flex items-center justify-center px-8 py-3 text-lg font-semibold text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-110"
                            style="transition: all 0.3s ease; color: white !important;"
                        >
                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                            快速开始
                        </a>
                        <a
                            href="https://github.com/xuntheme/xun-framework"
                            target="_blank"
                            rel="noopener noreferrer"
                            class="inline-flex items-center justify-center px-8 py-3 text-lg font-semibold text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-110"
                            style="transition: all 0.3s ease;"
                        >
                            <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                            查看源码
                        </a>
                    </div>
                </div>
            </div>

            <!-- 向下滚动指示器 -->
            <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
                <a href="#features" class="flex flex-col items-center text-gray-500 hover:text-blue-600 transition-colors">
                    <span class="text-sm mb-2">向下滚动</span>
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 14l-7 7m0 0l-7-7m7 7V3"></path>
                    </svg>
                </a>
            </div>
        </section>

        <!-- 核心特性 -->
        <section id="features" class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold text-gray-900 mb-4">
                        为什么选择 Xun Framework？
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        专为现代 WordPress 开发而设计，提供强大而灵活的选项管理解决方案
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php foreach ($features as $feature): ?>
                    <?php
                    // 定义颜色映射
                    $colors = [
                        'blue' => [
                            'bg' => 'background: linear-gradient(to bottom right, #eff6ff, #dbeafe);',
                            'border' => 'border-color: #bfdbfe;',
                            'icon' => 'background: linear-gradient(to bottom right, #3b82f6, #2563eb);'
                        ],
                        'purple' => [
                            'bg' => 'background: linear-gradient(to bottom right, #faf5ff, #f3e8ff);',
                            'border' => 'border-color: #e9d5ff;',
                            'icon' => 'background: linear-gradient(to bottom right, #a855f7, #9333ea);'
                        ],
                        'green' => [
                            'bg' => 'background: linear-gradient(to bottom right, #f0fdf4, #dcfce7);',
                            'border' => 'border-color: #bbf7d0;',
                            'icon' => 'background: linear-gradient(to bottom right, #22c55e, #16a34a);'
                        ],
                        'red' => [
                            'bg' => 'background: linear-gradient(to bottom right, #fef2f2, #fee2e2);',
                            'border' => 'border-color: #fecaca;',
                            'icon' => 'background: linear-gradient(to bottom right, #ef4444, #dc2626);'
                        ]
                    ];
                    $colorStyle = $colors[$feature['color']];
                    ?>
                    <div class="group p-8 rounded-2xl border hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2" style="<?php echo $colorStyle['bg'] . $colorStyle['border']; ?>">
                        <div class="w-12 h-12 rounded-xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300" style="<?php echo $colorStyle['icon']; ?>">
                            <?php if ($feature['icon'] === 'sparkles'): ?>
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                </svg>
                            <?php elseif ($feature['icon'] === 'squares'): ?>
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                            <?php elseif ($feature['icon'] === 'bolt'): ?>
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                </svg>

                            <?php elseif ($feature['icon'] === 'rocket'): ?>
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                                </svg>
                            <?php elseif ($feature['icon'] === 'cog'): ?>
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12,15.5A3.5,3.5 0 0,1 8.5,12A3.5,3.5 0 0,1 12,8.5A3.5,3.5 0 0,1 15.5,12A3.5,3.5 0 0,1 12,15.5M19.43,12.97C19.47,12.65 19.5,12.33 19.5,12C19.5,11.67 19.47,11.34 19.43,11L21.54,9.37C21.73,9.22 21.78,8.95 21.66,8.73L19.66,5.27C19.54,5.05 19.27,4.96 19.05,5.05L16.56,6.05C16.04,5.66 15.5,5.32 14.87,5.07L14.5,2.42C14.46,2.18 14.25,2 14,2H10C9.75,2 9.54,2.18 9.5,2.42L9.13,5.07C8.5,5.32 7.96,5.66 7.44,6.05L4.95,5.05C4.73,4.96 4.46,5.05 4.34,5.27L2.34,8.73C2.22,8.95 2.27,9.22 2.46,9.37L4.57,11C4.53,11.34 4.5,11.67 4.5,12C4.5,12.33 4.53,12.65 4.57,12.97L2.46,14.63C2.27,14.78 2.22,15.05 2.34,15.27L4.34,18.73C4.46,18.95 4.73,19.03 4.95,18.95L7.44,17.94C7.96,18.34 8.5,18.68 9.13,18.93L9.5,21.58C9.54,21.82 9.75,22 10,22H14C14.25,22 14.46,21.82 14.5,21.58L14.87,18.93C15.5,18.67 16.04,18.34 16.56,17.94L19.05,18.95C19.27,19.03 19.54,18.95 19.66,18.73L21.66,15.27C21.78,15.05 21.73,14.78 21.54,14.63L19.43,12.97Z"/>
                                </svg>
                            <?php elseif ($feature['icon'] === 'heart'): ?>
                                <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                                </svg>
                            <?php else: ?>
                                <!-- 默认图标 -->
                                <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                                </svg>
                            <?php endif; ?>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-900 mb-4"><?php echo htmlspecialchars($feature['title']); ?></h3>
                        <p class="text-gray-600 leading-relaxed">
                            <?php echo htmlspecialchars($feature['description']); ?>
                        </p>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>

        <!-- 字段类型展示 -->
        <section id="field-types" class="py-20 bg-gray-50">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold text-gray-900 mb-4">
                        丰富的字段类型
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        <?php echo $total_fields; ?> 种精心设计的字段类型，满足各种选项配置需求
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php foreach ($field_types as $category_id => $category): ?>
                    <div class="group">
                        <div class="bg-white rounded-2xl border border-gray-200 p-6 hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-900">
                                    <?php echo htmlspecialchars($category['title']); ?>
                                </h3>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-<?php echo $category['color']; ?>-100 text-<?php echo $category['color']; ?>-800">
                                    <?php echo count($category['fields']); ?> 个
                                </span>
                            </div>

                            <p class="text-gray-600 text-sm mb-4">
                                <?php echo htmlspecialchars($category['description']); ?>
                            </p>

                            <div class="grid grid-cols-2 gap-2">
                                <?php foreach (array_slice($category['fields'], 0, 4) as $field): ?>
                                <div class="flex items-center space-x-2 p-2 bg-gray-50 rounded-lg">
                                    <div class="w-2 h-2 bg-<?php echo $category['color']; ?>-500 rounded-full"></div>
                                    <span class="text-xs text-gray-700 truncate">
                                        <?php echo ucfirst($field); ?>
                                    </span>
                                </div>
                                <?php endforeach; ?>

                                <?php if (count($category['fields']) > 4): ?>
                                <div class="col-span-2 text-center">
                                    <span class="text-xs text-gray-500">
                                        还有 <?php echo count($category['fields']) - 4; ?> 个字段...
                                    </span>
                                </div>
                                <?php endif; ?>
                            </div>

                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <a
                                    href="#"
                                    class="text-<?php echo $category['color']; ?>-600 hover:text-<?php echo $category['color']; ?>-700 text-sm font-medium group-hover:underline transition-colors"
                                >
                                    查看详细文档 →
                                </a>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>

                <div class="text-center mt-12">
                    <a
                        href="#"
                        class="inline-flex items-center px-8 py-4 text-lg font-semibold text-gray-700 bg-white hover:bg-gray-50 border border-gray-300 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-110"
                        style="transition: all 0.3s ease;"
                    >
                        探索所有字段类型
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </section>

        <!-- 快速开始 -->
        <section id="getting-started" class="py-20 bg-white">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold text-gray-900 mb-4">
                        5 分钟快速上手
                    </h2>
                    <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                        简单几步，即可创建功能完整的 WordPress 选项页面
                    </p>
                </div>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                    <!-- 步骤说明 -->
                    <div class="space-y-8">
                        <?php foreach ($quick_start_steps as $step): ?>
                        <div class="flex items-start space-x-4">
                            <div class="flex-shrink-0 w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                                <?php echo $step['step']; ?>
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold text-gray-900 mb-2">
                                    <?php echo htmlspecialchars($step['title']); ?>
                                </h3>
                                <p class="text-gray-600 mb-3">
                                    <?php echo htmlspecialchars($step['description']); ?>
                                </p>
                                <code class="text-sm bg-gray-100 px-3 py-1 rounded text-gray-800 font-mono">
                                    <?php echo htmlspecialchars($step['code']); ?>
                                </code>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>

                    <!-- 代码示例 -->
                    <div class="bg-gray-900 rounded-2xl p-6 overflow-hidden">
                        <div class="flex items-center justify-between mb-4">
                            <div class="flex space-x-2">
                                <div class="w-3 h-3 bg-red-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-yellow-500 rounded-full"></div>
                                <div class="w-3 h-3 bg-green-500 rounded-full"></div>
                            </div>
                            <span class="text-gray-400 text-sm">functions.php</span>
                        </div>
                        <pre class="text-sm overflow-x-auto"><code class="language-php"><?php echo htmlspecialchars('<?php
// 1. 引入框架
require_once get_template_directory() . \'/xun-framework/xun-framework.php\';

// 2. 创建选项页面
XUN::createOptions( \'my_theme_options\', array(
    \'menu_title\'      => \'主题设置\',
    \'menu_slug\'       => \'my-theme-options\',
    \'framework_title\' => \'我的主题设置\',
) );

// 3. 添加字段
XUN::createSection( \'my_theme_options\', array(
    \'title\'  => \'基本设置\',
    \'fields\' => array(
        array(
            \'id\'    => \'site_logo\',
            \'type\'  => \'media\',
            \'title\' => \'网站Logo\',
        ),
        array(
            \'id\'      => \'theme_color\',
            \'type\'    => \'color\',
            \'title\'   => \'主题颜色\',
            \'default\' => \'#2563eb\',
        ),
    ),
) );

// 4. 获取选项值
$logo = XUN::get_option( \'my_theme_options\', \'site_logo\' );
$color = XUN::get_option( \'my_theme_options\', \'theme_color\' );'); ?></code></pre>
                    </div>
                </div>

                <div class="text-center mt-12">
                    <a
                        href="https://github.com/xuntheme/xun-framework"
                        target="_blank"
                        rel="noopener noreferrer"
                        class="inline-flex items-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 rounded-xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:scale-110"
                        style="transition: all 0.3s ease; color: white !important;"
                    >
                        开始使用 Xun Framework
                        <svg class="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                        </svg>
                    </a>
                </div>
            </div>
        </section>

        <!-- 统计数据 -->
        <section id="stats" class="py-20 bg-gradient-to-br from-blue-600 to-purple-700">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="text-center mb-16">
                    <h2 class="text-4xl font-bold text-white mb-4">
                        框架数据一览
                    </h2>
                    <p class="text-xl text-blue-100 max-w-3xl mx-auto">
                        持续更新和完善的现代化 WordPress 选项框架
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <?php foreach ($stats as $stat): ?>
                    <div class="text-center">
                        <div class="w-16 h-16 bg-white/20 rounded-2xl flex items-center justify-center mx-auto mb-4">
                            <?php if ($stat['icon'] === 'squares'): ?>
                                <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                </svg>
                            <?php elseif ($stat['icon'] === 'shield'): ?>
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 2L2 7v10c0 5.55 3.84 9.74 9 11 5.16-1.26 9-5.45 9-11V7l-10-5z"/>
                                </svg>
                            <?php elseif ($stat['icon'] === 'heart'): ?>
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
                                </svg>
                            <?php elseif ($stat['icon'] === 'star'): ?>
                                <svg class="w-8 h-8 text-white" fill="currentColor" viewBox="0 0 24 24">
                                    <path d="M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z"/>
                                </svg>
                            <?php endif; ?>
                        </div>
                        <div class="text-4xl font-bold text-white mb-2"><?php echo htmlspecialchars($stat['number']); ?></div>
                        <div class="text-blue-100"><?php echo htmlspecialchars($stat['label']); ?></div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </section>

<?php
// 引入通用底部
include 'includes/footer.php';
?>
    </div>

    <!-- 平滑滚动和导航JavaScript -->
    <script>
        // 平滑滚动到锚点
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // 导航菜单活动状态
        function updateActiveNavItem() {
            const sections = ['features', 'field-types', 'getting-started', 'stats'];
            const navItems = document.querySelectorAll('.nav-item');

            let currentSection = '';

            sections.forEach(sectionId => {
                const section = document.getElementById(sectionId);
                if (section) {
                    const rect = section.getBoundingClientRect();
                    if (rect.top <= 100 && rect.bottom >= 100) {
                        currentSection = sectionId;
                    }
                }
            });

            navItems.forEach(item => {
                item.classList.remove('active');
                const href = item.getAttribute('href');
                if (href === '#' + currentSection) {
                    item.classList.add('active');
                }
            });
        }

        // 监听滚动事件
        window.addEventListener('scroll', updateActiveNavItem);

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', updateActiveNavItem);
    </script>

    <!-- Prism.js 代码高亮 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
</body>
</html>
