/**
 * XUN Radio Field JavaScript
 * 
 * 现代化的单选按钮字段交互逻辑
 * 支持搜索、动画效果、键盘导航和无障碍访问
 * 比CSF更好的用户体验
 * 
 * @since 1.0.0
 * @version 1.0.0
 */

(function($) {
    'use strict';

    /**
     * Radio Field 类
     */
    var XunRadioField = {
        
        /**
         * 初始化
         */
        init: function() {
            this.bindEvents();
            this.initializeFields();
        },

        /**
         * 绑定事件
         */
        bindEvents: function() {
            var self = this;

            // 单选按钮变化事件
            $(document).on('change', '.xun-radio-field .xun-radio-input', function() {
                self.handleRadioChange($(this));
            });

            // 搜索功能
            $(document).on('input', '.xun-radio-search', function() {
                self.handleSearch($(this));
            });

            // 选项点击事件（整个选项区域可点击）
            $(document).on('click', '.xun-radio-option', function(e) {
                // 如果点击的不是input本身，则触发input点击
                if (!$(e.target).is('input[type="radio"]')) {
                    var $input = $(this).find('.xun-radio-input');
                    if (!$input.is(':disabled')) {
                        $input.prop('checked', true).trigger('change');
                    }
                }
            });

            // 键盘导航
            $(document).on('keydown', '.xun-radio-input', function(e) {
                self.handleKeyboardNavigation(e, $(this));
            });

            // 搜索框增强
            $(document).on('focus', '.xun-radio-search', function() {
                $(this).addClass('ring-2 ring-blue-500 border-blue-500');
            });

            $(document).on('blur', '.xun-radio-search', function() {
                $(this).removeClass('ring-2 ring-blue-500 border-blue-500');
            });

            // 键盘快捷键支持
            $(document).on('keydown', function(e) {
                // Cmd/Ctrl + K 聚焦搜索框
                if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
                    e.preventDefault();
                    $('.xun-radio-search:visible').first().focus();
                }
            });
        },

        /**
         * 初始化所有字段
         */
        initializeFields: function() {
            $('.xun-radio-field').each(function() {
                var $field = $(this);
                XunRadioField.updateFieldState($field);
                XunRadioField.addAnimations($field);
            });
        },

        /**
         * 处理单选按钮变化
         */
        handleRadioChange: function($input) {
            var $field = $input.closest('.xun-radio-field');
            var $option = $input.closest('.xun-radio-option');
            var value = $input.val();

            // 更新视觉状态
            this.updateFieldState($field);
            
            // 添加选中动画
            this.addSelectionAnimation($option);
            
            // 触发自定义事件
            this.triggerChangeEvent($field, value);
        },

        /**
         * 处理搜索
         */
        handleSearch: function($searchInput) {
            var searchTerm = $searchInput.val().toLowerCase();
            var $field = $searchInput.closest('.xun-radio-field');
            var $options = $field.find('.xun-radio-option');
            var visibleCount = 0;

            $options.each(function() {
                var $option = $(this);
                var text = $option.find('label').text().toLowerCase();
                var description = $option.find('.text-gray-500').text().toLowerCase();
                
                if (text.includes(searchTerm) || description.includes(searchTerm)) {
                    $option.removeClass('hidden').addClass('animate-fadeIn');
                    visibleCount++;
                } else {
                    $option.addClass('hidden').removeClass('animate-fadeIn');
                }
            });

            // 更新搜索结果统计
            this.updateSearchResults($field, searchTerm, visibleCount, $options.length);
        },

        /**
         * 键盘导航
         */
        handleKeyboardNavigation: function(e, $input) {
            var $field = $input.closest('.xun-radio-field');
            var $visibleInputs = $field.find('.xun-radio-input:not(.hidden)').filter(':visible');
            var currentIndex = $visibleInputs.index($input);
            var $target = null;

            switch (e.which) {
                case 38: // 上箭头
                    e.preventDefault();
                    $target = currentIndex > 0 ? $visibleInputs.eq(currentIndex - 1) : $visibleInputs.last();
                    break;
                case 40: // 下箭头
                    e.preventDefault();
                    $target = currentIndex < $visibleInputs.length - 1 ? $visibleInputs.eq(currentIndex + 1) : $visibleInputs.first();
                    break;
                case 32: // 空格
                case 13: // 回车
                    e.preventDefault();
                    $input.prop('checked', true).trigger('change');
                    break;
            }

            if ($target) {
                $target.focus();
            }
        },

        /**
         * 更新字段状态
         */
        updateFieldState: function($field) {
            var color = $field.data('color') || 'blue';
            
            $field.find('.xun-radio-option').each(function() {
                var $option = $(this);
                var $input = $option.find('.xun-radio-input');
                var isChecked = $input.is(':checked');

                // 移除所有状态类
                $option.removeClass([
                    'bg-blue-50', 'bg-green-50', 'bg-purple-50', 'bg-red-50', 'bg-gray-50',
                    'border-blue-200', 'border-green-200', 'border-purple-200', 'border-red-200', 'border-gray-200',
                    'ring-1', 'ring-blue-500', 'ring-green-500', 'ring-purple-500', 'ring-red-500', 'ring-gray-500'
                ].join(' '));

                if (isChecked) {
                    // 应用选中状态样式
                    $option.addClass('bg-' + color + '-50 border-' + color + '-200 ring-1 ring-' + color + '-500');
                } else {
                    // 应用未选中状态样式
                    $option.addClass('border-gray-200');
                }
            });
        },

        /**
         * 添加动画效果
         */
        addAnimations: function($field) {
            // 为选项添加进入动画
            $field.find('.xun-radio-option').each(function(index) {
                var $option = $(this);
                setTimeout(function() {
                    $option.addClass('animate-slideInUp');
                }, index * 50);
            });
        },

        /**
         * 添加选中动画
         */
        addSelectionAnimation: function($option) {
            $option.addClass('animate-pulse');
            setTimeout(function() {
                $option.removeClass('animate-pulse');
            }, 600);
        },

        /**
         * 更新搜索结果
         */
        updateSearchResults: function($field, searchTerm, visibleCount, totalCount) {
            // 移除现有的搜索结果提示
            $field.find('.search-stats, .no-results').remove();

            if (searchTerm) {
                if (visibleCount === 0) {
                    // 显示无结果提示
                    var noResultsHtml = '<div class="no-results flex flex-col items-center justify-center py-12 text-gray-500">' +
                        '<div class="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">' +
                        '<svg class="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">' +
                        '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>' +
                        '</svg>' +
                        '</div>' +
                        '<p class="text-sm font-medium text-gray-600 mb-1">未找到匹配的选项</p>' +
                        '<p class="text-xs text-gray-400">尝试使用其他关键词搜索</p>' +
                        '</div>';
                    $field.find('.xun-radio-group').after(noResultsHtml);
                } else {
                    // 显示搜索结果统计
                    var statsHtml = '<div class="search-stats text-center mb-4">' +
                        '<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800">' +
                        '显示 ' + visibleCount + ' / ' + totalCount + ' 项' +
                        '</span>' +
                        '</div>';
                    $field.find('.xun-radio-group').before(statsHtml);
                }
            }

            // 更新计数显示
            var $count = $field.find('.xun-radio-count');
            if ($count.length) {
                if (searchTerm && visibleCount !== totalCount) {
                    $count.text('显示 ' + visibleCount + ' / ' + totalCount + ' 个选项');
                } else {
                    $count.text('共 ' + totalCount + ' 个选项');
                }
            }
        },

        /**
         * 触发变化事件
         */
        triggerChangeEvent: function($field, value) {
            var fieldId = $field.data('field-id');

            // 触发自定义事件
            $field.trigger('xun:radio:change', {
                fieldId: fieldId,
                value: value
            });
        },

        /**
         * 重新初始化字段（用于动态添加的字段）
         */
        reinit: function() {
            this.initializeFields();
        },

        /**
         * 获取字段值
         */
        getValue: function(fieldId) {
            var $field = $('.xun-radio-field[data-field-id="' + fieldId + '"]');
            var $checked = $field.find('.xun-radio-input:checked');
            return $checked.length ? $checked.val() : '';
        },

        /**
         * 设置字段值
         */
        setValue: function(fieldId, value) {
            var $field = $('.xun-radio-field[data-field-id="' + fieldId + '"]');
            
            // 清除所有选中状态
            $field.find('.xun-radio-input').prop('checked', false);
            
            // 设置新的选中状态
            if (value) {
                $field.find('.xun-radio-input[value="' + value + '"]').prop('checked', true);
            }
            
            // 更新显示状态
            this.updateFieldState($field);
        },

        /**
         * 禁用/启用选项
         */
        toggleOption: function(fieldId, optionValue, disabled) {
            var $field = $('.xun-radio-field[data-field-id="' + fieldId + '"]');
            var $option = $field.find('.xun-radio-option[data-value="' + optionValue + '"]');
            var $input = $option.find('.xun-radio-input');
            
            if (disabled) {
                $input.prop('disabled', true);
                $option.addClass('disabled opacity-50 cursor-not-allowed');
            } else {
                $input.prop('disabled', false);
                $option.removeClass('disabled opacity-50 cursor-not-allowed');
            }
        }
    };

    // 文档就绪时初始化
    $(document).ready(function() {
        XunRadioField.init();
    });

    // 暴露到全局
    window.XunRadioField = XunRadioField;

})(jQuery);
