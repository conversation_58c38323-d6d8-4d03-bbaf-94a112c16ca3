<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework Palette 字段类型
 * 
 * 这个字段类型提供了一个现代化的调色板界面，支持预设颜色选择、
 * 颜色分组、搜索过滤、自定义颜色、多选支持等高级功能。
 * 采用TailwindCSS设计，提供优秀的用户体验。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Field_palette' ) ) {
    
    /**
     * XUN_Field_palette 调色板字段类
     * 
     * 功能特性：
     * - 预设颜色调色板选择
     * - 颜色分组和分类显示
     * - 颜色搜索和过滤功能
     * - 自定义颜色添加支持
     * - 单选和多选模式
     * - 颜色对比度检查
     * - 响应式设计
     * - 完整的数据验证
     * 
     * @since 1.0
     */
    class XUN_Field_palette extends XUN_Fields {
        
        /**
         * 构造函数
         * 
         * 初始化调色板字段实例。
         * 
         * @since 1.0
         * 
         * @param array  $field  字段配置
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field = array(), $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }
        
        /**
         * 渲染调色板字段
         * 
         * 生成现代化的调色板选择界面。
         * 
         * @since 1.0
         */
        public function render() {
            
            // 输出前置内容
            echo $this->field_before();
            
            // 解析字段配置
            $args = wp_parse_args( $this->field, array(
                'options'         => array(),
                'multiple'        => false,
                'show_labels'     => true,
                'show_search'     => true,
                'show_custom'     => false,
                'allow_empty'     => true,
                'group_colors'    => false,
                'colors_per_row'  => 5,
                'color_size'      => 'medium',
                'show_contrast'   => false,
                'show_preview'    => true,
                'layout'          => 'grid', // grid, list, compact
                'animation'       => true,
            ) );
            
            // 处理值
            $value = $this->value;
            if ( $args['multiple'] ) {
                if ( ! is_array( $value ) ) {
                    $value = ! empty( $value ) ? array( $value ) : array();
                }
            } else {
                if ( is_array( $value ) ) {
                    $value = ! empty( $value ) ? $value[0] : '';
                }
            }
            
            // 生成唯一ID
            $field_id = 'xun-palette-' . uniqid();
            
            // 主容器
            $container_classes = array(
                'xun-palette-field',
                'xun-palette-' . $args['layout'],
                'xun-palette-' . $args['color_size'],
            );
            
            if ( $args['multiple'] ) {
                $container_classes[] = 'xun-palette-multiple';
            }
            
            if ( $args['animation'] ) {
                $container_classes[] = 'xun-palette-animate';
            }
            
            echo '<div class="' . implode( ' ', $container_classes ) . '" data-field-id="' . esc_attr( $this->field['id'] ) . '">';
            
            // 调色板配置数据
            $palette_data = array(
                'multiple'      => $args['multiple'],
                'allowEmpty'    => $args['allow_empty'],
                'showContrast'  => $args['show_contrast'],
                'colorsPerRow'  => $args['colors_per_row'],
                'layout'        => $args['layout'],
                'animation'     => $args['animation'],
            );
            
            // 搜索和工具栏
            if ( $args['show_search'] || $args['show_custom'] ) {
                echo '<div class="xun-palette-toolbar mb-4 flex flex-wrap items-center gap-3">';
                
                // 搜索框
                if ( $args['show_search'] ) {
                    echo '<div class="flex-1 min-w-0">';
                    echo '<div class="relative">';
                    echo '<div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">';
                    echo '<svg class="h-4 w-4 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                    echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />';
                    echo '</svg>';
                    echo '</div>';
                    echo '<input type="text" class="xun-palette-search block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 text-sm" placeholder="搜索颜色..." />';
                    echo '</div>';
                    echo '</div>';
                }
                
                // 自定义颜色按钮
                if ( $args['show_custom'] ) {
                    echo '<button type="button" class="xun-palette-add-custom inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">';
                    echo '<svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                    echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />';
                    echo '</svg>';
                    echo '添加颜色';
                    echo '</button>';
                }
                
                echo '</div>';
            }
            
            // 调色板容器
            echo '<div class="xun-palette-container" data-palette-config="' . esc_attr( json_encode( $palette_data ) ) . '">';
            
            if ( ! empty( $args['options'] ) ) {
                
                // 根据布局渲染调色板
                if ( $args['group_colors'] && is_array( reset( $args['options'] ) ) ) {
                    // 分组显示
                    $this->render_grouped_palettes( $args['options'], $value, $args );
                } else {
                    // 单一调色板显示
                    $this->render_single_palette( $args['options'], $value, $args );
                }
                
            } else {
                // 空状态
                echo '<div class="xun-palette-empty text-center py-8 text-gray-500">';
                echo '<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" />';
                echo '</svg>';
                echo '<p class="mt-2 text-sm">暂无可用的调色板</p>';
                echo '</div>';
            }
            
            echo '</div>'; // 结束调色板容器
            
            // 选中颜色预览
            if ( $args['show_preview'] ) {
                echo '<div class="xun-palette-preview mt-4 p-3 bg-gray-50 rounded-lg">';
                echo '<div class="text-sm font-medium text-gray-700 mb-2">已选择的颜色</div>';
                echo '<div class="xun-palette-selected-colors flex flex-wrap gap-2"></div>';
                echo '</div>';
            }
            
            // 隐藏输入字段
            if ( $args['multiple'] ) {
                if ( is_array( $value ) ) {
                    foreach ( $value as $selected_value ) {
                        echo '<input type="hidden" name="' . esc_attr( $this->field_name() ) . '[]" value="' . esc_attr( $selected_value ) . '" class="xun-palette-input" />';
                    }
                }
                // 确保至少有一个空的输入字段用于表单提交
                if ( empty( $value ) ) {
                    echo '<input type="hidden" name="' . esc_attr( $this->field_name() ) . '[]" value="" class="xun-palette-input" />';
                }
            } else {
                echo '<input type="hidden" name="' . esc_attr( $this->field_name() ) . '" value="' . esc_attr( $value ) . '" class="xun-palette-input" />';
            }
            
            // 不需要添加任何容器，我们将使用Xun Framework自己的颜色选择器
            
            echo '</div>'; // 结束主容器
            
            // 输出后置内容
            echo $this->field_after();
        }
        
        /**
         * 渲染分组调色板
         * 
         * @since 1.0
         * 
         * @param array $palettes 调色板组
         * @param mixed $value    当前值
         * @param array $args     配置参数
         */
        private function render_grouped_palettes( $palettes, $value, $args ) {
            
            foreach ( $palettes as $group_key => $group_data ) {
                
                // 组标题
                if ( $args['show_labels'] && ! empty( $group_data['label'] ) ) {
                    echo '<div class="xun-palette-group-label mb-3 text-sm font-medium text-gray-700">';
                    echo esc_html( $group_data['label'] );
                    echo '</div>';
                }
                
                // 组容器
                echo '<div class="xun-palette-group mb-6" data-group="' . esc_attr( $group_key ) . '">';
                
                $colors = ! empty( $group_data['colors'] ) ? $group_data['colors'] : $group_data;
                $this->render_color_grid( $colors, $value, $args );
                
                echo '</div>';
            }
        }
        
        /**
         * 渲染单一调色板
         * 
         * @since 1.0
         * 
         * @param array $colors 颜色数组
         * @param mixed $value  当前值
         * @param array $args   配置参数
         */
        private function render_single_palette( $colors, $value, $args ) {
            echo '<div class="xun-palette-group" data-group="default">';
            $this->render_color_grid( $colors, $value, $args );
            echo '</div>';
        }
        
        /**
         * 渲染颜色网格
         *
         * @since 1.0
         *
         * @param array $colors 颜色数组
         * @param mixed $value  当前值
         * @param array $args   配置参数
         */
        private function render_color_grid( $colors, $value, $args ) {

            // 网格样式 - 修复TailwindCSS类名生成问题
            $grid_classes = 'grid gap-3';

            switch ( $args['layout'] ) {
                case 'list':
                    $grid_classes .= ' grid-cols-1';
                    break;
                case 'compact':
                    $grid_classes .= ' grid-cols-8 sm:grid-cols-10 md:grid-cols-12';
                    break;
                default: // grid
                    $cols = intval( $args['colors_per_row'] );
                    // 使用具体的类名而不是动态生成，确保TailwindCSS能识别
                    switch ( $cols ) {
                        case 3:
                            $grid_classes .= ' grid-cols-3';
                            break;
                        case 4:
                            $grid_classes .= ' grid-cols-4';
                            break;
                        case 5:
                            $grid_classes .= ' grid-cols-5';
                            break;
                        case 6:
                            $grid_classes .= ' grid-cols-6';
                            break;
                        case 7:
                            $grid_classes .= ' grid-cols-7';
                            break;
                        case 8:
                            $grid_classes .= ' grid-cols-8';
                            break;
                        case 10:
                            $grid_classes .= ' grid-cols-10';
                            break;
                        case 12:
                            $grid_classes .= ' grid-cols-12';
                            break;
                        default:
                            $grid_classes .= ' grid-cols-5'; // 默认5列
                            break;
                    }
                    break;
            }
            
            echo '<div class="' . $grid_classes . '">';
            
            foreach ( $colors as $color_key => $color_data ) {
                
                // 处理颜色数据
                if ( is_array( $color_data ) ) {
                    $color_value = ! empty( $color_data['value'] ) ? $color_data['value'] : $color_key;
                    $color_label = ! empty( $color_data['label'] ) ? $color_data['label'] : $color_value;
                    $color_desc = ! empty( $color_data['desc'] ) ? $color_data['desc'] : '';
                } else {
                    $color_value = $color_data;
                    $color_label = $color_data;
                    $color_desc = '';
                }
                
                // 检查是否选中
                $is_selected = false;
                if ( $args['multiple'] ) {
                    $is_selected = is_array( $value ) && in_array( $color_value, $value );
                } else {
                    $is_selected = $color_value === $value;
                }
                
                // 颜色项目容器
                $item_classes = array(
                    'xun-palette-item',
                    'relative',
                    'cursor-pointer',
                    'group',
                    'transition-all',
                    'duration-200',
                );
                
                if ( $is_selected ) {
                    $item_classes[] = 'xun-selected';
                }
                
                echo '<div class="' . implode( ' ', $item_classes ) . '" data-color="' . esc_attr( $color_value ) . '" data-label="' . esc_attr( $color_label ) . '">';
                
                // 颜色显示
                $this->render_color_display( $color_value, $color_label, $color_desc, $args, $is_selected );
                
                echo '</div>';
            }
            
            echo '</div>';
        }
        
        /**
         * 渲染颜色显示
         * 
         * @since 1.0
         * 
         * @param string $color_value 颜色值
         * @param string $color_label 颜色标签
         * @param string $color_desc  颜色描述
         * @param array  $args        配置参数
         * @param bool   $is_selected 是否选中
         */
        private function render_color_display( $color_value, $color_label, $color_desc, $args, $is_selected ) {
            
            // 颜色块尺寸
            $size_classes = array(
                'small'  => 'w-8 h-8',
                'medium' => 'w-12 h-12',
                'large'  => 'w-16 h-16',
            );
            
            $color_size = $size_classes[ $args['color_size'] ] ?? $size_classes['medium'];
            
            if ( $args['layout'] === 'list' ) {
                // 列表布局 - 使用描边表示选中状态
                $list_border_classes = $is_selected
                    ? 'border-blue-500 bg-blue-50 border-2'
                    : 'border-gray-200 hover:border-gray-300 border';

                echo '<div class="flex items-center space-x-3 p-3 rounded-lg transition-colors ' . $list_border_classes . '">';

                // 颜色块也使用描边
                $color_border_classes = $is_selected
                    ? 'border-2 border-blue-500'
                    : 'border border-gray-200';

                echo '<div class="' . $color_size . ' rounded-lg shadow-sm flex-shrink-0 ' . $color_border_classes . '" style="background-color: ' . esc_attr( $color_value ) . ';"></div>';
                echo '<div class="flex-1 min-w-0">';
                echo '<div class="text-sm font-medium text-gray-900">' . esc_html( $color_label ) . '</div>';
                if ( ! empty( $color_desc ) ) {
                    echo '<div class="text-xs text-gray-500">' . esc_html( $color_desc ) . '</div>';
                }
                echo '<div class="text-xs text-gray-400 font-mono">' . esc_html( $color_value ) . '</div>';
                echo '</div>';
                echo '</div>';
            } else {
                // 网格和紧凑布局
                echo '<div class="relative">';

                // 颜色块 - 使用描边表示选中状态
                $border_classes = $is_selected
                    ? 'border-2 border-blue-500 shadow-md'
                    : 'border border-gray-200 group-hover:border-gray-400';

                echo '<div class="' . $color_size . ' rounded-lg shadow-sm transition-all duration-200 ' . $border_classes . ' group-hover:shadow-md" style="background-color: ' . esc_attr( $color_value ) . ';"></div>';

                // 工具提示
                if ( $args['layout'] !== 'compact' ) {
                    echo '<div class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">';
                    echo esc_html( $color_label );
                    if ( $color_label !== $color_value ) {
                        echo '<br><span class="font-mono">' . esc_html( $color_value ) . '</span>';
                    }
                    echo '</div>';
                }

                echo '</div>';
            }
        }
        
        /**
         * 加载字段资源
         *
         * 加载调色板字段所需的JavaScript资源。
         *
         * @since 1.0
         */
        public function enqueue() {

            // 加载自定义JavaScript，依赖Xun Color字段
            wp_enqueue_script(
                'xun-field-palette',
                XUN_Setup::$url . '/assets/js/fields/palette.js',
                array( 'jquery', 'wp-color-picker' ),
                XUN_Setup::$version,
                true
            );
            
            // 本地化脚本
            wp_localize_script( 'xun-field-palette', 'xunPalette', array(
                'strings' => array(
                    'search'        => '搜索颜色...',
                    'noResults'     => '未找到匹配的颜色',
                    'addCustom'     => '添加自定义颜色',
                    'removeColor'   => '移除颜色',
                    'selectColor'   => '选择颜色',
                    'selectedColor' => '已选择的颜色',
                    'colorValue'    => '颜色值',
                ),
                'nonce' => wp_create_nonce( 'xun_palette_nonce' ),
            ) );
        }
        
        /**
         * 验证和清理字段数据
         * 
         * 对调色板字段的数据进行验证和清理。
         * 
         * @since 1.0
         * 
         * @param mixed $value 要验证的值
         * 
         * @return mixed 清理后的数据
         */
        public function validate( $value ) {
            
            $args = wp_parse_args( $this->field, array(
                'multiple'    => false,
                'allow_empty' => true,
                'options'     => array(),
            ) );
            
            if ( $args['multiple'] ) {
                // 多选验证
                if ( ! is_array( $value ) ) {
                    return array();
                }
                
                $validated = array();
                foreach ( $value as $single_value ) {
                    $clean_value = sanitize_text_field( $single_value );
                    if ( ! empty( $clean_value ) || $args['allow_empty'] ) {
                        $validated[] = $clean_value;
                    }
                }
                
                return array_unique( $validated );
            } else {
                // 单选验证
                $validated = sanitize_text_field( $value );
                
                if ( empty( $validated ) && ! $args['allow_empty'] ) {
                    return '';
                }
                
                return $validated;
            }
        }
    }
}
