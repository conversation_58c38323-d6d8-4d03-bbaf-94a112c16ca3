/**
 * Xun Framework 媒体字段 JavaScript
 * 
 * 提供现代化的媒体选择和管理功能，包括：
 * - WordPress媒体库集成
 * - 拖拽上传支持
 * - 实时预览更新
 * - 用户体验优化
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

(function($) {
    'use strict';

    /**
     * 媒体字段类
     * 
     * 管理单个媒体字段的所有交互功能
     */
    class XunMediaField {
        
        /**
         * 构造函数
         * 
         * @param {jQuery} $container 字段容器元素
         */
        constructor($container) {
            this.$container = $container;
            this.$preview = $container.find('.xun-media-preview');
            this.$controls = $container.find('.xun-media-controls');
            this.$urlInput = $container.find('.xun-media-url');
            this.$hiddenFields = {};
            
            // 缓存隐藏字段
            const hiddenFields = ['id', 'filename', 'filesize', 'width', 'height', 'thumbnail', 'alt', 'title', 'description', 'mime_type'];
            hiddenFields.forEach(field => {
                this.$hiddenFields[field] = $container.find(`.xun-media-${field}`);
            });
            
            // 获取配置
            this.config = {
                fieldId: $container.data('field-id'),
                library: $container.data('library'),
                previewSize: $container.data('preview-size') || 'medium',
                multiple: $container.data('multiple') === true
            };
            
            this.init();
        }
        
        /**
         * 初始化字段
         */
        init() {
            this.bindEvents();
        }
        
        /**
         * 绑定事件
         */
        bindEvents() {
            // 选择媒体按钮
            this.$container.on('click', '.xun-media-button', (e) => {
                e.preventDefault();
                this.openMediaLibrary();
            });

            // 移除按钮
            this.$container.on('click', '.xun-media-remove', (e) => {
                e.preventDefault();
                this.removeMedia();
            });
            
            // URL输入框变化
            this.$urlInput.on('change', (e) => {
                this.handleUrlChange(e.target.value);
            });
        }
        

        
        /**
         * 打开媒体库
         */
        openMediaLibrary() {
            // 如果已经有媒体库实例在运行，先关闭它
            if (this.mediaLibrary) {
                this.mediaLibrary.close();
                this.mediaLibrary = null;
            }

            // 创建媒体库实例
            this.mediaLibrary = wp.media({
                title: '选择媒体文件',
                button: {
                    text: '选择'
                },
                multiple: this.config.multiple,
                library: this.getLibraryConfig(),
                frame: 'select'  // 明确指定使用select框架
            });

            // 选择文件时的处理
            this.mediaLibrary.on('select', () => {
                const selection = this.mediaLibrary.state().get('selection');
                const attachment = selection.first().toJSON();
                this.setMedia(attachment);

                // 选择完成后立即关闭媒体库
                this.mediaLibrary.close();
            });

            // 关闭事件处理
            this.mediaLibrary.on('close', () => {
                // 清理媒体库实例
                this.mediaLibrary = null;
            });

            // 打开媒体库
            this.mediaLibrary.open();
        }
        
        /**
         * 获取媒体库配置
         * 
         * @return {object} 媒体库配置
         */
        getLibraryConfig() {
            const config = {
                uploadedTo: wp.media.view.settings.post.id
            };
            
            if (this.config.library) {
                const types = this.config.library.split(',').map(type => type.trim());
                if (types.length > 0) {
                    config.type = types;
                }
            }
            
            return config;
        }
        
        /**
         * 设置媒体文件
         * 
         * @param {object} attachment 附件对象
         */
        setMedia(attachment) {
            // 更新隐藏字段
            this.updateHiddenField('id', attachment.id);
            this.updateHiddenField('url', attachment.url);
            this.updateHiddenField('filename', attachment.filename);
            this.updateHiddenField('alt', attachment.alt || '');
            this.updateHiddenField('title', attachment.title || '');
            this.updateHiddenField('description', attachment.description || '');
            this.updateHiddenField('mime_type', attachment.mime || '');
            
            // 处理文件大小
            if (attachment.filesizeHumanReadable) {
                this.updateHiddenField('filesize', attachment.filesizeHumanReadable);
            }
            
            // 处理图片尺寸
            if (attachment.width && attachment.height) {
                this.updateHiddenField('width', attachment.width);
                this.updateHiddenField('height', attachment.height);
            }
            
            // 处理缩略图
            let thumbnailUrl = '';
            if (attachment.sizes && attachment.sizes.thumbnail) {
                thumbnailUrl = attachment.sizes.thumbnail.url;
            } else if (attachment.type === 'image') {
                thumbnailUrl = attachment.url;
            }
            this.updateHiddenField('thumbnail', thumbnailUrl);
            
            // 更新URL输入框
            this.$urlInput.val(attachment.url);
            
            // 更新预览
            this.updatePreview(attachment);
            
            // 触发变化事件
            this.$container.trigger('xun:media:changed', [attachment]);
        }
        
        /**
         * 移除媒体文件
         */
        removeMedia() {
            if (!confirm('确定要移除这个文件吗？')) {
                return;
            }
            
            // 清空所有字段
            Object.keys(this.$hiddenFields).forEach(field => {
                this.updateHiddenField(field, '');
            });
            
            // 清空URL输入框
            this.$urlInput.val('');
            
            // 移除预览
            this.$preview.remove();
            
            // 触发变化事件
            this.$container.trigger('xun:media:removed');
        }
        
        /**
         * 更新隐藏字段值
         * 
         * @param {string} field 字段名
         * @param {string} value 字段值
         */
        updateHiddenField(field, value) {
            if (this.$hiddenFields[field]) {
                this.$hiddenFields[field].val(value);
            }
        }
        
        /**
         * 更新预览
         * 
         * @param {object} attachment 附件对象
         */
        updatePreview(attachment) {
            // 如果没有预览容器，创建一个
            if (!this.$preview.length) {
                this.createPreview();
            }
            
            const isImage = attachment.type === 'image';
            const previewUrl = isImage ? 
                (attachment.sizes && attachment.sizes[this.config.previewSize] ? 
                    attachment.sizes[this.config.previewSize].url : attachment.url) : 
                attachment.url;
            
            if (isImage) {
                // 图片预览
                this.$preview.html(`
                    <img src="${previewUrl}" alt="${attachment.alt || ''}" class="w-full h-full object-cover" />
                    <button type="button" class="xun-media-remove absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2" title="移除">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                    </button>
                `);
            } else {
                // 非图片文件预览
                const icon = this.getFileIcon(attachment.mime);
                this.$preview.html(`
                    <div class="flex items-center justify-center p-6 min-h-[120px]">
                        <div class="text-center">
                            <div class="text-4xl text-gray-400 mb-2">${icon}</div>
                            <div class="text-sm font-medium text-gray-700 truncate max-w-[200px]">${attachment.filename}</div>
                            ${attachment.filesizeHumanReadable ? `<div class="text-xs text-gray-500 mt-1">${attachment.filesizeHumanReadable}</div>` : ''}
                        </div>
                    </div>
                    <button type="button" class="xun-media-remove absolute top-2 right-2 w-8 h-8 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center hover:bg-red-600 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2" title="移除">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path></svg>
                    </button>
                `);
            }
            
            this.$preview.show();
        }
        
        /**
         * 创建预览容器
         */
        createPreview() {
            this.$preview = $('<div class="xun-media-preview relative group bg-gray-50 border-2 border-dashed border-gray-200 rounded-lg overflow-hidden transition-all duration-200 hover:border-gray-300 mb-4"></div>');
            this.$controls.before(this.$preview);
        }
        
        /**
         * 获取文件类型图标
         * 
         * @param {string} mimeType MIME类型
         * @return {string} 图标HTML
         */
        getFileIcon(mimeType) {
            if (mimeType.startsWith('image/')) {
                return '<svg fill="currentColor" viewBox="0 0 24 24" class="w-full h-full"><path d="M4 4h16a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V6a2 2 0 012-2zm16 12V8l-4 4-6-6-6 6v4h16zM9 11a1 1 0 100-2 1 1 0 000 2z"/></svg>';
            } else if (mimeType.startsWith('video/')) {
                return '<svg fill="currentColor" viewBox="0 0 24 24" class="w-full h-full"><path d="M4 4h16a2 2 0 012 2v12a2 2 0 01-2 2H4a2 2 0 01-2-2V6a2 2 0 012-2zm6 4v8l6-4-6-4z"/></svg>';
            } else if (mimeType.startsWith('audio/')) {
                return '<svg fill="currentColor" viewBox="0 0 24 24" class="w-full h-full"><path d="M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z"/></svg>';
            } else {
                return '<svg fill="currentColor" viewBox="0 0 24 24" class="w-full h-full"><path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8l-6-6zm4 18H6V4h7v5h5v11z"/></svg>';
            }
        }
        
        /**
         * 处理URL变化
         * 
         * @param {string} url 新的URL
         */
        handleUrlChange(url) {
            if (!url) {
                this.removeMedia();
                return;
            }
            
            // 简单的URL验证
            try {
                new URL(url);
                this.updateHiddenField('url', url);
                
                // 如果是图片URL，尝试创建预览
                if (this.isImageUrl(url)) {
                    const fakeAttachment = {
                        id: '',
                        url: url,
                        type: 'image',
                        filename: url.split('/').pop(),
                        alt: '',
                        title: '',
                        description: '',
                        mime: 'image/jpeg'
                    };
                    this.updatePreview(fakeAttachment);
                }
            } catch (e) {
                // URL无效，不做处理
            }
        }
        
        /**
         * 检查是否为图片URL
         * 
         * @param {string} url URL地址
         * @return {boolean} 是否为图片
         */
        isImageUrl(url) {
            const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp'];
            const extension = url.split('.').pop().toLowerCase();
            return imageExtensions.includes(extension);
        }
        

    }

    /**
     * 初始化所有媒体字段
     */
    function initMediaFields() {
        $('.xun-media-field').each(function() {
            const $this = $(this);
            if (!$this.data('xun-media-initialized')) {
                new XunMediaField($this);
                $this.data('xun-media-initialized', true);
            }
        });
    }

    // 文档就绪时初始化
    $(document).ready(initMediaFields);
    
    // 支持动态添加的字段
    $(document).on('xun:field:added', initMediaFields);

})(jQuery);
