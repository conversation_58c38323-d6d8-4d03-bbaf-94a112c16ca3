<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework Slider 字段类型
 * 
 * 这个字段类型提供了一个现代化的滑块界面，支持单值和范围选择、
 * 实时预览、键盘快捷键、自定义单位显示等高级功能。
 * 采用TailwindCSS设计，提供优秀的用户体验。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Field_slider' ) ) {
    
    /**
     * XUN_Field_slider 滑块字段类
     * 
     * 功能特性：
     * - 单值和范围滑块支持
     * - 现代化TailwindCSS界面
     * - 实时数值显示和预览
     * - 键盘快捷键支持
     * - 移动端友好设计
     * - 自定义单位和格式化
     * - 平滑动画过渡效果
     * - 完整的数据验证
     * 
     * @since 1.0
     */
    class XUN_Field_slider extends XUN_Fields {
        
        /**
         * 构造函数
         * 
         * 初始化滑块字段实例。
         * 
         * @since 1.0
         * 
         * @param array  $field  字段配置
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field = array(), $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }
        
        /**
         * 渲染滑块字段
         * 
         * 生成现代化的滑块界面，支持单值和范围选择。
         * 
         * @since 1.0
         */
        public function render() {
            
            // 输出前置内容
            echo $this->field_before();
            
            // 解析字段配置
            $args = wp_parse_args( $this->field, array(
                'min'           => 0,
                'max'           => 100,
                'step'          => 1,
                'unit'          => '',
                'range'         => false,
                'show_input'    => true,
                'show_labels'   => true,
                'show_ticks'    => false,
                'tick_step'     => 10,
                'precision'     => 0,
                'prefix'        => '',
                'suffix'        => '',
                'color'         => 'blue',
                'size'          => 'medium',
                'animate'       => true,
                'keyboard'      => true,
                'tooltip'       => true,
                'format_value'  => null,
            ) );
            
            // 处理值
            $value = $this->value;
            if ( $args['range'] ) {
                // 范围滑块
                if ( ! is_array( $value ) ) {
                    $value = array(
                        'min' => $args['min'],
                        'max' => $args['max'],
                    );
                }
                $value = wp_parse_args( $value, array(
                    'min' => $args['min'],
                    'max' => $args['max'],
                ) );
            } else {
                // 单值滑块
                if ( $value === '' || $value === null ) {
                    $value = $args['min'];
                }
                $value = (float) $value;
            }
            
            // 生成唯一ID
            $field_id = 'xun-slider-' . uniqid();
            
            // 主容器
            $container_classes = array(
                'xun-slider-field',
                'xun-slider-' . $args['color'],
                'xun-slider-' . $args['size'],
            );
            
            if ( $args['range'] ) {
                $container_classes[] = 'xun-slider-range';
            }
            
            if ( $args['animate'] ) {
                $container_classes[] = 'xun-slider-animate';
            }
            
            echo '<div class="' . implode( ' ', $container_classes ) . '" data-field-id="' . esc_attr( $this->field['id'] ) . '">';
            
            // 滑块配置数据
            $slider_data = array(
                'min'          => $args['min'],
                'max'          => $args['max'],
                'step'         => $args['step'],
                'range'        => $args['range'],
                'precision'    => $args['precision'],
                'keyboard'     => $args['keyboard'],
                'tooltip'      => $args['tooltip'],
                'animate'      => $args['animate'],
                'color'        => $args['color'],
                'formatValue'  => $args['format_value'],
            );
            
            // 标签显示
            if ( $args['show_labels'] ) {
                echo '<div class="flex justify-between items-center mb-3">';
                echo '<span class="text-sm font-medium text-gray-700">';
                echo esc_html( $args['prefix'] ) . esc_html( $args['min'] ) . esc_html( $args['suffix'] );
                echo '</span>';
                echo '<span class="text-sm font-medium text-gray-700">';
                echo esc_html( $args['prefix'] ) . esc_html( $args['max'] ) . esc_html( $args['suffix'] );
                echo '</span>';
                echo '</div>';
            }
            
            // 滑块容器
            echo '<div class="relative mb-4">';
            
            // 滑块轨道
            $track_classes = 'xun-slider-track relative w-full h-2 bg-gray-200 rounded-full cursor-pointer transition-colors duration-200 hover:bg-gray-300';
            echo '<div class="' . $track_classes . '" data-slider-config="' . esc_attr( json_encode( $slider_data ) ) . '">';
            
            // 进度条
            echo '<div class="xun-slider-progress absolute top-0 left-0 h-full bg-' . esc_attr( $args['color'] ) . '-500 rounded-full transition-all duration-200"></div>';
            
            if ( $args['range'] ) {
                // 范围滑块的两个手柄
                echo '<div class="xun-slider-handle xun-slider-handle-min absolute top-1/2 -translate-y-1/2 w-5 h-5 bg-white border-2 border-' . esc_attr( $args['color'] ) . '-500 rounded-full cursor-grab shadow-md transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-' . esc_attr( $args['color'] ) . '-500 focus:ring-offset-2" tabindex="0" role="slider" aria-valuemin="' . esc_attr( $args['min'] ) . '" aria-valuemax="' . esc_attr( $args['max'] ) . '" aria-valuenow="' . esc_attr( $value['min'] ) . '"></div>';
                echo '<div class="xun-slider-handle xun-slider-handle-max absolute top-1/2 -translate-y-1/2 w-5 h-5 bg-white border-2 border-' . esc_attr( $args['color'] ) . '-500 rounded-full cursor-grab shadow-md transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-' . esc_attr( $args['color'] ) . '-500 focus:ring-offset-2" tabindex="0" role="slider" aria-valuemin="' . esc_attr( $args['min'] ) . '" aria-valuemax="' . esc_attr( $args['max'] ) . '" aria-valuenow="' . esc_attr( $value['max'] ) . '"></div>';
            } else {
                // 单值滑块的手柄
                echo '<div class="xun-slider-handle absolute top-1/2 -translate-y-1/2 w-5 h-5 bg-white border-2 border-' . esc_attr( $args['color'] ) . '-500 rounded-full cursor-grab shadow-md transition-all duration-200 hover:scale-110 focus:outline-none focus:ring-2 focus:ring-' . esc_attr( $args['color'] ) . '-500 focus:ring-offset-2" tabindex="0" role="slider" aria-valuemin="' . esc_attr( $args['min'] ) . '" aria-valuemax="' . esc_attr( $args['max'] ) . '" aria-valuenow="' . esc_attr( $value ) . '"></div>';
            }
            
            // 工具提示
            if ( $args['tooltip'] ) {
                if ( $args['range'] ) {
                    echo '<div class="xun-slider-tooltip xun-slider-tooltip-min absolute -top-10 left-0 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 transition-opacity duration-200 pointer-events-none"></div>';
                    echo '<div class="xun-slider-tooltip xun-slider-tooltip-max absolute -top-10 right-0 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 transition-opacity duration-200 pointer-events-none"></div>';
                } else {
                    echo '<div class="xun-slider-tooltip absolute -top-10 left-0 px-2 py-1 bg-gray-800 text-white text-xs rounded opacity-0 transition-opacity duration-200 pointer-events-none"></div>';
                }
            }
            
            echo '</div>'; // 结束滑块轨道
            
            // 刻度显示
            if ( $args['show_ticks'] ) {
                echo '<div class="xun-slider-ticks flex justify-between mt-2">';
                $tick_count = ( $args['max'] - $args['min'] ) / $args['tick_step'];
                for ( $i = 0; $i <= $tick_count; $i++ ) {
                    $tick_value = $args['min'] + ( $i * $args['tick_step'] );
                    echo '<div class="text-xs text-gray-500">' . esc_html( $args['prefix'] ) . esc_html( $tick_value ) . esc_html( $args['suffix'] ) . '</div>';
                }
                echo '</div>';
            }
            
            echo '</div>'; // 结束滑块容器
            
            // 数值输入框
            if ( $args['show_input'] ) {
                echo '<div class="flex items-center space-x-3">';
                
                if ( $args['range'] ) {
                    // 范围输入
                    echo '<div class="flex items-center space-x-2">';
                    echo '<label class="text-sm font-medium text-gray-700">最小值:</label>';
                    echo '<input type="number" name="' . esc_attr( $this->field_name( '[min]' ) ) . '" value="' . esc_attr( $value['min'] ) . '" min="' . esc_attr( $args['min'] ) . '" max="' . esc_attr( $args['max'] ) . '" step="' . esc_attr( $args['step'] ) . '" class="xun-slider-input xun-slider-input-min w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-' . esc_attr( $args['color'] ) . '-500 focus:border-transparent" />';
                    if ( ! empty( $args['unit'] ) ) {
                        echo '<span class="text-sm text-gray-500">' . esc_html( $args['unit'] ) . '</span>';
                    }
                    echo '</div>';
                    
                    echo '<div class="flex items-center space-x-2">';
                    echo '<label class="text-sm font-medium text-gray-700">最大值:</label>';
                    echo '<input type="number" name="' . esc_attr( $this->field_name( '[max]' ) ) . '" value="' . esc_attr( $value['max'] ) . '" min="' . esc_attr( $args['min'] ) . '" max="' . esc_attr( $args['max'] ) . '" step="' . esc_attr( $args['step'] ) . '" class="xun-slider-input xun-slider-input-max w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-' . esc_attr( $args['color'] ) . '-500 focus:border-transparent" />';
                    if ( ! empty( $args['unit'] ) ) {
                        echo '<span class="text-sm text-gray-500">' . esc_html( $args['unit'] ) . '</span>';
                    }
                    echo '</div>';
                } else {
                    // 单值输入
                    echo '<div class="flex items-center space-x-2">';
                    echo '<label class="text-sm font-medium text-gray-700">数值:</label>';
                    echo '<input type="number" name="' . esc_attr( $this->field_name() ) . '" value="' . esc_attr( $value ) . '" min="' . esc_attr( $args['min'] ) . '" max="' . esc_attr( $args['max'] ) . '" step="' . esc_attr( $args['step'] ) . '" class="xun-slider-input w-20 px-2 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-' . esc_attr( $args['color'] ) . '-500 focus:border-transparent" />';
                    if ( ! empty( $args['unit'] ) ) {
                        echo '<span class="text-sm text-gray-500">' . esc_html( $args['unit'] ) . '</span>';
                    }
                    echo '</div>';
                }
                
                echo '</div>';
            } else {
                // 隐藏输入字段
                if ( $args['range'] ) {
                    echo '<input type="hidden" name="' . esc_attr( $this->field_name( '[min]' ) ) . '" value="' . esc_attr( $value['min'] ) . '" class="xun-slider-input xun-slider-input-min" />';
                    echo '<input type="hidden" name="' . esc_attr( $this->field_name( '[max]' ) ) . '" value="' . esc_attr( $value['max'] ) . '" class="xun-slider-input xun-slider-input-max" />';
                } else {
                    echo '<input type="hidden" name="' . esc_attr( $this->field_name() ) . '" value="' . esc_attr( $value ) . '" class="xun-slider-input" />';
                }
            }
            
            // 实时预览区域
            if ( ! empty( $this->field['show_preview'] ) ) {
                echo '<div class="mt-4 p-3 bg-gray-50 rounded-lg">';
                echo '<div class="text-sm font-medium text-gray-700 mb-2">实时预览</div>';
                echo '<div class="xun-slider-preview text-lg font-semibold text-' . esc_attr( $args['color'] ) . '-600"></div>';
                echo '</div>';
            }
            
            echo '</div>'; // 结束主容器
            
            // 输出后置内容
            echo $this->field_after();
        }
        
        /**
         * 加载字段资源
         * 
         * 加载滑块字段所需的JavaScript资源。
         * 
         * @since 1.0
         */
        public function enqueue() {
            
            // 加载自定义JavaScript
            wp_enqueue_script(
                'xun-field-slider',
                XUN_Setup::$url . '/assets/js/fields/slider.js',
                array( 'jquery' ),
                XUN_Setup::$version,
                true
            );
            
            // 本地化脚本
            wp_localize_script( 'xun-field-slider', 'xunSlider', array(
                'strings' => array(
                    'min'     => '最小值',
                    'max'     => '最大值',
                    'value'   => '当前值',
                    'range'   => '范围',
                    'invalid' => '无效数值',
                ),
                'nonce' => wp_create_nonce( 'xun_slider_nonce' ),
            ) );
        }
        
        /**
         * 验证和清理字段数据
         * 
         * 对滑块字段的数据进行验证和清理。
         * 
         * @since 1.0
         * 
         * @param mixed $value 要验证的值
         * 
         * @return mixed 清理后的数据
         */
        public function validate( $value ) {
            
            $args = wp_parse_args( $this->field, array(
                'min'   => 0,
                'max'   => 100,
                'step'  => 1,
                'range' => false,
            ) );
            
            if ( $args['range'] ) {
                // 范围滑块验证
                if ( ! is_array( $value ) ) {
                    return array(
                        'min' => $args['min'],
                        'max' => $args['max'],
                    );
                }
                
                $min = isset( $value['min'] ) ? (float) $value['min'] : $args['min'];
                $max = isset( $value['max'] ) ? (float) $value['max'] : $args['max'];
                
                // 确保值在范围内
                $min = max( $args['min'], min( $args['max'], $min ) );
                $max = max( $args['min'], min( $args['max'], $max ) );
                
                // 确保最小值不大于最大值
                if ( $min > $max ) {
                    $temp = $min;
                    $min = $max;
                    $max = $temp;
                }
                
                $validated = array(
                    'min' => $min,
                    'max' => $max,
                );
            } else {
                // 单值滑块验证
                $validated = (float) $value;
                $validated = max( $args['min'], min( $args['max'], $validated ) );
            }
            
            // 应用自定义验证过滤器
            $validated = apply_filters( 'xun_validate_slider_field', $validated, $this->field );
            $validated = apply_filters( "xun_validate_slider_field_{$this->field['id']}", $validated, $this->field );
            
            return $validated;
        }
    }
}
