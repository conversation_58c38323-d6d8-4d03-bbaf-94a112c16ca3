<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework Sortable 字段类型
 * 
 * 这个字段类型提供了一个现代化的可排序界面，支持拖拽排序、
 * 嵌套字段、多种布局模式、键盘快捷键等高级功能。
 * 采用TailwindCSS设计，提供优秀的用户体验。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */
if ( ! class_exists( 'XUN_Field_sortable' ) ) {
    
    /**
     * XUN_Field_sortable 可排序字段类
     * 
     * 功能特性：
     * - 拖拽排序支持
     * - 嵌套字段渲染
     * - 多种布局模式（垂直、水平、网格）
     * - 键盘快捷键支持
     * - 添加/删除项目操作
     * - 实时预览和状态保存
     * - 响应式设计
     * - 无障碍访问优化
     * - 完整的数据验证
     * 
     * @since 1.0
     */
    class XUN_Field_sortable extends XUN_Fields {
        
        /**
         * 构造函数
         * 
         * 初始化可排序字段实例。
         * 
         * @since 1.0
         * 
         * @param array  $field  字段配置
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field = array(), $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }
        
        /**
         * 渲染可排序字段
         * 
         * 生成现代化的可排序界面。
         * 
         * @since 1.0
         */
        public function render() {
            
            // 输出前置内容
            echo $this->field_before();
            
            // 解析字段配置
            $args = wp_parse_args( $this->field, array(
                'fields'          => array(),
                'layout'          => 'vertical',   // vertical, horizontal, grid
                'sortable'        => true,
                'addable'         => false,
                'removable'       => false,
                'collapsible'     => false,
                'show_handles'    => true,
                'show_numbers'    => true,
                'show_preview'    => false,
                'min_items'       => 0,
                'max_items'       => 0,
                'animation'       => true,
                'keyboard_nav'    => true,
                'grid_columns'    => 3,
                'item_template'   => '',
                'empty_message'   => '暂无项目，点击添加按钮开始。',
                'add_button_text' => '添加项目',
            ) );
            
            // 处理字段定义
            if ( empty( $args['fields'] ) ) {
                echo '<div class="text-red-500 text-sm">错误：未定义字段结构</div>';
                echo $this->field_after();
                return;
            }
            
            // 预处理字段和值
            $pre_fields = array();
            foreach ( $args['fields'] as $key => $field ) {
                $pre_fields[$field['id']] = $field;
            }
            
            // 根据保存的值排序字段
            $sorted_fields = $this->sort_fields_by_value( $pre_fields, $this->value );
            
            // 生成唯一ID
            $field_id = 'xun-sortable-' . uniqid();
            
            // 主容器
            $container_classes = array(
                'xun-sortable-field',
                'xun-sortable-' . $args['layout'],
            );
            
            if ( $args['animation'] ) {
                $container_classes[] = 'xun-sortable-animate';
            }
            
            if ( $args['keyboard_nav'] ) {
                $container_classes[] = 'xun-sortable-keyboard';
            }
            
            echo '<div class="' . implode( ' ', $container_classes ) . '" data-field-id="' . esc_attr( $this->field['id'] ) . '">';
            
            // 可排序配置数据
            $sortable_data = array(
                'layout'        => $args['layout'],
                'sortable'      => $args['sortable'],
                'addable'       => $args['addable'],
                'removable'     => $args['removable'],
                'collapsible'   => $args['collapsible'],
                'showHandles'   => $args['show_handles'],
                'showNumbers'   => $args['show_numbers'],
                'minItems'      => $args['min_items'],
                'maxItems'      => $args['max_items'],
                'animation'     => $args['animation'],
                'keyboardNav'   => $args['keyboard_nav'],
                'gridColumns'   => $args['grid_columns'],
            );
            
            // 工具栏
            if ( $args['addable'] || $args['show_preview'] ) {
                echo '<div class="xun-sortable-toolbar mb-4 flex flex-wrap items-center justify-between gap-3">';
                
                // 添加按钮
                if ( $args['addable'] ) {
                    echo '<button type="button" class="xun-sortable-add-item inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">';
                    echo '<svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                    echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />';
                    echo '</svg>';
                    echo esc_html( $args['add_button_text'] );
                    echo '</button>';
                }
                
                // 预览切换
                if ( $args['show_preview'] ) {
                    echo '<button type="button" class="xun-sortable-toggle-preview inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">';
                    echo '<svg class="mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                    echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />';
                    echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />';
                    echo '</svg>';
                    echo '预览模式';
                    echo '</button>';
                }
                
                echo '</div>';
            }
            
            // 可排序容器
            echo '<div class="xun-sortable-container" data-sortable-config="' . esc_attr( json_encode( $sortable_data ) ) . '">';
            
            if ( ! empty( $sorted_fields ) ) {
                
                // 根据布局渲染项目
                $this->render_sortable_items( $sorted_fields, $args );
                
            } else {
                // 空状态
                echo '<div class="xun-sortable-empty text-center py-8 text-gray-500">';
                echo '<svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />';
                echo '</svg>';
                echo '<p class="mt-2 text-sm">' . esc_html( $args['empty_message'] ) . '</p>';
                if ( $args['addable'] ) {
                    echo '<button type="button" class="xun-sortable-add-item mt-3 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">';
                    echo esc_html( $args['add_button_text'] );
                    echo '</button>';
                }
                echo '</div>';
            }
            
            echo '</div>'; // 结束可排序容器
            
            // 预览面板
            if ( $args['show_preview'] ) {
                echo '<div class="xun-sortable-preview mt-4 p-4 bg-gray-50 rounded-lg hidden">';
                echo '<div class="text-sm font-medium text-gray-700 mb-2">排序预览</div>';
                echo '<div class="xun-sortable-preview-content text-sm text-gray-600"></div>';
                echo '</div>';
            }
            
            // 项目模板（隐藏，用于动态添加）
            if ( $args['addable'] ) {
                echo '<script type="text/template" class="xun-sortable-item-template">';
                $this->render_item_template( $args['fields'], $args );
                echo '</script>';
            }
            
            echo '</div>'; // 结束主容器
            
            // 输出后置内容
            echo $this->field_after();
        }
        
        /**
         * 根据保存的值排序字段
         * 
         * @since 1.0
         * 
         * @param array $fields 字段定义
         * @param mixed $value  保存的值
         * 
         * @return array 排序后的字段
         */
        private function sort_fields_by_value( $fields, $value ) {
            
            $sorted_fields = array();
            
            if ( ! empty( $value ) && is_array( $value ) ) {
                // 按保存的顺序排列
                foreach ( $value as $key => $field_value ) {
                    if ( isset( $fields[$key] ) ) {
                        $sorted_fields[$key] = $fields[$key];
                    }
                }
                
                // 添加新增的字段
                $diff = array_diff_key( $fields, $value );
                if ( ! empty( $diff ) ) {
                    $sorted_fields = array_merge( $sorted_fields, $diff );
                }
            } else {
                // 使用默认顺序
                $sorted_fields = $fields;
            }
            
            return $sorted_fields;
        }
        
        /**
         * 渲染可排序项目
         * 
         * @since 1.0
         * 
         * @param array $fields 字段数组
         * @param array $args   配置参数
         */
        private function render_sortable_items( $fields, $args ) {
            
            // 容器样式
            $items_classes = 'xun-sortable-items';
            
            switch ( $args['layout'] ) {
                case 'horizontal':
                    $items_classes .= ' flex flex-wrap gap-4';
                    break;
                case 'grid':
                    $cols = intval( $args['grid_columns'] );
                    switch ( $cols ) {
                        case 2:
                            $items_classes .= ' grid grid-cols-1 md:grid-cols-2 gap-4';
                            break;
                        case 3:
                            $items_classes .= ' grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';
                            break;
                        case 4:
                            $items_classes .= ' grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4';
                            break;
                        default:
                            $items_classes .= ' grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4';
                            break;
                    }
                    break;
                default: // vertical
                    $items_classes .= ' space-y-4';
                    break;
            }
            
            echo '<div class="' . $items_classes . '">';
            
            $index = 0;
            foreach ( $fields as $key => $field ) {
                $this->render_sortable_item( $key, $field, $args, $index );
                $index++;
            }
            
            echo '</div>';
        }
        
        /**
         * 渲染单个可排序项目
         * 
         * @since 1.0
         * 
         * @param string $key   字段键
         * @param array  $field 字段配置
         * @param array  $args  配置参数
         * @param int    $index 索引
         */
        private function render_sortable_item( $key, $field, $args, $index ) {
            
            // 获取字段值
            $field_default = isset( $this->field['default'][$key] ) ? $this->field['default'][$key] : '';
            $field_value = isset( $this->value[$key] ) ? $this->value[$key] : $field_default;
            $unique_id = ! empty( $this->unique ) ? $this->unique . '[' . $this->field['id'] . ']' : $this->field['id'];
            
            // 项目容器
            $item_classes = array(
                'xun-sortable-item',
                'relative',
                'bg-white',
                'border',
                'border-gray-200',
                'rounded-lg',
                'shadow-sm',
                'transition-all',
                'duration-200',
            );
            
            if ( $args['collapsible'] ) {
                $item_classes[] = 'xun-collapsible';
            }
            
            echo '<div class="' . implode( ' ', $item_classes ) . '" data-item-key="' . esc_attr( $key ) . '" data-item-index="' . esc_attr( $index ) . '">';
            
            // 项目头部
            if ( $args['show_handles'] || $args['show_numbers'] || $args['removable'] || $args['collapsible'] ) {
                echo '<div class="xun-sortable-item-header flex items-center justify-between p-3 border-b border-gray-200">';
                
                // 左侧：拖拽手柄和编号
                echo '<div class="flex items-center space-x-3">';
                
                if ( $args['show_handles'] && $args['sortable'] ) {
                    echo '<div class="xun-sortable-handle cursor-grab active:cursor-grabbing text-gray-400 hover:text-gray-600">';
                    echo '<svg class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                    echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16" />';
                    echo '</svg>';
                    echo '</div>';
                }
                
                if ( $args['show_numbers'] ) {
                    echo '<span class="xun-sortable-number inline-flex items-center justify-center w-6 h-6 text-xs font-medium text-gray-500 bg-gray-100 rounded-full">' . ( $index + 1 ) . '</span>';
                }
                
                // 字段标题
                if ( ! empty( $field['title'] ) ) {
                    echo '<h4 class="text-sm font-medium text-gray-900">' . esc_html( $field['title'] ) . '</h4>';
                }
                
                echo '</div>';
                
                // 右侧：操作按钮
                echo '<div class="flex items-center space-x-2">';
                
                if ( $args['collapsible'] ) {
                    echo '<button type="button" class="xun-sortable-toggle text-gray-400 hover:text-gray-600 focus:outline-none">';
                    echo '<svg class="w-4 h-4 transform transition-transform duration-200" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                    echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />';
                    echo '</svg>';
                    echo '</button>';
                }
                
                if ( $args['removable'] ) {
                    echo '<button type="button" class="xun-sortable-remove text-red-400 hover:text-red-600 focus:outline-none">';
                    echo '<svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">';
                    echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />';
                    echo '</svg>';
                    echo '</button>';
                }
                
                echo '</div>';
                
                echo '</div>';
            }
            
            // 项目内容
            echo '<div class="xun-sortable-item-content p-4">';
            
            // 渲染嵌套字段
            XUN::field( $field, $field_value, $unique_id, 'field/sortable' );
            
            echo '</div>';
            
            echo '</div>';
        }
        
        /**
         * 渲染项目模板
         * 
         * @since 1.0
         * 
         * @param array $fields 字段数组
         * @param array $args   配置参数
         */
        private function render_item_template( $fields, $args ) {
            // 这里渲染用于动态添加的项目模板
            // 实际实现会在JavaScript中处理
            echo '<!-- Item template will be handled by JavaScript -->';
        }
        
        /**
         * 加载字段资源
         * 
         * 加载可排序字段所需的JavaScript资源。
         * 
         * @since 1.0
         */
        public function enqueue() {
            
            // 加载自定义JavaScript
            wp_enqueue_script(
                'xun-field-sortable',
                XUN_Setup::$url . '/assets/js/fields/sortable.js',
                array( 'jquery', 'jquery-ui-sortable' ),
                XUN_Setup::$version,
                true
            );
            
            // 本地化脚本
            wp_localize_script( 'xun-field-sortable', 'xunSortable', array(
                'strings' => array(
                    'confirmRemove'   => '确定要删除这个项目吗？',
                    'addItem'         => '添加项目',
                    'removeItem'      => '删除项目',
                    'moveUp'          => '向上移动',
                    'moveDown'        => '向下移动',
                    'collapse'        => '折叠',
                    'expand'          => '展开',
                    'maxItemsReached' => '已达到最大项目数量限制',
                    'minItemsRequired' => '至少需要保留一个项目',
                ),
                'nonce' => wp_create_nonce( 'xun_sortable_nonce' ),
            ) );
        }
        
        /**
         * 验证和清理字段数据
         * 
         * 对可排序字段的数据进行验证和清理。
         * 
         * @since 1.0
         * 
         * @param mixed $value 要验证的值
         * 
         * @return mixed 清理后的数据
         */
        public function validate( $value ) {
            
            $args = wp_parse_args( $this->field, array(
                'fields'    => array(),
                'min_items' => 0,
                'max_items' => 0,
            ) );
            
            if ( ! is_array( $value ) ) {
                return array();
            }
            
            $validated = array();
            $field_definitions = array();
            
            // 构建字段定义映射
            foreach ( $args['fields'] as $field ) {
                $field_definitions[$field['id']] = $field;
            }
            
            // 验证每个字段值
            foreach ( $value as $key => $field_value ) {
                if ( isset( $field_definitions[$key] ) ) {
                    $field_def = $field_definitions[$key];
                    
                    // 根据字段类型进行验证
                    $validated_value = $this->validate_field_value( $field_value, $field_def );
                    $validated[$key] = $validated_value;
                }
            }
            
            // 检查数量限制
            if ( $args['min_items'] > 0 && count( $validated ) < $args['min_items'] ) {
                // 可以添加默认项目或返回错误
            }
            
            if ( $args['max_items'] > 0 && count( $validated ) > $args['max_items'] ) {
                $validated = array_slice( $validated, 0, $args['max_items'], true );
            }
            
            return $validated;
        }
        
        /**
         * 验证单个字段值
         * 
         * @since 1.0
         * 
         * @param mixed $value 字段值
         * @param array $field 字段定义
         * 
         * @return mixed 验证后的值
         */
        private function validate_field_value( $value, $field ) {
            
            // 根据字段类型进行相应的验证
            switch ( $field['type'] ) {
                case 'text':
                case 'textarea':
                    return sanitize_text_field( $value );
                    
                case 'email':
                    return sanitize_email( $value );
                    
                case 'url':
                    return esc_url_raw( $value );
                    
                case 'number':
                    return intval( $value );
                    
                case 'select':
                case 'radio':
                    // 验证值是否在选项中
                    if ( isset( $field['options'] ) && is_array( $field['options'] ) ) {
                        return in_array( $value, array_keys( $field['options'] ) ) ? $value : '';
                    }
                    return sanitize_text_field( $value );
                    
                case 'checkbox':
                    return is_array( $value ) ? array_map( 'sanitize_text_field', $value ) : array();
                    
                default:
                    return sanitize_text_field( $value );
            }
        }
    }
}
