/**
 * Xun Framework 文档系统 - TailwindCSS 4 输入文件
 *
 * 这个文件是TailwindCSS 4的主要输入文件，包含了框架的基础样式、
 * 组件样式和工具类。与框架保持一致的深色主题设计。
 */

/* 导入 TailwindCSS 4 */
@import "tailwindcss";

/* 自定义CSS变量 - 与框架保持一致 */
:root {
  /* 主色调 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  
  /* 灰色调 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* 成功色 */
  --success-500: #10b981;
  --success-600: #059669;
  
  /* 警告色 */
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  
  /* 错误色 */
  --error-500: #ef4444;
  --error-600: #dc2626;
}

/* 深色主题变量 */
[data-theme="dark"] {
  --bg-primary: var(--gray-900);
  --bg-secondary: var(--gray-800);
  --bg-tertiary: var(--gray-700);
  --text-primary: var(--gray-100);
  --text-secondary: var(--gray-300);
  --text-tertiary: var(--gray-400);
  --border-color: var(--gray-600);
}

/* 浅色主题变量 */
[data-theme="light"] {
  --bg-primary: var(--gray-50);
  --bg-secondary: #ffffff;
  --bg-tertiary: var(--gray-100);
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-tertiary: var(--gray-500);
  --border-color: var(--gray-200);
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 代码字体 */
code, pre, .font-mono {
  font-family: 'JetBrains Mono', 'Fira Code', Consolas, 'Liberation Mono', Menlo, monospace;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* 选择文本样式 */
::selection {
  background-color: var(--primary-200);
  color: var(--primary-900);
}

[data-theme="dark"] ::selection {
  background-color: var(--primary-800);
  color: var(--primary-100);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* 链接样式 */
a {
  color: var(--primary-600);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-700);
}

[data-theme="dark"] a {
  color: var(--primary-400);
}

[data-theme="dark"] a:hover {
  color: var(--primary-300);
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 0.5em;
}

/* 代码块样式 */
pre {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  overflow-x: auto;
  font-size: 0.875rem;
  line-height: 1.5;
}

code {
  background-color: var(--bg-tertiary);
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
  font-size: 0.875em;
}

pre code {
  background: none;
  padding: 0;
  border-radius: 0;
}

/* 按钮基础样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  font-size: 0.875rem;
}

.btn-primary {
  background-color: var(--primary-600);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-700);
  color: white;
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

/* 卡片样式 */
.card {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.card:hover {
  border-color: var(--primary-300);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-full {
    width: 100% !important;
  }
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载动画 */
.loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 按钮悬浮效果 */
.btn-hover {
  transition: all 0.3s ease;
  transform: translateY(0);
}

.btn-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.btn-primary-hover {
  background: linear-gradient(135deg, #2563eb, #1d4ed8);
  transition: all 0.3s ease;
  transform: translateY(0);
}

.btn-primary-hover:hover {
  background: linear-gradient(135deg, #1d4ed8, #1e40af);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(37, 99, 235, 0.3);
}

.btn-secondary-hover {
  transition: all 0.3s ease;
  transform: translateY(0);
}

.btn-secondary-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  background-color: #f9fafb;
}

/* 导航菜单样式 */
.nav-item {
  position: relative;
  display: inline-block;
}

.nav-item::before {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: #2563eb;
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-item:hover::before {
  width: 80%;
}

.nav-item.active {
  color: #2563eb !important;
  background-color: transparent !important;
  box-shadow: none !important;
}

.nav-item.active::before {
  width: 80%;
}

/* 导航菜单悬浮效果 */
.nav-item:hover {
  background-color: #eff6ff !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  color: #2563eb !important;
}

/* 确保所有特性卡片颜色类生成 */
.feature-colors {
  /* Blue */
  background: linear-gradient(to bottom right, #eff6ff, #dbeafe);
  border-color: #bfdbfe;
  background: linear-gradient(to bottom right, #3b82f6, #2563eb);

  /* Purple */
  background: linear-gradient(to bottom right, #faf5ff, #f3e8ff);
  border-color: #e9d5ff;
  background: linear-gradient(to bottom right, #a855f7, #9333ea);

  /* Green */
  background: linear-gradient(to bottom right, #f0fdf4, #dcfce7);
  border-color: #bbf7d0;
  background: linear-gradient(to bottom right, #22c55e, #16a34a);

  /* Yellow */
  background: linear-gradient(to bottom right, #fefce8, #fef3c7);
  border-color: #fde68a;
  background: linear-gradient(to bottom right, #eab308, #ca8a04);

  /* Indigo */
  background: linear-gradient(to bottom right, #eef2ff, #e0e7ff);
  border-color: #c7d2fe;
  background: linear-gradient(to bottom right, #6366f1, #4f46e5);

  /* Red */
  background: linear-gradient(to bottom right, #fef2f2, #fee2e2);
  border-color: #fecaca;
  background: linear-gradient(to bottom right, #ef4444, #dc2626);
}

/* 代码高亮容器样式 */
.bg-gray-900 pre[class*="language-"] {
  background: transparent !important;
  margin: 0 !important;
  padding: 0 !important;
  border-radius: 0 !important;
}

/* 字段类型页面样式 */
/* 字段卡片展开动画 */
.field-content {
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}

.field-content.hidden {
  display: none !important;
}

/* 展开按钮焦点样式 */
.field-expand-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 4px;
}

/* 搜索框焦点样式 */
#field-search:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 分类标签焦点样式 */
.category-tab:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
}

/* 减少动画对于偏好减少动画的用户 */
@media (prefers-reduced-motion: reduce) {
  .field-content,
  .field-expand-btn svg {
    transition: none;
  }
}

/* API参考页面样式 */
/* 方法详情展开动画 */
.method-details {
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}

.method-details.hidden {
  display: none !important;
}

/* 方法展开按钮焦点样式 */
.method-expand-btn:focus {
  outline: 2px solid #3b82f6;
  outline-offset: 2px;
  border-radius: 4px;
}

/* API搜索框焦点样式 */
#api-search:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 侧边栏导航样式 */
.api-nav-section a.text-blue-600 {
  background-color: rgb(239 246 255);
  color: rgb(37 99 235);
}

/* 代码块样式优化 */
.api-method pre,
.api-function pre,
.hook-item pre {
  background: #1f2937 !important;
  border-radius: 0.5rem;
  font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
}

.api-method code,
.api-function code,
.hook-item code {
  color: #10b981 !important;
  font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
}

/* 参数列表样式 */
.api-method .flex.items-start,
.api-function .flex.items-start,
.hook-item .flex.items-start {
  padding: 0.5rem 0;
  border-bottom: 1px solid #f3f4f6;
}

.api-method .flex.items-start:last-child,
.api-function .flex.items-start:last-child,
.hook-item .flex.items-start:last-child {
  border-bottom: none;
}

/* 滚动行为优化 */
html {
  scroll-behavior: smooth;
}

/* 锚点偏移 */
.api-section {
  scroll-margin-top: 2rem;
}

.api-class {
  scroll-margin-top: 2rem;
}

/* 示例代码页面样式 */
/* 示例内容展开动画 */
.example-content {
  transition: all 0.3s ease-in-out;
  overflow: hidden;
}

.example-content.hidden {
  display: none !important;
}

/* 示例展开按钮焦点样式 */
.example-expand-btn:focus {
  outline: 2px solid #10b981;
  outline-offset: 2px;
  border-radius: 4px;
}

/* 示例搜索框焦点样式 */
#example-search:focus {
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* 示例卡片悬停效果 */
.example-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.example-category-card:hover {
  cursor: pointer;
}

/* 代码块样式优化 */
.example-card pre {
  background: #1f2937 !important;
  border-radius: 0;
  font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
  font-size: 0.875rem;
  line-height: 1.5;
}

.example-card code {
  color: #e5e7eb !important;
  font-family: 'JetBrains Mono', 'Fira Code', Consolas, monospace;
}

/* 特性标签样式 */
.example-card .inline-flex {
  transition: all 0.2s ease-in-out;
}

.example-card .inline-flex:hover {
  transform: scale(1.05);
}

/* 使用说明框样式 */
.example-card .bg-blue-50 code {
  background: rgba(59, 130, 246, 0.1) !important;
  color: #1e40af !important;
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
}

/* 复制按钮样式 */
.copy-button {
  transition: all 0.2s ease-in-out;
}

.copy-button:hover {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 0.25rem;
  padding: 0.25rem 0.5rem;
}

/* 键盘快捷键样式 */
kbd {
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
  font-size: 0.75rem;
  font-weight: 500;
  line-height: 1;
  color: #374151;
  background-color: #f9fafb;
  border: 1px solid #d1d5db;
  border-radius: 0.25rem;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* 滚动行为优化 */
.example-section {
  scroll-margin-top: 2rem;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .example-card {
    margin-bottom: 1rem;
  }

  .example-card pre {
    font-size: 0.75rem;
  }

  .copy-button {
    font-size: 0.75rem;
    padding: 0.25rem;
  }
}
