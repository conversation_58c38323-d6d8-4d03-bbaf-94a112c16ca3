/**
 * Xun Framework - Number Field JavaScript
 * 
 * 为数字输入字段提供现代化的交互功能，包括：
 * - 实时验证和错误提示
 * - 键盘快捷键支持
 * - 增减按钮功能
 * - 数字格式化
 * - 范围指示器
 * - 无障碍访问支持
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

(function($) {
    'use strict';

    /**
     * 数字字段类
     */
    class XunNumberField {

        constructor($container) {
            this.$container = $container;
            this.$input = $container.find('.xun-number-input');
            this.$errorMessage = $container.find('.xun-number-error-message');
            this.$rangeProgress = $container.find('.xun-number-range-progress');
            this.$incrementBtn = $container.find('.xun-number-increment');
            this.$decrementBtn = $container.find('.xun-number-decrement');

            // 滑块相关元素
            this.$slider = $container.find('.xun-number-range-slider');
            this.$sliderTrack = $container.find('.xun-slider-track');
            this.$sliderFill = $container.find('.xun-slider-fill');
            this.$sliderHandle = $container.find('.xun-slider-handle');
            this.$sliderValue = $container.find('.xun-slider-value');

            // 获取配置
            this.config = {
                fieldId: $container.data('field-id'),
                mode: this.$input.data('mode') || 'basic',
                precision: parseInt(this.$input.data('precision')) || 0,
                thousandSep: this.$input.data('thousand-sep') === 'true',
                formatDisplay: this.$input.data('format-display') === 'true',
                validation: this.$input.data('validation') === 'true',
                keyboardNav: this.$input.data('keyboard-nav') === 'true',
                autoSelect: this.$input.data('auto-select') === 'true',
                min: this.$input.data('min'),
                max: this.$input.data('max'),
                step: parseFloat(this.$input.data('step')) || 1
            };

            this.lastValidValue = this.$input.val();
            this.isFormatted = false;

            this.init();
        }

        init() {
            this.bindEvents();
            this.updateRangeIndicator();
            this.validateValue();
        }

        bindEvents() {
            // 输入框事件
            this.$input
                .on('input', this.handleInput.bind(this))
                .on('change', this.handleChange.bind(this))
                .on('blur', this.handleBlur.bind(this))
                .on('focus', this.handleFocus.bind(this))
                .on('keydown', this.handleKeydown.bind(this))
                .on('wheel', this.handleWheel.bind(this));

            // 增减按钮事件
            this.$incrementBtn.on('click', this.increment.bind(this));
            this.$decrementBtn.on('click', this.decrement.bind(this));

            // 防止按钮获得焦点
            this.$incrementBtn.add(this.$decrementBtn).on('mousedown', function(e) {
                e.preventDefault();
            });

            // 滑块事件绑定
            if (this.$slider.length > 0) {
                this.bindSliderEvents();
            }
        }

        handleInput(e) {
            if (this.config.validation) {
                this.validateValue();
            }
            this.updateRangeIndicator();
        }

        handleChange(e) {
            this.formatValue();
            this.validateValue();
            this.updateRangeIndicator();
        }

        handleBlur(e) {
            if (this.config.formatDisplay && !this.isFormatted) {
                this.formatValue();
            }
            this.validateValue();
        }

        handleFocus(e) {
            if (this.config.autoSelect) {
                setTimeout(() => {
                    this.$input[0].select();
                }, 10);
            }

            // 如果值已格式化，恢复原始数值
            if (this.isFormatted) {
                this.unformatValue();
            }
        }

        handleKeydown(e) {
            if (!this.config.keyboardNav) return;

            const currentValue = parseFloat(this.$input.val()) || 0;
            let newValue = currentValue;

            switch (e.key) {
                case 'ArrowUp':
                    e.preventDefault();
                    newValue = this.adjustValue(currentValue, this.config.step);
                    break;
                case 'ArrowDown':
                    e.preventDefault();
                    newValue = this.adjustValue(currentValue, -this.config.step);
                    break;
                case 'PageUp':
                    e.preventDefault();
                    newValue = this.adjustValue(currentValue, this.config.step * 10);
                    break;
                case 'PageDown':
                    e.preventDefault();
                    newValue = this.adjustValue(currentValue, -this.config.step * 10);
                    break;
                case 'Home':
                    if (this.config.min !== undefined && this.config.min !== '') {
                        e.preventDefault();
                        newValue = parseFloat(this.config.min);
                    }
                    break;
                case 'End':
                    if (this.config.max !== undefined && this.config.max !== '') {
                        e.preventDefault();
                        newValue = parseFloat(this.config.max);
                    }
                    break;
                default:
                    return;
            }

            if (newValue !== currentValue) {
                this.$input.val(newValue).trigger('input');
            }
        }

        handleWheel(e) {
            if (!this.$input.is(':focus')) return;

            e.preventDefault();
            const currentValue = parseFloat(this.$input.val()) || 0;
            const delta = e.originalEvent.deltaY > 0 ? -this.config.step : this.config.step;
            const newValue = this.adjustValue(currentValue, delta);

            this.$input.val(newValue).trigger('input');
        }

        increment() {
            const currentValue = parseFloat(this.$input.val()) || 0;
            const newValue = this.adjustValue(currentValue, this.config.step);
            this.$input.val(newValue).trigger('input').focus();
        }

        decrement() {
            const currentValue = parseFloat(this.$input.val()) || 0;
            const newValue = this.adjustValue(currentValue, -this.config.step);
            this.$input.val(newValue).trigger('input').focus();
        }

        adjustValue(currentValue, delta) {
            let newValue = currentValue + delta;

            // 应用范围限制
            if (this.config.min !== undefined && this.config.min !== '') {
                newValue = Math.max(newValue, parseFloat(this.config.min));
            }

            if (this.config.max !== undefined && this.config.max !== '') {
                newValue = Math.min(newValue, parseFloat(this.config.max));
            }

            // 应用精度
            if (this.config.precision > 0) {
                newValue = parseFloat(newValue.toFixed(this.config.precision));
            } else {
                newValue = Math.round(newValue);
            }

            return newValue;
        }

        validateValue() {
            const value = this.$input.val();
            const errors = [];

            // 清除之前的错误状态
            this.clearError();

            if (value === '') {
                if (this.$input.prop('required')) {
                    errors.push(xunNumberField.i18n.required_field);
                }
            } else {
                // 验证数字格式
                const numericValue = this.parseNumber(value);
                if (isNaN(numericValue)) {
                    errors.push(xunNumberField.i18n.invalid_number);
                } else {
                    // 验证范围
                    if (this.config.min !== undefined && this.config.min !== '' && numericValue < parseFloat(this.config.min)) {
                        errors.push(xunNumberField.i18n.below_minimum.replace('%s', this.config.min));
                    }

                    if (this.config.max !== undefined && this.config.max !== '' && numericValue > parseFloat(this.config.max)) {
                        errors.push(xunNumberField.i18n.above_maximum.replace('%s', this.config.max));
                    }

                    // 验证步长
                    if (this.config.step && this.config.step !== 'any') {
                        const min = this.config.min !== undefined && this.config.min !== '' ? parseFloat(this.config.min) : 0;
                        const remainder = (numericValue - min) % this.config.step;
                        if (Math.abs(remainder) > 0.0001) {
                            errors.push(xunNumberField.i18n.invalid_step.replace('%s', this.config.step));
                        }
                    }

                    // 验证小数位数
                    if (this.config.precision >= 0) {
                        const decimalPlaces = (value.split('.')[1] || '').length;
                        if (decimalPlaces > this.config.precision) {
                            errors.push(xunNumberField.i18n.decimal_places.replace('%d', this.config.precision));
                        }
                    }
                }
            }

            if (errors.length > 0) {
                this.showError(errors[0]);
                return false;
            }

            this.lastValidValue = value;
            return true;
        }

        formatValue() {
            if (!this.config.formatDisplay) return;

            const value = this.$input.val();
            const numericValue = this.parseNumber(value);

            if (!isNaN(numericValue)) {
                let formatted = numericValue.toFixed(this.config.precision);

                // 添加千分位分隔符
                if (this.config.thousandSep) {
                    formatted = this.addThousandSeparator(formatted);
                }

                // 根据模式添加符号
                switch (this.config.mode) {
                    case 'currency':
                        formatted = xunNumberField.settings.currency_symbol + formatted;
                        break;
                    case 'percentage':
                        formatted = formatted + xunNumberField.settings.percentage_symbol;
                        break;
                }

                this.$input.val(formatted);
                this.isFormatted = true;
            }
        }

        unformatValue() {
            if (!this.isFormatted) return;

            const value = this.$input.val();
            let unformatted = value;

            // 移除符号和分隔符
            unformatted = unformatted.replace(/[$%,]/g, '');

            this.$input.val(unformatted);
            this.isFormatted = false;
        }

        parseNumber(value) {
            if (typeof value === 'number') return value;
            
            // 移除格式化字符
            const cleaned = value.toString().replace(/[$%,]/g, '');
            return parseFloat(cleaned);
        }

        addThousandSeparator(value) {
            const parts = value.split('.');
            parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, xunNumberField.settings.thousand_separator);
            return parts.join(xunNumberField.settings.decimal_separator);
        }

        updateRangeIndicator() {
            // 更新旧式进度条（如果存在）
            if (this.$rangeProgress.length > 0) {
                const value = this.parseNumber(this.$input.val());
                if (isNaN(value)) {
                    this.$rangeProgress.css('width', '0%');
                    return;
                }

                const min = parseFloat(this.config.min) || 0;
                const max = parseFloat(this.config.max) || 100;
                const percentage = Math.max(0, Math.min(100, ((value - min) / (max - min)) * 100));

                this.$rangeProgress.css('width', percentage + '%');
            }

            // 更新新式滑块（如果存在）
            if (this.$slider.length > 0) {
                this.updateSliderPosition();
                this.updateSliderValue();
            }
        }

        showError(message) {
            this.$errorMessage.text(message).removeClass('hidden');
            // 使用outline样式显示错误状态
            const $inputContainer = this.$container.find('.xun-number-input-container');
            $inputContainer.addClass('outline-red-500 focus-within:outline-red-500');
            // 移除正常状态的outline颜色类
            $inputContainer.removeClass('outline-gray-300 focus-within:outline-indigo-600');
        }

        clearError() {
            this.$errorMessage.addClass('hidden');
            // 移除错误样式
            const $inputContainer = this.$container.find('.xun-number-input-container');
            $inputContainer.removeClass('outline-red-500 focus-within:outline-red-500');
            // 恢复正常状态的outline颜色类
            $inputContainer.addClass('outline-gray-300 focus-within:outline-indigo-600');
        }

        /**
         * 绑定滑块事件
         */
        bindSliderEvents() {
            let isDragging = false;

            // 鼠标/触摸事件
            this.$sliderHandle.on('mousedown touchstart', (e) => {
                e.preventDefault();
                isDragging = true;

                // 添加拖动状态类
                this.$slider.addClass('dragging');

                // 立即更新一次位置
                this.handleSliderDrag(e);

                $(document).on('mousemove.slider touchmove.slider', (e) => {
                    if (!isDragging) return;
                    this.handleSliderDrag(e);
                });

                $(document).on('mouseup.slider touchend.slider', () => {
                    isDragging = false;
                    this.$slider.removeClass('dragging');
                    $(document).off('.slider');
                });
            });

            // 点击轨道跳转
            this.$sliderTrack.on('click', (e) => {
                if (e.target === this.$sliderHandle[0]) return;
                this.handleSliderClick(e);
            });

            // 键盘导航
            this.$sliderHandle.on('keydown', (e) => {
                this.handleSliderKeydown(e);
            });
        }

        /**
         * 处理滑块拖动
         */
        handleSliderDrag(e) {
            e.preventDefault();

            const currentX = e.type === 'mousedown' || e.type === 'mousemove' ? e.clientX : e.originalEvent.touches[0].clientX;
            const rect = this.$sliderTrack[0].getBoundingClientRect();
            const relativeX = currentX - rect.left;
            const percent = Math.max(0, Math.min(1, relativeX / rect.width));

            const range = this.config.max - this.config.min;
            const newValue = this.config.min + (percent * range);

            this.updateValueFromSlider(this.constrainValue(newValue));
        }

        /**
         * 处理滑块点击
         */
        handleSliderClick(e) {
            const rect = this.$sliderTrack[0].getBoundingClientRect();
            const clickX = e.clientX - rect.left;
            const percent = clickX / rect.width;
            const range = this.config.max - this.config.min;
            const newValue = this.config.min + (percent * range);

            this.updateValueFromSlider(this.constrainValue(newValue));
        }

        /**
         * 处理滑块键盘导航
         */
        handleSliderKeydown(e) {
            let newValue = parseFloat(this.$input.val()) || 0;
            const step = this.config.step;

            switch (e.key) {
                case 'ArrowLeft':
                case 'ArrowDown':
                    e.preventDefault();
                    newValue -= step;
                    break;
                case 'ArrowRight':
                case 'ArrowUp':
                    e.preventDefault();
                    newValue += step;
                    break;
                case 'Home':
                    e.preventDefault();
                    newValue = this.config.min;
                    break;
                case 'End':
                    e.preventDefault();
                    newValue = this.config.max;
                    break;
                default:
                    return;
            }

            this.updateValueFromSlider(this.constrainValue(newValue));
        }

        /**
         * 约束数值在有效范围内
         */
        constrainValue(value) {
            let constrainedValue = value;

            if (this.config.min !== undefined && this.config.min !== '') {
                constrainedValue = Math.max(constrainedValue, parseFloat(this.config.min));
            }

            if (this.config.max !== undefined && this.config.max !== '') {
                constrainedValue = Math.min(constrainedValue, parseFloat(this.config.max));
            }

            // 应用步长
            if (this.config.step && this.config.step !== 'any') {
                const min = this.config.min !== undefined && this.config.min !== '' ? parseFloat(this.config.min) : 0;
                const steps = Math.round((constrainedValue - min) / this.config.step);
                constrainedValue = min + (steps * this.config.step);
            }

            return constrainedValue;
        }

        /**
         * 从滑块更新数值
         */
        updateValueFromSlider(value) {
            this.$input.val(value).trigger('input');
            this.updateSliderPosition();
            this.updateSliderValue();
        }

        /**
         * 更新滑块位置
         */
        updateSliderPosition() {
            if (this.$slider.length === 0) return;

            const value = parseFloat(this.$input.val()) || 0;
            const min = parseFloat(this.config.min) || 0;
            const max = parseFloat(this.config.max) || 100;
            const percent = Math.max(0, Math.min(100, ((value - min) / (max - min)) * 100));

            this.$sliderFill.css('width', percent + '%');
            this.$sliderHandle.css('left', percent + '%');
            this.$sliderHandle.attr('aria-valuenow', value);
        }

        /**
         * 更新滑块显示值
         */
        updateSliderValue() {
            if (this.$sliderValue.length === 0) return;

            const value = parseFloat(this.$input.val()) || 0;
            this.$sliderValue.text(value);
        }
    }

    /**
     * 初始化所有数字字段
     */
    function initNumberFields() {
        $('.xun-number-field-container').each(function() {
            const $container = $(this);
            if (!$container.data('xun-number-initialized')) {
                new XunNumberField($container);
                $container.data('xun-number-initialized', true);
            }
        });
    }

    // 页面加载完成后初始化
    $(document).ready(function() {
        initNumberFields();
    });

    // 支持动态添加的字段
    $(document).on('xun-field-added', function() {
        initNumberFields();
    });

})(jQuery);
