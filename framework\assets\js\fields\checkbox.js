/**
 * Checkbox Field JavaScript - 基础字段增强
 *
 * 提供checkbox字段的增强功能：搜索、全选、验证、计数等
 *
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.1.0
 */

(function($) {
    'use strict';

    /**
     * 现代Checkbox字段类
     */
    class XunCheckboxField {
        constructor(container) {
            this.$container = $(container);
            this.fieldId = this.$container.data('field-id');
            this.options = [];
            this.selectedValues = [];
            this.filteredOptions = [];

            this.init();
        }

        /**
         * 初始化
         */
        init() {
            this.cacheElements();
            this.bindEvents();
            this.initializeOptions();
            this.updateState();
        }

        /**
         * 缓存DOM元素
         */
        cacheElements() {
            this.$toolbar = this.$container.find('.xun-checkbox-toolbar');
            this.$searchInput = this.$container.find('.xun-checkbox-search');
            this.$selectAllBtn = this.$container.find('.xun-checkbox-select-all');
            this.$clearAllBtn = this.$container.find('.xun-checkbox-clear-all');
            this.$countDisplay = this.$container.find('.xun-checkbox-count');
            this.$optionsContainer = this.$container.find('.xun-checkbox-options');
            this.$validationMsg = this.$container.find('.xun-checkbox-validation');
            this.$checkboxInputs = this.$container.find('.xun-checkbox-input');
            this.$hiddenInputs = this.$container.find('.xun-checkbox-hidden-input');
            this.$emptyInput = this.$container.find('.xun-checkbox-empty-input');
        }

        /**
         * 绑定事件
         */
        bindEvents() {
            // 搜索功能
            this.$searchInput.on('input', this.handleSearch.bind(this));

            // 全选/清空
            this.$selectAllBtn.on('click', this.selectAll.bind(this));
            this.$clearAllBtn.on('click', this.clearAll.bind(this));

            // 选项变化
            this.$container.on('change', '.xun-checkbox-input', this.handleOptionChange.bind(this));

            // 选项点击（用于整个选项区域）
            this.$container.on('click', '.xun-checkbox-option', this.handleOptionClick.bind(this));

            // 无障碍：键盘导航支持
            this.$container.on('keydown', '.xun-checkbox-option[role="button"]', this.handleKeyboardNavigation.bind(this));
        }

        /**
         * 初始化选项数据
         */
        initializeOptions() {
            this.options = [];
            this.$container.find('.xun-checkbox-option').each((_, element) => {
                const $option = $(element);
                const $input = $option.find('.xun-checkbox-input');

                if ($input.length) {
                    const option = {
                        element: $option,
                        input: $input,
                        value: $input.val(),
                        label: $input.next('label').text() || $option.find('label').text(),
                        checked: $input.is(':checked'),
                        disabled: $input.is(':disabled'),
                        visible: true
                    };

                    this.options.push(option);
                }
            });

            this.filteredOptions = [...this.options];
            this.updateSelectedValues();
        }

        /**
         * 处理搜索
         */
        handleSearch(e) {
            const searchTerm = $(e.target).val().toLowerCase();

            this.filteredOptions = this.options.filter(option => {
                const matches = option.label.toLowerCase().includes(searchTerm);
                option.visible = matches;

                // 显示/隐藏选项
                if (matches) {
                    option.element.show();
                } else {
                    option.element.hide();
                }

                return matches;
            });

            this.updateState();
        }

        /**
         * 全选
         */
        selectAll() {
            this.filteredOptions.forEach(option => {
                if (!option.disabled && !option.checked) {
                    option.input.prop('checked', true);
                    option.checked = true;
                    this.updateCheckmarkIcon(option.input, true);
                }
            });

            this.updateSelectedValues();
            this.updateState();
            this.validate();
            this.triggerChange();
        }

        /**
         * 清空选择
         */
        clearAll() {
            this.filteredOptions.forEach(option => {
                if (!option.disabled && option.checked) {
                    option.input.prop('checked', false);
                    option.checked = false;
                }
            });

            this.updateSelectedValues();
            this.updateState();
            this.validate();
            this.triggerChange();
        }

        /**
         * 处理选项变化
         */
        handleOptionChange(e) {
            const $input = $(e.target);
            const value = $input.val();
            const isChecked = $input.is(':checked');

            // 更新选项状态
            const option = this.options.find(opt => opt.value === value);
            if (option) {
                option.checked = isChecked;
            }

            // 更新SVG图标显示
            this.updateCheckmarkIcon($input, isChecked);

            // 更新选中值
            this.updateSelectedValues();

            // 更新ARIA状态
            const $option = $input.closest('.xun-checkbox-option');
            this.updateAriaStates($option, $input);

            // 更新UI状态
            this.updateState();

            // 验证
            this.validate();

            // 触发自定义事件
            this.triggerChange();
        }

        /**
         * 更新勾号图标显示
         */
        updateCheckmarkIcon($input, isChecked) {
            const $checkmark = $input.siblings('svg').find('.xun-checkbox-checkmark');
            if ($checkmark.length) {
                if (isChecked) {
                    $checkmark.removeClass('opacity-0').addClass('opacity-100');
                } else {
                    $checkmark.removeClass('opacity-100').addClass('opacity-0');
                }
            }
        }

        /**
         * 处理选项点击（用于整个选项区域） - 无障碍增强
         */
        handleOptionClick(e) {
            // 如果点击的是checkbox本身，不需要处理（避免重复触发）
            if ($(e.target).is('input[type="checkbox"]')) {
                return;
            }

            const $option = $(e.currentTarget);
            const $input = $option.find('.xun-checkbox-input');

            // 检查是否禁用
            if ($input.length && !$input.is(':disabled') && $option.attr('aria-disabled') !== 'true') {
                e.preventDefault();
                $input.prop('checked', !$input.is(':checked')).trigger('change');

                // 更新ARIA状态
                this.updateAriaStates($option, $input);
            }
        }

        /**
         * 更新选中值
         */
        updateSelectedValues() {
            this.selectedValues = [];
            this.options.forEach(option => {
                option.checked = option.input.is(':checked');
                if (option.checked) {
                    this.selectedValues.push(option.value);
                }
            });

            // 更新隐藏输入字段
            this.updateHiddenInputs();
        }

        /**
         * 更新隐藏输入字段
         */
        updateHiddenInputs() {
            // 移除现有的隐藏输入
            this.$hiddenInputs.remove();
            this.$emptyInput.remove();

            if (this.selectedValues.length > 0) {
                // 添加选中值的隐藏输入
                this.selectedValues.forEach(value => {
                    const $hidden = $('<input type="hidden" class="xun-checkbox-hidden-input">');
                    $hidden.attr('name', this.getFieldName() + '[]');
                    $hidden.val(value);
                    this.$container.append($hidden);
                });
            } else {
                // 添加空值输入以确保字段被提交
                const $empty = $('<input type="hidden" class="xun-checkbox-empty-input">');
                $empty.attr('name', this.getFieldName());
                $empty.val('');
                this.$container.append($empty);
            }

            // 重新缓存隐藏输入
            this.$hiddenInputs = this.$container.find('.xun-checkbox-hidden-input');
            this.$emptyInput = this.$container.find('.xun-checkbox-empty-input');
        }

        /**
         * 获取字段名称
         */
        getFieldName() {
            // 从第一个checkbox获取name属性，去掉[]后缀
            const firstName = this.$checkboxInputs.first().attr('name');
            return firstName ? firstName.replace('[]', '') : '';
        }

        /**
         * 更新UI状态
         */
        updateState() {
            const selectedCount = this.selectedValues.length;
            const totalCount = this.filteredOptions.length;
            const allSelected = selectedCount === totalCount && totalCount > 0;
            const noneSelected = selectedCount === 0;

            // 更新计数显示
            if (this.$countDisplay.length) {
                this.$countDisplay.html(
                    '已选择 <span class="font-semibold text-indigo-600">' + selectedCount + '</span> / ' + this.options.length + ' 项'
                );
            }

            // 更新全选/清空按钮
            if (allSelected) {
                this.$selectAllBtn.hide();
                this.$clearAllBtn.show();
            } else if (noneSelected) {
                this.$selectAllBtn.show();
                this.$clearAllBtn.hide();
            } else {
                this.$selectAllBtn.show();
                this.$clearAllBtn.show();
            }
        }

        /**
         * 验证
         */
        validate() {
            const selectedCount = this.selectedValues.length;
            const minRequired = parseInt(this.$container.data('min-required')) || 0;
            const maxAllowed = parseInt(this.$container.data('max-allowed')) || 0;

            let isValid = true;
            let message = '';

            // 检查最小选择数量
            if (minRequired > 0 && selectedCount < minRequired) {
                isValid = false;
                message = (xunCheckbox.messages.minRequired || '至少需要选择 %d 项').replace('%d', minRequired);
            }

            // 检查最大选择数量
            if (maxAllowed > 0 && selectedCount > maxAllowed) {
                isValid = false;
                message = (xunCheckbox.messages.maxExceeded || '最多只能选择 %d 项').replace('%d', maxAllowed);
            }

            // 显示验证消息
            if (isValid) {
                this.$validationMsg.addClass('hidden').text('');
            } else {
                this.$validationMsg.removeClass('hidden').text(message);
            }

            return isValid;
        }

        /**
         * 触发变化事件
         */
        triggerChange() {
            this.$container.trigger('xun:checkbox:change', {
                selectedValues: this.selectedValues,
                selectedCount: this.selectedValues.length,
                totalCount: this.options.length
            });
        }

        /**
         * 处理键盘导航 - 无障碍支持
         */
        handleKeyboardNavigation(e) {
            const $option = $(e.currentTarget);
            const $input = $option.find('.xun-checkbox-input');

            // 空格键或回车键切换选中状态
            if (e.keyCode === 32 || e.keyCode === 13) {
                e.preventDefault();

                // 如果禁用，不处理
                if ($input.is(':disabled') || $option.attr('aria-disabled') === 'true') {
                    return;
                }

                $input.prop('checked', !$input.is(':checked')).trigger('change');
                this.updateAriaStates($option, $input);
            }

            // 方向键导航
            else if (e.keyCode === 38 || e.keyCode === 40) { // 上下箭头
                e.preventDefault();
                const $options = this.$container.find('.xun-checkbox-option[role="button"]:visible');
                const currentIndex = $options.index($option);
                let nextIndex;

                if (e.keyCode === 38) { // 上箭头
                    nextIndex = currentIndex > 0 ? currentIndex - 1 : $options.length - 1;
                } else { // 下箭头
                    nextIndex = currentIndex < $options.length - 1 ? currentIndex + 1 : 0;
                }

                $options.eq(nextIndex).focus();
            }
        }

        /**
         * 更新ARIA状态 - 无障碍支持
         */
        updateAriaStates($option, $input) {
            if ($option && $option.attr('role') === 'button') {
                const isChecked = $input ? $input.is(':checked') : $option.find('.xun-checkbox-input').is(':checked');
                $option.attr('aria-checked', isChecked ? 'true' : 'false');
            }
        }
    }

    /**
     * jQuery插件
     */
    $.fn.xunCheckbox = function() {
        return this.each(function() {
            if (!$(this).data('xun-checkbox')) {
                $(this).data('xun-checkbox', new XunCheckboxField(this));
            }
        });
    };

    /**
     * 自动初始化
     */
    $(document).ready(function() {
        $('.xun-checkbox-field').xunCheckbox();
    });

    // 支持动态添加的字段 - 使用现代的MutationObserver
    if (window.MutationObserver) {
        const observer = new MutationObserver(function(mutations) {
            mutations.forEach(function(mutation) {
                if (mutation.type === 'childList') {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1) { // Element node
                            const $node = $(node);
                            // 检查新添加的节点是否包含checkbox字段
                            $node.find('.xun-checkbox-field').xunCheckbox();
                            // 检查节点本身是否是checkbox字段
                            if ($node.hasClass('xun-checkbox-field')) {
                                $node.xunCheckbox();
                            }
                        }
                    });
                }
            });
        });

        // 开始观察DOM变化
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }

})(jQuery);