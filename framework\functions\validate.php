<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Xun Framework 数据验证函数
 * 
 * 这个文件包含了框架中使用的各种数据验证函数。
 * 这些函数用于验证用户输入的数据是否符合预期格式和要求。
 * 
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

/**
 * 验证必填字段
 * 
 * 检查字段值是否为空（针对必填字段）。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要验证的值
 * @param array $field 字段配置
 * 
 * @return bool|string 验证通过返回true，失败返回错误信息
 */
if ( ! function_exists( 'xun_validate_required' ) ) {
    function xun_validate_required( $value, $field ) {
        
        if ( empty( $field['required'] ) ) {
            return true;
        }
        
        if ( empty( $value ) && $value !== '0' && $value !== 0 ) {
            $title = isset( $field['title'] ) ? $field['title'] : '此字段';
            return $title . '是必填项';
        }
        
        return true;
    }
}

/**
 * 验证邮箱格式
 * 
 * 检查邮箱地址格式是否正确。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要验证的值
 * @param array $field 字段配置
 * 
 * @return bool|string 验证通过返回true，失败返回错误信息
 */
if ( ! function_exists( 'xun_validate_email' ) ) {
    function xun_validate_email( $value, $field ) {
        
        if ( empty( $value ) ) {
            return true;
        }
        
        if ( ! is_email( $value ) ) {
            return '请输入有效的邮箱地址';
        }
        
        return true;
    }
}

/**
 * 验证URL格式
 * 
 * 检查URL格式是否正确。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要验证的值
 * @param array $field 字段配置
 * 
 * @return bool|string 验证通过返回true，失败返回错误信息
 */
if ( ! function_exists( 'xun_validate_url' ) ) {
    function xun_validate_url( $value, $field ) {
        
        if ( empty( $value ) ) {
            return true;
        }
        
        if ( ! filter_var( $value, FILTER_VALIDATE_URL ) ) {
            return '请输入有效的URL地址';
        }
        
        return true;
    }
}

/**
 * 验证数字范围
 * 
 * 检查数字是否在指定范围内。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要验证的值
 * @param array $field 字段配置
 * 
 * @return bool|string 验证通过返回true，失败返回错误信息
 */
if ( ! function_exists( 'xun_validate_number_range' ) ) {
    function xun_validate_number_range( $value, $field ) {
        
        if ( empty( $value ) && $value !== '0' && $value !== 0 ) {
            return true;
        }
        
        $number = floatval( $value );
        
        // 检查最小值
        if ( isset( $field['min'] ) && $number < $field['min'] ) {
            return '值不能小于 ' . $field['min'];
        }
        
        // 检查最大值
        if ( isset( $field['max'] ) && $number > $field['max'] ) {
            return '值不能大于 ' . $field['max'];
        }
        
        return true;
    }
}

/**
 * 验证字符串长度
 * 
 * 检查字符串长度是否在指定范围内。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要验证的值
 * @param array $field 字段配置
 * 
 * @return bool|string 验证通过返回true，失败返回错误信息
 */
if ( ! function_exists( 'xun_validate_string_length' ) ) {
    function xun_validate_string_length( $value, $field ) {
        
        if ( empty( $value ) ) {
            return true;
        }
        
        $length = strlen( $value );
        
        // 检查最小长度
        if ( isset( $field['min_length'] ) && $length < $field['min_length'] ) {
            return '长度不能少于 ' . $field['min_length'] . ' 个字符';
        }
        
        // 检查最大长度
        if ( isset( $field['max_length'] ) && $length > $field['max_length'] ) {
            return '长度不能超过 ' . $field['max_length'] . ' 个字符';
        }
        
        return true;
    }
}

/**
 * 验证正则表达式
 * 
 * 使用正则表达式验证字段值。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要验证的值
 * @param array $field 字段配置
 * 
 * @return bool|string 验证通过返回true，失败返回错误信息
 */
if ( ! function_exists( 'xun_validate_pattern' ) ) {
    function xun_validate_pattern( $value, $field ) {
        
        if ( empty( $value ) || empty( $field['pattern'] ) ) {
            return true;
        }
        
        if ( ! preg_match( $field['pattern'], $value ) ) {
            $message = isset( $field['pattern_message'] ) ? $field['pattern_message'] : '输入格式不正确';
            return $message;
        }
        
        return true;
    }
}

/**
 * 验证颜色值
 * 
 * 检查颜色值格式是否正确。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要验证的值
 * @param array $field 字段配置
 * 
 * @return bool|string 验证通过返回true，失败返回错误信息
 */
if ( ! function_exists( 'xun_validate_color' ) ) {
    function xun_validate_color( $value, $field ) {
        
        if ( empty( $value ) ) {
            return true;
        }
        
        // 检查十六进制颜色值
        if ( preg_match( '/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/', $value ) ) {
            return true;
        }
        
        // 检查RGB颜色值
        if ( preg_match( '/^rgb\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)$/', $value ) ) {
            return true;
        }
        
        // 检查RGBA颜色值
        if ( preg_match( '/^rgba\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)$/', $value ) ) {
            return true;
        }
        
        return '请输入有效的颜色值';
    }
}

/**
 * 验证选择项
 * 
 * 检查选择的值是否在允许的选项中。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要验证的值
 * @param array $field 字段配置
 * 
 * @return bool|string 验证通过返回true，失败返回错误信息
 */
if ( ! function_exists( 'xun_validate_options' ) ) {
    function xun_validate_options( $value, $field ) {
        
        if ( empty( $value ) || empty( $field['options'] ) ) {
            return true;
        }
        
        if ( ! is_array( $field['options'] ) ) {
            return true;
        }
        
        // 如果是多选
        if ( is_array( $value ) ) {
            foreach ( $value as $val ) {
                if ( ! array_key_exists( $val, $field['options'] ) ) {
                    return '选择的值无效';
                }
            }
        } else {
            // 单选
            if ( ! array_key_exists( $value, $field['options'] ) ) {
                return '选择的值无效';
            }
        }
        
        return true;
    }
}

/**
 * 验证文件类型
 * 
 * 检查上传文件的类型是否允许。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要验证的值
 * @param array $field 字段配置
 * 
 * @return bool|string 验证通过返回true，失败返回错误信息
 */
if ( ! function_exists( 'xun_validate_file_type' ) ) {
    function xun_validate_file_type( $value, $field ) {
        
        if ( empty( $value ) || empty( $field['allowed_types'] ) ) {
            return true;
        }
        
        $file_info = pathinfo( $value );
        $extension = isset( $file_info['extension'] ) ? strtolower( $file_info['extension'] ) : '';
        
        $allowed_types = $field['allowed_types'];
        if ( is_string( $allowed_types ) ) {
            $allowed_types = explode( ',', $allowed_types );
        }
        
        $allowed_types = array_map( 'trim', $allowed_types );
        $allowed_types = array_map( 'strtolower', $allowed_types );
        
        if ( ! in_array( $extension, $allowed_types ) ) {
            return '不支持的文件类型。允许的类型：' . implode( ', ', $allowed_types );
        }
        
        return true;
    }
}

/**
 * 验证日期格式
 * 
 * 检查日期格式是否正确。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要验证的值
 * @param array $field 字段配置
 * 
 * @return bool|string 验证通过返回true，失败返回错误信息
 */
if ( ! function_exists( 'xun_validate_date' ) ) {
    function xun_validate_date( $value, $field ) {
        
        if ( empty( $value ) ) {
            return true;
        }
        
        $format = isset( $field['date_format'] ) ? $field['date_format'] : 'Y-m-d';
        
        $date = DateTime::createFromFormat( $format, $value );
        
        if ( ! $date || $date->format( $format ) !== $value ) {
            return '请输入有效的日期格式：' . $format;
        }
        
        return true;
    }
}

/**
 * 通用字段验证函数
 * 
 * 根据字段配置执行所有相关的验证。
 * 
 * @since 1.0
 * 
 * @param mixed $value 要验证的值
 * @param array $field 字段配置
 * 
 * @return array 验证结果数组，包含是否通过和错误信息
 */
if ( ! function_exists( 'xun_validate_field' ) ) {
    function xun_validate_field( $value, $field ) {
        
        $errors = array();
        
        // 验证必填字段
        $required_result = xun_validate_required( $value, $field );
        if ( $required_result !== true ) {
            $errors[] = $required_result;
        }
        
        // 如果字段为空且不是必填，跳过其他验证
        if ( empty( $value ) && $value !== '0' && $value !== 0 && empty( $field['required'] ) ) {
            return array(
                'valid' => true,
                'errors' => array()
            );
        }
        
        // 根据字段类型进行特定验证
        $field_type = isset( $field['type'] ) ? $field['type'] : 'text';
        
        switch ( $field_type ) {
            case 'email':
                $email_result = xun_validate_email( $value, $field );
                if ( $email_result !== true ) {
                    $errors[] = $email_result;
                }
                break;
                
            case 'url':
                $url_result = xun_validate_url( $value, $field );
                if ( $url_result !== true ) {
                    $errors[] = $url_result;
                }
                break;
                
            case 'number':
                $range_result = xun_validate_number_range( $value, $field );
                if ( $range_result !== true ) {
                    $errors[] = $range_result;
                }
                break;
                
            case 'color':
                $color_result = xun_validate_color( $value, $field );
                if ( $color_result !== true ) {
                    $errors[] = $color_result;
                }
                break;
                
            case 'select':
            case 'radio':
            case 'checkbox':
                $options_result = xun_validate_options( $value, $field );
                if ( $options_result !== true ) {
                    $errors[] = $options_result;
                }
                break;
        }
        
        // 验证字符串长度
        if ( in_array( $field_type, array( 'text', 'textarea', 'email', 'url' ) ) ) {
            $length_result = xun_validate_string_length( $value, $field );
            if ( $length_result !== true ) {
                $errors[] = $length_result;
            }
        }
        
        // 验证正则表达式
        $pattern_result = xun_validate_pattern( $value, $field );
        if ( $pattern_result !== true ) {
            $errors[] = $pattern_result;
        }
        
        // 应用自定义验证过滤器
        $custom_errors = apply_filters( "xun_validate_{$field_type}", array(), $value, $field );
        if ( ! empty( $custom_errors ) && is_array( $custom_errors ) ) {
            $errors = array_merge( $errors, $custom_errors );
        }
        
        return array(
            'valid' => empty( $errors ),
            'errors' => $errors
        );
    }
}
