<?php
/**
 * Xun Framework 文档系统 - 首页
 *
 * 这是文档系统的主页面，提供路由功能和页面加载。
 * 根据URL参数加载相应的页面内容。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

// 定义基础路径
define('ABSPATH', __DIR__ . '/');

// 加载配置和函数
require_once ABSPATH . 'includes/config.php';
require_once ABSPATH . 'includes/functions.php';

// 获取当前页面
$current_page = xun_docs_get_current_page();

// 页面路由配置
$page_routes = [
    '' => 'home',
    'home' => 'home',
    'getting-started' => 'getting-started',
    'installation' => 'installation',
    'basic-usage' => 'basic-usage',
    'examples' => 'examples',
    'field-types' => 'field-types/index',
    'field-types/text' => 'field-types/text',
    'field-types/select' => 'field-types/select',
    'field-types/media' => 'field-types/media',
    'field-types/switch' => 'field-types/switch',
    'field-types/color' => 'field-types/color',
    'field-types/date' => 'field-types/date',
    'field-types/repeater' => 'field-types/repeater',
    'api-reference' => 'api-reference',
    'hooks-filters' => 'hooks-filters',
    'customization' => 'customization',
    'best-practices' => 'best-practices',
    'troubleshooting' => 'troubleshooting',
    'changelog' => 'changelog',
    'migration' => 'migration',
    'contributing' => 'contributing',
    'license' => 'license'
];

// 确定要加载的模板
$template = $page_routes[$current_page] ?? '404';

// 页面特定的变量
$page_title_override = '';
$page_scripts = [];
$inline_scripts = '';

// 根据页面设置特定变量
switch ($template) {
    case 'home':
        $page_title_override = '';
        break;
    case 'getting-started':
        $page_title_override = '快速开始';
        break;
    case 'installation':
        $page_title_override = '安装指南';
        break;
    case 'basic-usage':
        $page_title_override = '基础用法';
        break;
    case 'examples':
        $page_title_override = '示例代码';
        break;
    case 'field-types/index':
        $page_title_override = '字段类型';
        break;
    case 'api-reference':
        $page_title_override = 'API 参考';
        break;
    case '404':
        $page_title_override = '页面未找到';
        break;
}

// AJAX请求处理
if (xun_docs_is_ajax()) {
    // 处理AJAX搜索请求
    if (isset($_GET['action']) && $_GET['action'] === 'search') {
        $query = $_GET['q'] ?? '';
        $results = xun_docs_search($query);
        xun_docs_send_json(['results' => $results]);
    }
    
    // 处理其他AJAX请求
    xun_docs_send_json(['error' => '未知的AJAX请求'], 400);
}

// 加载页面头部
include ABSPATH . 'includes/header.php';

// 加载页面内容
if ($template === '404') {
    xun_docs_load_404();
} else {
    xun_docs_load_template($template, [
        'current_page' => $current_page,
        'page_title' => $page_title_override
    ]);
}

// 加载页面底部
include ABSPATH . 'includes/footer.php';

/**
 * 简单的搜索功能
 * 
 * @param string $query 搜索查询
 * @return array 搜索结果
 */
function xun_docs_search($query) {
    if (empty($query) || strlen($query) < 2) {
        return [];
    }
    
    // 这里是一个简单的搜索实现
    // 实际项目中可以使用更复杂的搜索算法
    $results = [];
    
    // 搜索导航菜单
    $navigation = xun_docs_get_navigation('main');
    foreach ($navigation as $section) {
        foreach ($section['items'] as $item) {
            if (stripos($item['title'], $query) !== false || 
                stripos($item['desc'] ?? '', $query) !== false) {
                $results[] = [
                    'title' => $item['title'],
                    'description' => $item['desc'] ?? '',
                    'url' => xun_docs_get_page_url($item['url']),
                    'type' => 'page'
                ];
            }
        }
    }
    
    // 搜索字段类型
    $field_types = xun_docs_get_field_types();
    foreach ($field_types as $category => $data) {
        if (stripos($data['title'], $query) !== false || 
            stripos($data['description'], $query) !== false) {
            $results[] = [
                'title' => $data['title'],
                'description' => $data['description'],
                'url' => xun_docs_get_page_url('field-types'),
                'type' => 'category'
            ];
        }
        
        foreach ($data['fields'] as $field_id => $field) {
            if (stripos($field['title'], $query) !== false) {
                $results[] = [
                    'title' => $field['title'],
                    'description' => "字段类型 - {$data['title']}",
                    'url' => xun_docs_get_page_url("field-types/{$field_id}"),
                    'type' => 'field'
                ];
            }
        }
    }
    
    // 限制结果数量
    return array_slice($results, 0, 10);
}
