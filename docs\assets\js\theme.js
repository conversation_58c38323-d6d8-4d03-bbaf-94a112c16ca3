/**
 * Xun Framework 文档系统 - 主题切换功能
 *
 * 提供深色/浅色主题切换功能，支持本地存储和系统主题检测。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

(function() {
    'use strict';

    /**
     * 主题管理器
     */
    const ThemeManager = {
        // 配置
        config: {
            storageKey: 'xun-docs-theme',
            defaultTheme: 'dark',
            themes: ['light', 'dark'],
            transitionDuration: 300
        },

        // 当前主题
        currentTheme: null,

        // DOM元素
        elements: {
            html: document.documentElement,
            toggleButton: null,
            themeIcons: {
                light: null,
                dark: null
            }
        },

        /**
         * 初始化主题管理器
         */
        init() {
            this.cacheElements();
            this.loadTheme();
            this.bindEvents();
            this.updateUI();
        },

        /**
         * 缓存DOM元素
         */
        cacheElements() {
            this.elements.toggleButton = document.getElementById('theme-toggle');
            
            if (this.elements.toggleButton) {
                this.elements.themeIcons.light = this.elements.toggleButton.querySelector('.dark\\:hidden');
                this.elements.themeIcons.dark = this.elements.toggleButton.querySelector('.hidden.dark\\:block');
            }
        },

        /**
         * 绑定事件
         */
        bindEvents() {
            // 主题切换按钮
            if (this.elements.toggleButton) {
                this.elements.toggleButton.addEventListener('click', () => {
                    this.toggleTheme();
                });
            }

            // 键盘快捷键 (Ctrl/Cmd + Shift + T)
            document.addEventListener('keydown', (e) => {
                if ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'T') {
                    e.preventDefault();
                    this.toggleTheme();
                }
            });

            // 监听系统主题变化
            if (window.matchMedia) {
                const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
                mediaQuery.addEventListener('change', (e) => {
                    // 只有在没有手动设置主题时才跟随系统
                    if (!this.hasManualTheme()) {
                        this.setTheme(e.matches ? 'dark' : 'light', false);
                    }
                });
            }
        },

        /**
         * 加载主题
         */
        loadTheme() {
            // 优先级：本地存储 > 系统偏好 > 默认主题
            let theme = this.getStoredTheme();
            
            if (!theme) {
                theme = this.getSystemTheme() || this.config.defaultTheme;
            }

            this.setTheme(theme, false);
        },

        /**
         * 获取存储的主题
         */
        getStoredTheme() {
            try {
                return localStorage.getItem(this.config.storageKey);
            } catch (e) {
                console.warn('无法访问localStorage:', e);
                return null;
            }
        },

        /**
         * 获取系统主题偏好
         */
        getSystemTheme() {
            if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
                return 'dark';
            }
            return 'light';
        },

        /**
         * 检查是否有手动设置的主题
         */
        hasManualTheme() {
            return this.getStoredTheme() !== null;
        },

        /**
         * 设置主题
         */
        setTheme(theme, save = true) {
            if (!this.config.themes.includes(theme)) {
                console.warn(`未知主题: ${theme}`);
                return;
            }

            // 添加过渡效果
            this.addTransition();

            // 更新HTML属性
            this.elements.html.setAttribute('data-theme', theme);
            
            // 更新当前主题
            this.currentTheme = theme;

            // 保存到本地存储
            if (save) {
                this.saveTheme(theme);
            }

            // 更新UI
            this.updateUI();

            // 触发主题变化事件
            this.dispatchThemeChangeEvent(theme);

            // 移除过渡效果
            setTimeout(() => {
                this.removeTransition();
            }, this.config.transitionDuration);
        },

        /**
         * 保存主题到本地存储
         */
        saveTheme(theme) {
            try {
                localStorage.setItem(this.config.storageKey, theme);
            } catch (e) {
                console.warn('无法保存主题到localStorage:', e);
            }
        },

        /**
         * 切换主题
         */
        toggleTheme() {
            const newTheme = this.currentTheme === 'dark' ? 'light' : 'dark';
            this.setTheme(newTheme);
        },

        /**
         * 更新UI元素
         */
        updateUI() {
            if (!this.elements.toggleButton) return;

            // 更新按钮aria-label
            const label = this.currentTheme === 'dark' ? '切换到浅色主题' : '切换到深色主题';
            this.elements.toggleButton.setAttribute('aria-label', label);

            // 更新图标显示
            this.updateThemeIcons();

            // 更新按钮状态
            this.updateButtonState();
        },

        /**
         * 更新主题图标
         */
        updateThemeIcons() {
            if (!this.elements.themeIcons.light || !this.elements.themeIcons.dark) return;

            if (this.currentTheme === 'dark') {
                // 深色主题：显示太阳图标（切换到浅色）
                this.elements.themeIcons.light.classList.add('hidden');
                this.elements.themeIcons.dark.classList.remove('hidden');
            } else {
                // 浅色主题：显示月亮图标（切换到深色）
                this.elements.themeIcons.light.classList.remove('hidden');
                this.elements.themeIcons.dark.classList.add('hidden');
            }
        },

        /**
         * 更新按钮状态
         */
        updateButtonState() {
            if (!this.elements.toggleButton) return;

            // 添加活动状态类
            if (this.currentTheme === 'dark') {
                this.elements.toggleButton.classList.add('theme-active');
            } else {
                this.elements.toggleButton.classList.remove('theme-active');
            }
        },

        /**
         * 添加过渡效果
         */
        addTransition() {
            const style = document.createElement('style');
            style.id = 'theme-transition';
            style.textContent = `
                *, *::before, *::after {
                    transition: background-color ${this.config.transitionDuration}ms ease,
                                border-color ${this.config.transitionDuration}ms ease,
                                color ${this.config.transitionDuration}ms ease !important;
                }
            `;
            document.head.appendChild(style);
        },

        /**
         * 移除过渡效果
         */
        removeTransition() {
            const style = document.getElementById('theme-transition');
            if (style) {
                style.remove();
            }
        },

        /**
         * 触发主题变化事件
         */
        dispatchThemeChangeEvent(theme) {
            const event = new CustomEvent('themechange', {
                detail: {
                    theme: theme,
                    previousTheme: this.currentTheme
                }
            });
            document.dispatchEvent(event);
        },

        /**
         * 获取当前主题
         */
        getCurrentTheme() {
            return this.currentTheme;
        },

        /**
         * 检查是否为深色主题
         */
        isDarkTheme() {
            return this.currentTheme === 'dark';
        },

        /**
         * 检查是否为浅色主题
         */
        isLightTheme() {
            return this.currentTheme === 'light';
        },

        /**
         * 重置主题（清除本地存储，使用系统偏好）
         */
        resetTheme() {
            try {
                localStorage.removeItem(this.config.storageKey);
            } catch (e) {
                console.warn('无法清除localStorage中的主题设置:', e);
            }
            
            const systemTheme = this.getSystemTheme() || this.config.defaultTheme;
            this.setTheme(systemTheme, false);
        },

        /**
         * 预加载主题资源
         */
        preloadThemeAssets() {
            // 这里可以预加载不同主题的图片或其他资源
            // 暂时保留空实现
        }
    };

    // 主题工具函数
    window.XunTheme = {
        /**
         * 获取当前主题
         */
        getCurrentTheme() {
            return ThemeManager.getCurrentTheme();
        },

        /**
         * 设置主题
         */
        setTheme(theme) {
            ThemeManager.setTheme(theme);
        },

        /**
         * 切换主题
         */
        toggleTheme() {
            ThemeManager.toggleTheme();
        },

        /**
         * 检查是否为深色主题
         */
        isDark() {
            return ThemeManager.isDarkTheme();
        },

        /**
         * 检查是否为浅色主题
         */
        isLight() {
            return ThemeManager.isLightTheme();
        },

        /**
         * 重置主题
         */
        reset() {
            ThemeManager.resetTheme();
        },

        /**
         * 监听主题变化
         */
        onThemeChange(callback) {
            document.addEventListener('themechange', callback);
        },

        /**
         * 移除主题变化监听器
         */
        offThemeChange(callback) {
            document.removeEventListener('themechange', callback);
        }
    };

    // 初始化主题管理器
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => ThemeManager.init());
    } else {
        ThemeManager.init();
    }

})();
