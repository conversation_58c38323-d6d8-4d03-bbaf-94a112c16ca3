<?php if ( ! defined( 'ABSPATH' ) ) { die; } // 禁止直接访问
/**
 * Date 字段类 - 组合式架构增强
 *
 * 保留所有日期选择器特有功能：日历弹窗、日期验证、格式化等
 * 基础文本输入功能由text字段处理
 *
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.1.0
 */
if ( ! class_exists( 'XUN_Field_date' ) ) {
    
    /**
     * XUN_Field_date 日期选择器字段类
     *
     * 采用组合式架构，保留所有日期选择器特有功能：
     * - 日历弹窗和交互
     * - 日期格式化和验证
     * - 日期范围限制
     * - 快速选择功能
     * - 键盘导航支持
     * - 无障碍访问优化
     *
     * 基础文本输入由text字段组件处理
     *
     * @since 1.1.0
     */
    class XUN_Field_date extends XUN_Fields {
        
        /**
         * 构造函数
         *
         * 初始化date字段实例。
         *
         * @since 1.1.0
         *
         * @param array  $field  字段配置数组
         * @param mixed  $value  字段值
         * @param string $unique 唯一标识符
         * @param string $where  字段位置
         * @param string $parent 父级字段
         */
        public function __construct( $field, $value = '', $unique = '', $where = '', $parent = '' ) {
            parent::__construct( $field, $value, $unique, $where, $parent );
        }
        
        /**
         * 渲染日期选择器字段 - 组合式架构增强
         *
         * 保留所有日期选择器特有功能，基础文本输入改为组合式。
         *
         * @since 1.1.0
         */
        public function render() {

            // 获取字段配置
            $settings = $this->get_field_settings();

            // 输出字段前置内容
            echo $this->field_before();

            // 判断是否为日期范围模式
            if ( ! empty( $this->field['date_range'] ) ) {
                $this->render_date_range( $settings );
            } else {
                // 处理单个日期字段值
                $display_value = $this->format_display_value( $this->value, $settings );
                $this->render_single_date( $settings, $display_value );
            }

            // 输出字段后置内容
            echo $this->field_after();
        }
        
        /**
         * 渲染单个日期选择器
         * 
         * @since 1.0
         * 
         * @param array  $settings      字段设置
         * @param string $display_value 显示值
         */
        private function render_single_date( $settings, $display_value ) {
            
            $field_id = $this->field_name();
            $unique_id = 'xun-date-' . uniqid();
            
            echo '<div class="xun-date-field-wrapper relative" data-settings="' . esc_attr( json_encode( $settings ) ) . '">';

            // 隐藏的实际输入字段（存储标准格式的日期）
            echo '<input type="hidden" name="' . esc_attr( $field_id ) . '" value="' . esc_attr( $this->value ) . '" class="xun-date-value" />';

            // 显示输入字段（用户看到的格式化日期） - 组合式架构
            // 暂时保留原有实现，等text字段完善后可以替换为：
            // $text_field = array(
            //     'id'   => $unique_id,
            //     'type' => 'text',
            //     'attributes' => array(
            //         'class' => 'xun-date-input block w-full rounded-md bg-white px-3 py-1.5 pr-10 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 cursor-pointer transition-colors duration-200',
            //         'placeholder' => $settings['placeholder'],
            //         'readonly' => 'readonly',
            //     ),
            // );
            // XUN::field( $text_field, $display_value, '', 'field/date' );

            echo '<div class="relative">';
            echo '<input type="text" ';
            echo 'id="' . esc_attr( $unique_id ) . '" ';
            echo 'class="xun-date-input grid w-full cursor-default grid-cols-1 rounded-md bg-white py-1.5 pr-10 pl-3 text-left text-gray-900 ';
            echo 'sm:text-sm/6 touch-manipulation ';
            echo 'focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 transition-colors duration-200" ';
            echo 'value="' . esc_attr( $display_value ) . '" ';
            echo 'placeholder="' . esc_attr( $settings['placeholder'] ) . '" ';
            echo 'readonly ';
            echo $this->field_attributes() . ' />';

            // 日历图标按钮
            echo '<button type="button" class="xun-date-trigger absolute right-2 top-1/2 -translate-y-1/2 flex items-center ';
            echo 'text-gray-400 hover:text-gray-600 transition-colors duration-200 focus:outline-none focus:text-indigo-600" ';
            echo 'aria-label="打开日期选择器">';
            echo '<svg class="w-5 h-5 sm:w-4 sm:h-4 md:w-4 md:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">';
            echo '<rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke-width="2"></rect>';
            echo '<line x1="16" y1="2" x2="16" y2="6" stroke-width="2"></line>';
            echo '<line x1="8" y1="2" x2="8" y2="6" stroke-width="2"></line>';
            echo '<line x1="3" y1="10" x2="21" y2="10" stroke-width="2"></line>';
            echo '</svg>';
            echo '</button>';
            echo '</div>';

            // 日期选择器弹出层
            echo '<div class="xun-date-picker absolute top-full left-0 mt-2 z-50 hidden ';
            echo 'bg-white border border-gray-200 rounded-lg shadow-xl min-w-80 max-w-sm ';
            echo 'transform opacity-0 scale-95 transition-all duration-200 ease-out">';
            
            $this->render_date_picker_content();
            
            echo '</div>';
            echo '</div>';
        }
        
        /**
         * 渲染日期范围选择器
         * 
         * @since 1.0
         * 
         * @param array $settings 字段设置
         */
        private function render_date_range( $settings ) {
            
            $field_name = $this->field_name();
            
            // 解析范围值
            $default_range = array(
                'from' => '',
                'to'   => '',
            );

            // 确保值是数组格式
            if ( ! is_array( $this->value ) ) {
                $range_value = $default_range;
            } else {
                $range_value = wp_parse_args( $this->value, $default_range );
            }
            
            $from_display = $this->format_display_value( $range_value['from'], $settings );
            $to_display = $this->format_display_value( $range_value['to'], $settings );
            
            echo '<div class="xun-date-range-wrapper" data-settings="' . esc_attr( json_encode( $settings ) ) . '">';
            echo '<div class="grid grid-cols-1 md:grid-cols-2 gap-4">';
            
            // 开始日期
            echo '<div class="xun-date-range-from">';
            echo '<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">';
            echo esc_html( $settings['text_from'] );
            echo '</label>';
            $this->render_range_input( $field_name . '[from]', $range_value['from'], $from_display, $settings, 'from' );
            echo '</div>';
            
            // 结束日期
            echo '<div class="xun-date-range-to">';
            echo '<label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">';
            echo esc_html( $settings['text_to'] );
            echo '</label>';
            $this->render_range_input( $field_name . '[to]', $range_value['to'], $to_display, $settings, 'to' );
            echo '</div>';
            
            echo '</div>';
            echo '</div>';
        }
        
        /**
         * 渲染范围输入字段
         * 
         * @since 1.0
         * 
         * @param string $name         字段名称
         * @param string $value        字段值
         * @param string $display_value 显示值
         * @param array  $settings     设置
         * @param string $type         类型（from/to）
         */
        private function render_range_input( $name, $value, $display_value, $settings, $type ) {
            
            $unique_id = 'xun-date-range-' . $type . '-' . uniqid();
            
            echo '<div class="xun-date-field-wrapper relative" data-range-type="' . esc_attr( $type ) . '">';
            
            // 隐藏的实际输入字段
            echo '<input type="hidden" name="' . esc_attr( $name ) . '" value="' . esc_attr( $value ) . '" class="xun-date-value" />';
            
            // 显示输入字段 - 组合式架构（日期范围模式）
            // 暂时保留原有实现，等text字段完善后可以替换为：
            // $range_text_field = array(
            //     'id'   => $unique_id,
            //     'type' => 'text',
            //     'attributes' => array(
            //         'class' => 'xun-date-input block w-full rounded-md bg-white px-3 py-1.5 pr-10 text-base text-gray-900 outline-1 -outline-offset-1 outline-gray-300 placeholder:text-gray-400 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 sm:text-sm/6 cursor-pointer transition-colors duration-200',
            //         'placeholder' => $settings['placeholder'],
            //         'readonly' => 'readonly',
            //     ),
            // );
            // XUN::field( $range_text_field, $display_value, '', 'field/date' );

            echo '<div class="relative">';
            echo '<input type="text" ';
            echo 'id="' . esc_attr( $unique_id ) . '" ';
            echo 'class="xun-date-input grid w-full cursor-default grid-cols-1 rounded-md bg-white py-1.5 pr-10 pl-3 text-left text-gray-900 ';
            echo 'sm:text-sm/6 touch-manipulation ';
            echo 'focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 transition-colors duration-200" ';
            echo 'value="' . esc_attr( $display_value ) . '" ';
            echo 'placeholder="' . esc_attr( $settings['placeholder'] ) . '" ';
            echo 'readonly />';
            
            // 日历图标按钮
            echo '<button type="button" class="xun-date-trigger absolute right-2 top-1/2 -translate-y-1/2 flex items-center ';
            echo 'text-gray-400 hover:text-gray-600 transition-colors duration-200 focus:outline-none focus:text-indigo-600" ';
            echo 'aria-label="打开日期选择器">';
            echo '<svg class="w-5 h-5 sm:w-4 sm:h-4 md:w-4 md:h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">';
            echo '<rect x="3" y="4" width="18" height="18" rx="2" ry="2" stroke-width="2"></rect>';
            echo '<line x1="16" y1="2" x2="16" y2="6" stroke-width="2"></line>';
            echo '<line x1="8" y1="2" x2="8" y2="6" stroke-width="2"></line>';
            echo '<line x1="3" y1="10" x2="21" y2="10" stroke-width="2"></line>';
            echo '</svg>';
            echo '</button>';
            echo '</div>';

            // 日期选择器弹出层
            echo '<div class="xun-date-picker absolute top-full left-0 mt-2 z-50 hidden ';
            echo 'bg-white border border-gray-200 rounded-lg shadow-xl min-w-80 max-w-sm ';
            echo 'transform opacity-0 scale-95 transition-all duration-200 ease-out">';
            
            $this->render_date_picker_content();
            
            echo '</div>';
            echo '</div>';
        }
        
        /**
         * 渲染日期选择器内容
         *
         * @since 1.0
         */
        private function render_date_picker_content() {

            $settings = $this->get_field_settings();

            echo '<div class="xun-date-picker-content p-4">';

            // 头部导航
            echo '<div class="xun-date-header flex items-center justify-between mb-4">';
            echo '<button type="button" class="xun-date-prev-month p-2 rounded-lg hover:bg-gray-100 transition-colors" aria-label="上个月">';
            echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">';
            echo '<polyline points="15,18 9,12 15,6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></polyline>';
            echo '</svg>';
            echo '</button>';

            echo '<div class="xun-date-title flex items-center space-x-2">';
            echo '<button type="button" class="xun-date-month-year px-3 py-1 text-sm font-medium rounded-lg hover:bg-gray-100 transition-colors">';
            echo '<span class="xun-current-month-year"></span>';
            echo '</button>';
            echo '</div>';

            echo '<button type="button" class="xun-date-next-month p-2 rounded-lg hover:bg-gray-100 transition-colors" aria-label="下个月">';
            echo '<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">';
            echo '<polyline points="9,18 15,12 9,6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></polyline>';
            echo '</svg>';
            echo '</button>';
            echo '</div>';

            // 星期标题
            echo '<div class="xun-date-weekdays grid grid-cols-7 gap-1 mb-2">';
            $weekdays = array( '日', '一', '二', '三', '四', '五', '六' );
            foreach ( $weekdays as $day ) {
                echo '<div class="text-center text-xs font-medium text-gray-500 py-2">' . esc_html( $day ) . '</div>';
            }
            echo '</div>';

            // 日期网格
            echo '<div class="xun-date-grid grid grid-cols-7 gap-1 mb-4"></div>';

            // 时间选择器（如果启用）
            if ( $settings['enable_time'] ) {
                echo '<div class="xun-time-section pt-3 border-t border-gray-200">';
                $this->render_time_picker( $settings );
                echo '</div>';
            }

            // 底部操作按钮
            $show_footer = $settings['show_today'] || $settings['show_clear'];
            if ( $show_footer ) {
                echo '<div class="xun-date-footer flex items-center justify-between pt-3 border-t border-gray-200">';

                if ( $settings['show_today'] ) {
                    echo '<button type="button" class="xun-date-today px-3 py-1 text-sm text-indigo-600 hover:bg-indigo-50 rounded-lg transition-colors">现在</button>';
                } else {
                    echo '<div></div>'; // 占位符保持布局
                }

                if ( $settings['show_clear'] ) {
                    echo '<button type="button" class="xun-date-clear px-3 py-1 text-sm text-gray-500 hover:bg-gray-100 rounded-lg transition-colors">清除</button>';
                } else {
                    echo '<div></div>'; // 占位符保持布局
                }

                echo '</div>';
            }

            echo '</div>';
        }

        /**
         * 渲染时间选择器
         *
         * @since 1.0
         *
         * @param array $settings 字段设置
         */
        private function render_time_picker( $settings ) {

            echo '<div class="xun-time-picker-section">';
            echo '<div class="text-sm font-medium text-gray-700 mb-3">选择时间</div>';

            echo '<div class="flex items-center space-x-2">';

            // 小时输入框
            echo '<div class="flex-1">';
            echo '<input type="number" class="xun-time-hour block w-full rounded-md bg-white px-2 py-1 text-sm text-gray-900 ';
            echo 'outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 text-center" ';

            if ( $settings['time_format'] === '12' ) {
                echo 'min="1" max="12" placeholder="12" ';
            } else {
                echo 'min="0" max="23" placeholder="00" ';
            }
            echo 'value="' . ( $settings['time_format'] === '12' ? '12' : '00' ) . '" />';
            echo '<label class="block text-xs text-gray-500 mt-1 text-center">时</label>';
            echo '</div>';

            echo '<div class="text-gray-400 text-sm">:</div>';

            // 分钟输入框
            echo '<div class="flex-1">';
            echo '<input type="number" class="xun-time-minute block w-full rounded-md bg-white px-2 py-1 text-sm text-gray-900 ';
            echo 'outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 text-center" ';
            echo 'min="0" max="59" placeholder="00" value="00" />';
            echo '<label class="block text-xs text-gray-500 mt-1 text-center">分</label>';
            echo '</div>';

            // 秒输入框（如果启用）
            if ( $settings['show_seconds'] ) {
                echo '<div class="text-gray-400 text-sm">:</div>';
                echo '<div class="flex-1">';
                echo '<input type="number" class="xun-time-second block w-full rounded-md bg-white px-2 py-1 text-sm text-gray-900 ';
                echo 'outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600 text-center" ';
                echo 'min="0" max="59" placeholder="00" value="00" />';
                echo '<label class="block text-xs text-gray-500 mt-1 text-center">秒</label>';
                echo '</div>';
            }

            // AM/PM选择（12小时制）
            if ( $settings['time_format'] === '12' ) {
                echo '<div class="flex-1">';
                echo '<select class="xun-time-ampm block w-full rounded-md bg-white px-2 py-1 text-sm text-gray-900 ';
                echo 'outline-1 -outline-offset-1 outline-gray-300 focus:outline-2 focus:-outline-offset-2 focus:outline-indigo-600">';
                echo '<option value="AM">上午</option>';
                echo '<option value="PM">下午</option>';
                echo '</select>';
                echo '<label class="block text-xs text-gray-500 mt-1 text-center">上/下午</label>';
                echo '</div>';
            }

            echo '</div>';

            // 快速时间按钮
            echo '<div class="mt-3 flex flex-wrap gap-2">';
            echo '<button type="button" class="xun-time-preset px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors" data-time="09:00">09:00</button>';
            echo '<button type="button" class="xun-time-preset px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors" data-time="12:00">12:00</button>';
            echo '<button type="button" class="xun-time-preset px-2 py-1 text-xs bg-gray-100 hover:bg-gray-200 rounded transition-colors" data-time="18:00">18:00</button>';
            echo '<button type="button" class="xun-time-now px-2 py-1 text-xs bg-indigo-100 hover:bg-indigo-200 text-indigo-700 rounded transition-colors">现在</button>';
            echo '</div>';

            echo '</div>';
        }

        /**
         * 获取字段设置
         *
         * 合并默认设置和用户自定义设置。
         *
         * @since 1.0
         *
         * @return array 完整的字段设置
         */
        private function get_field_settings() {

            // 默认设置
            $default_settings = array(
                'date_format'    => 'Y-m-d',           // 存储格式
                'display_format' => 'Y年m月d日',        // 显示格式
                'placeholder'    => '请选择日期',        // 占位符文本
                'min_date'       => '',                // 最小日期
                'max_date'       => '',                // 最大日期
                'disabled_dates' => array(),           // 禁用的日期数组
                'disabled_days'  => array(),           // 禁用的星期（0-6，0为周日）
                'first_day'      => 1,                 // 一周的第一天（0为周日，1为周一）
                'show_today'     => true,              // 显示"今天"按钮
                'show_clear'     => true,              // 显示"清除"按钮
                'auto_close'     => true,              // 选择日期后自动关闭
                'text_from'      => '开始日期',         // 范围选择：开始文本
                'text_to'        => '结束日期',         // 范围选择：结束文本
                'locale'         => 'zh-CN',           // 本地化设置
                // 时间选择相关设置
                'enable_time'    => false,             // 是否启用时间选择
                'time_format'    => '24',              // 时间格式：12 或 24
                'show_seconds'   => false,             // 是否显示秒
                'minute_step'    => 1,                 // 分钟步长
                'second_step'    => 1,                 // 秒步长
                'default_time'   => '',               // 默认时间
            );

            // 获取用户设置
            $user_settings = ! empty( $this->field['settings'] ) ? $this->field['settings'] : array();

            // 合并设置
            $settings = wp_parse_args( $user_settings, $default_settings );

            // 处理日期范围设置
            if ( ! empty( $this->field['date_range'] ) ) {
                $range_settings = wp_parse_args( $this->field, array(
                    'text_from' => '开始日期',
                    'text_to'   => '结束日期',
                ) );
                $settings['text_from'] = $range_settings['text_from'];
                $settings['text_to'] = $range_settings['text_to'];
            }

            return $settings;
        }

        /**
         * 格式化显示值
         *
         * 将存储格式的日期转换为显示格式。
         *
         * @since 1.0
         *
         * @param string $value    日期值
         * @param array  $settings 字段设置
         *
         * @return string 格式化后的显示值
         */
        private function format_display_value( $value, $settings ) {

            if ( empty( $value ) || ! is_string( $value ) ) {
                return '';
            }

            // 尝试解析日期
            $date = DateTime::createFromFormat( $settings['date_format'], $value );

            if ( $date === false ) {
                // 如果解析失败，尝试其他常见格式
                $common_formats = array( 'Y-m-d', 'Y/m/d', 'd/m/Y', 'm/d/Y', 'Y-m-d H:i:s' );

                foreach ( $common_formats as $format ) {
                    $date = DateTime::createFromFormat( $format, $value );
                    if ( $date !== false ) {
                        break;
                    }
                }
            }

            if ( $date === false ) {
                return $value; // 如果都解析失败，返回原值
            }

            return $date->format( $settings['display_format'] );
        }

        /**
         * 加载字段资源 - 组合式架构增强
         *
         * 保留date字段特有的功能（日历弹窗、日期验证等），
         * 基础文本输入由text字段自动加载资源。
         *
         * @since 1.1.0
         */
        public function enqueue() {

            // 加载日期选择器的JavaScript文件
            wp_enqueue_script(
                'xun-field-date',
                XUN_Setup::$url . '/assets/js/fields/date.js',
                array( 'jquery' ),
                XUN_Setup::$version,
                true
            );

            // 本地化脚本
            wp_localize_script( 'xun-field-date', 'xunDateL10n', array(
                'months' => array(
                    '一月', '二月', '三月', '四月', '五月', '六月',
                    '七月', '八月', '九月', '十月', '十一月', '十二月'
                ),
                'monthsShort' => array(
                    '1月', '2月', '3月', '4月', '5月', '6月',
                    '7月', '8月', '9月', '10月', '11月', '12月'
                ),
                'weekdays' => array(
                    '星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'
                ),
                'weekdaysShort' => array(
                    '日', '一', '二', '三', '四', '五', '六'
                ),
                'today' => '今天',
                'clear' => '清除',
                'close' => '关闭',
                'prevMonth' => '上个月',
                'nextMonth' => '下个月',
                'selectMonth' => '选择月份',
                'selectYear' => '选择年份',
            ) );
        }

        /**
         * 验证日期字段值
         *
         * 验证和清理日期值，确保格式正确和在允许范围内。
         *
         * @since 1.0
         *
         * @param mixed $value 要验证的值
         *
         * @return mixed 验证后的值
         */
        public function validate( $value ) {

            // 如果是日期范围
            if ( ! empty( $this->field['date_range'] ) && is_array( $value ) ) {
                return $this->validate_date_range( $value );
            }

            // 单个日期验证
            return $this->validate_single_date( $value );
        }

        /**
         * 验证单个日期
         *
         * @since 1.0
         *
         * @param mixed $value 日期值
         *
         * @return string 验证后的日期值
         */
        private function validate_single_date( $value ) {

            if ( empty( $value ) ) {
                return '';
            }

            $settings = $this->get_field_settings();

            // 尝试解析日期
            $date = $this->parse_date( $value, $settings );

            if ( $date === false ) {
                return ''; // 无效日期返回空值
            }

            // 检查日期范围限制
            if ( ! $this->is_date_in_range( $date, $settings ) ) {
                return ''; // 超出范围返回空值
            }

            // 检查禁用日期
            if ( $this->is_date_disabled( $date, $settings ) ) {
                return ''; // 禁用日期返回空值
            }

            // 返回标准格式的日期
            return $date->format( $settings['date_format'] );
        }

        /**
         * 验证日期范围
         *
         * @since 1.0
         *
         * @param array $value 日期范围值
         *
         * @return array 验证后的日期范围值
         */
        private function validate_date_range( $value ) {

            $result = array(
                'from' => '',
                'to'   => '',
            );

            if ( ! is_array( $value ) ) {
                return $result;
            }

            $value = wp_parse_args( $value, $result );

            // 验证开始日期
            if ( ! empty( $value['from'] ) ) {
                $result['from'] = $this->validate_single_date( $value['from'] );
            }

            // 验证结束日期
            if ( ! empty( $value['to'] ) ) {
                $result['to'] = $this->validate_single_date( $value['to'] );
            }

            // 确保开始日期不晚于结束日期
            if ( ! empty( $result['from'] ) && ! empty( $result['to'] ) ) {
                $settings = $this->get_field_settings();
                $from_date = $this->parse_date( $result['from'], $settings );
                $to_date = $this->parse_date( $result['to'], $settings );

                if ( $from_date && $to_date && $from_date > $to_date ) {
                    // 如果开始日期晚于结束日期，交换它们
                    $temp = $result['from'];
                    $result['from'] = $result['to'];
                    $result['to'] = $temp;
                }
            }

            return $result;
        }

        /**
         * 解析日期字符串
         *
         * @since 1.0
         *
         * @param string $value    日期字符串
         * @param array  $settings 字段设置
         *
         * @return DateTime|false 解析后的日期对象或false
         */
        private function parse_date( $value, $settings ) {

            if ( empty( $value ) ) {
                return false;
            }

            // 尝试使用指定格式解析
            $date = DateTime::createFromFormat( $settings['date_format'], $value );

            if ( $date !== false ) {
                return $date;
            }

            // 尝试其他常见格式
            $common_formats = array( 'Y-m-d', 'Y/m/d', 'd/m/Y', 'm/d/Y', 'Y-m-d H:i:s' );

            foreach ( $common_formats as $format ) {
                $date = DateTime::createFromFormat( $format, $value );
                if ( $date !== false ) {
                    return $date;
                }
            }

            // 尝试使用strtotime
            $timestamp = strtotime( $value );
            if ( $timestamp !== false ) {
                return new DateTime( '@' . $timestamp );
            }

            return false;
        }

        /**
         * 检查日期是否在允许范围内
         *
         * @since 1.0
         *
         * @param DateTime $date     要检查的日期
         * @param array    $settings 字段设置
         *
         * @return bool 是否在范围内
         */
        private function is_date_in_range( $date, $settings ) {

            // 检查最小日期
            if ( ! empty( $settings['min_date'] ) ) {
                $min_date = $this->parse_date( $settings['min_date'], $settings );
                if ( $min_date && $date < $min_date ) {
                    return false;
                }
            }

            // 检查最大日期
            if ( ! empty( $settings['max_date'] ) ) {
                $max_date = $this->parse_date( $settings['max_date'], $settings );
                if ( $max_date && $date > $max_date ) {
                    return false;
                }
            }

            return true;
        }

        /**
         * 检查日期是否被禁用
         *
         * @since 1.0
         *
         * @param DateTime $date     要检查的日期
         * @param array    $settings 字段设置
         *
         * @return bool 是否被禁用
         */
        private function is_date_disabled( $date, $settings ) {

            // 检查禁用的星期
            if ( ! empty( $settings['disabled_days'] ) && is_array( $settings['disabled_days'] ) ) {
                $day_of_week = (int) $date->format( 'w' ); // 0 = Sunday, 6 = Saturday
                if ( in_array( $day_of_week, $settings['disabled_days'] ) ) {
                    return true;
                }
            }

            // 检查禁用的具体日期
            if ( ! empty( $settings['disabled_dates'] ) && is_array( $settings['disabled_dates'] ) ) {
                $date_string = $date->format( $settings['date_format'] );
                if ( in_array( $date_string, $settings['disabled_dates'] ) ) {
                    return true;
                }
            }

            return false;
        }

        /**
         * 获取字段配置示例
         *
         * 返回日期字段的配置示例，用于文档和开发参考。
         *
         * @since 1.0
         *
         * @return array 配置示例数组
         */
        public static function get_config_example() {

            return array(
                'id'          => 'date_field_example',
                'type'        => 'date',
                'title'       => '日期选择器示例',
                'desc'        => '这是一个现代化的日期选择器字段',
                'default'     => '',
                'settings'    => array(
                    'date_format'    => 'Y-m-d',        // 存储格式
                    'display_format' => 'Y年m月d日',     // 显示格式
                    'placeholder'    => '请选择日期',     // 占位符
                    'min_date'       => '2020-01-01',   // 最小日期
                    'max_date'       => '2030-12-31',   // 最大日期
                    'disabled_days'  => array( 0, 6 ),  // 禁用周末
                    'first_day'      => 1,              // 周一为第一天
                    'show_today'     => true,           // 显示今天按钮
                    'show_clear'     => true,           // 显示清除按钮
                    'auto_close'     => true,           // 自动关闭
                ),
                // 日期范围选择示例
                'date_range'  => false,                 // 设为true启用范围选择
                'text_from'   => '开始日期',             // 范围选择开始文本
                'text_to'     => '结束日期',             // 范围选择结束文本
            );
        }
    }
}
