/**
 * Xun Framework 文档系统 - TailwindCSS 4 输入文件
 * 
 * 这个文件是TailwindCSS 4的主要输入文件，包含了框架的基础样式、
 * 组件样式和工具类。与框架保持一致的深色主题设计。
 */

/* 导入 TailwindCSS 基础样式 */
@import "tailwindcss";

/* 扫描源文件以生成工具类 */
@source "../../../framework/assets/css/style.min.css";
@source "../views/**/*.php";
@source "../pages/**/*.php";
@source "../includes/**/*.php";
@source "../assets/js/**/*.js";

/* 自定义CSS变量 - 与框架保持一致 */
:root {
  /* 主色调 */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
  
  /* 灰色调 */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* 成功色 */
  --success-500: #10b981;
  --success-600: #059669;
  
  /* 警告色 */
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  
  /* 错误色 */
  --error-500: #ef4444;
  --error-600: #dc2626;
}

/* 深色主题变量 */
[data-theme="dark"] {
  --bg-primary: var(--gray-900);
  --bg-secondary: var(--gray-800);
  --bg-tertiary: var(--gray-700);
  --text-primary: var(--gray-100);
  --text-secondary: var(--gray-300);
  --text-tertiary: var(--gray-400);
  --border-color: var(--gray-600);
}

/* 浅色主题变量 */
[data-theme="light"] {
  --bg-primary: var(--gray-50);
  --bg-secondary: #ffffff;
  --bg-tertiary: var(--gray-100);
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-tertiary: var(--gray-500);
  --border-color: var(--gray-200);
}

/* 基础样式重置 */
* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* 代码字体 */
code, pre, .font-mono {
  font-family: 'JetBrains Mono', 'Fira Code', Consolas, 'Liberation Mono', Menlo, monospace;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* 选择文本样式 */
::selection {
  background-color: var(--primary-200);
  color: var(--primary-900);
}

[data-theme="dark"] ::selection {
  background-color: var(--primary-800);
  color: var(--primary-100);
}

/* 焦点样式 */
:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* 链接样式 */
a {
  color: var(--primary-600);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-700);
}

[data-theme="dark"] a {
  color: var(--primary-400);
}

[data-theme="dark"] a:hover {
  color: var(--primary-300);
}

/* 标题样式 */
h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
  margin-bottom: 0.5em;
}

/* 代码块样式 */
pre {
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1rem;
  overflow-x: auto;
  font-size: 0.875rem;
  line-height: 1.5;
}

code {
  background-color: var(--bg-tertiary);
  padding: 0.125rem 0.25rem;
  border-radius: 4px;
  font-size: 0.875em;
}

pre code {
  background: none;
  padding: 0;
  border-radius: 0;
}

/* 表格样式 */
table {
  width: 100%;
  border-collapse: collapse;
  margin: 1rem 0;
}

th, td {
  padding: 0.75rem;
  text-align: left;
  border-bottom: 1px solid var(--border-color);
}

th {
  font-weight: 600;
  background-color: var(--bg-secondary);
}

/* 按钮基础样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  font-size: 0.875rem;
}

.btn-primary {
  background-color: var(--primary-600);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-700);
  color: white;
}

.btn-secondary {
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--border-color);
}

.btn-secondary:hover {
  background-color: var(--bg-tertiary);
  color: var(--text-primary);
}

/* 卡片样式 */
.card {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.card:hover {
  border-color: var(--primary-300);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

[data-theme="dark"] .card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* 导航样式 */
.nav-item {
  padding: 0.5rem 1rem;
  border-radius: 6px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.nav-item:hover {
  background-color: var(--bg-tertiary);
}

.nav-item.active {
  background-color: var(--primary-600);
  color: white;
}

/* 响应式工具类 */
@media (max-width: 768px) {
  .mobile-hidden {
    display: none !important;
  }
  
  .mobile-full {
    width: 100% !important;
  }
}

/* 动画类 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

/* 加载动画 */
.loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 打印样式 */
@media print {
  .no-print {
    display: none !important;
  }
  
  body {
    background: white !important;
    color: black !important;
  }
  
  .card {
    border: 1px solid #ccc !important;
    box-shadow: none !important;
  }
}
