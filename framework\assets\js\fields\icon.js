/**
 * Xun Framework 图标选择字段脚本
 *
 * 这个文件包含图标选择字段的所有JavaScript功能。
 * 采用与color字段一致的弹窗实现方式，包括相同的动画效果和交互逻辑。
 *
 * @package Xun Framework
 * <AUTHOR>
 * @since   1.0
 */

(function($) {
    'use strict';

    /**
     * 图标选择器类 - 与XunColorPicker保持一致的架构设计
     */
    var XunIconPicker = {

        // 存储所有图标选择器实例
        instances: {},

        /**
         * 初始化
         */
        init: function() {
            var self = this;

            // 页面加载完成后初始化所有图标选择器
            $(document).ready(function() {
                self.initIconPickers();
            });

            // 使用 MutationObserver 监听DOM变化，与color字段保持一致
            if (window.MutationObserver) {
                var observer = new MutationObserver(function(mutations) {
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList') {
                            mutation.addedNodes.forEach(function(node) {
                                if (node.nodeType === 1 && ($(node).hasClass('xun-icon-field') || $(node).find('.xun-icon-field').length > 0)) {
                                    self.initIconPickers();
                                }
                            });
                        }
                    });
                });

                observer.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            }

            // 键盘快捷键支持 - ESC键关闭弹窗
            $(document).on('keydown', function(e) {
                if (e.keyCode === 27) { // ESC
                    self.closeIconPicker();
                }
            });
        },

        /**
         * 初始化所有图标选择器
         */
        initIconPickers: function() {
            var self = this;

            $('.xun-icon-field').each(function() {
                var $container = $(this);
                var $config = $container.find('.xun-icon-config');

                if ($config.length === 0) return;

                try {
                    var config = JSON.parse($config.text());
                    if (!self.instances[config.fieldId]) {
                        self.createIconPicker(config);
                    }
                } catch (error) {
                    // 忽略配置解析错误
                }
            });
        },

        /**
         * 创建图标选择器实例
         */
        createIconPicker: function(config) {
            var self = this;
            var $field = $('[data-field-id="' + config.fieldId + '"]');
            var $input = $field.find('.xun-icon-input');

            if ($field.length === 0 || $input.length === 0) {
                return;
            }

            // 创建实例
            self.instances[config.fieldId] = {
                field: $field,
                input: $input,
                config: config,
                currentIcon: config.currentValue
            };

            // 绑定事件
            self.bindBasicEvents($field, config);

            // 如果有初始值，更新预览区域
            if (config.currentValue) {
                self.updateLivePreview(config.fieldId, config.currentValue);
            }
        },

        /**
         * 绑定基本事件
         */
        bindBasicEvents: function($field, config) {
            var self = this;
            var fieldId = config.fieldId;

            // 点击选择图标按钮
            $field.on('click', '.xun-icon-select-btn', function(e) {
                e.preventDefault();
                self.openIconPicker(fieldId);
            });

            // 点击清除图标按钮
            $field.on('click', '.xun-icon-clear-btn', function(e) {
                e.preventDefault();
                self.clearIcon(fieldId);
            });

            // 点击图标预览区域（不包括按钮）
            $field.on('click', '.xun-icon-preview, .xun-icon-placeholder', function(e) {
                e.preventDefault();
                e.stopPropagation();
                self.openIconPicker(fieldId);
            });
        },
        
        /**
         * 打开图标选择弹窗 - 与color字段保持一致的实现
         */
        openIconPicker: function(fieldId) {
            var self = this;
            var instance = this.instances[fieldId];
            if (!instance) return;

            // 检查是否已经有图标选择器打开
            if ($('.xun-icon-picker-modal').length > 0) {
                $('.xun-icon-picker-modal').remove();
            }

            // 创建模态框
            var currentIcon = instance.input.val() || '';
            var modalHtml = this.createIconPickerModal(fieldId, currentIcon);

            // 添加到页面（初始隐藏状态）
            var $modal = $(modalHtml);
            $modal.css({
                opacity: 0,
                transition: 'opacity 0.3s ease-in-out'
            });
            $('body').append($modal);

            // 绑定模态框事件
            this.bindModalEvents(fieldId);

            // 显示模态框动画 - 与color字段完全一致
            setTimeout(function() {
                $modal.css({
                    opacity: 1
                });

                // 对话框弹出动画
                $modal.find('.xun-icon-picker-dialog').removeClass('opacity-0 translate-y-4 scale-95')
                                                     .addClass('opacity-100 translate-y-0 scale-100');
            }, 10);
        },

        /**
         * 关闭图标选择弹窗 - 与color字段保持一致的实现
         */
        closeIconPicker: function() {
            var $modal = $('.xun-icon-picker-modal');

            // 关闭动画 - 与color字段完全一致
            var $dialog = $modal.find('.xun-icon-picker-dialog');

            $modal.css({
                opacity: 0
            });
            $dialog.removeClass('opacity-100 translate-y-0 scale-100')
                   .addClass('opacity-0 translate-y-4 scale-95');

            // 动画完成后移除元素
            setTimeout(function() {
                $modal.remove();
            }, 200);
        },

        /**
         * 创建图标选择器模态框 - 与color字段保持一致的HTML结构和样式
         */
        createIconPickerModal: function(fieldId, currentIcon) {
            var instance = this.instances[fieldId];
            var config = instance.config;

            var html = '<div class="xun-icon-picker-modal fixed inset-0 flex items-center justify-center z-50 p-4" style="z-index: 9999; backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px); background: rgba(255, 255, 255, 0.1); opacity: 0; transition: opacity 0.3s ease-out;">';
            html += '<div class="xun-icon-picker-dialog bg-white/90 backdrop-blur-xl rounded-2xl shadow-2xl w-full max-w-lg border border-white/20 transform transition-all duration-300 ease-out opacity-0 translate-y-4 scale-95 max-h-[90vh] overflow-y-auto" style="backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(255, 255, 255, 0.1);">';

            // 头部 - 与color字段保持一致的样式
            html += '<div class="flex items-center justify-between p-3 sm:p-4 border-b border-white/20">';
            html += '<h3 class="text-base sm:text-lg font-semibold text-gray-800">' + config.messages.selectIcon + '</h3>';
            html += '<button type="button" class="xun-icon-picker-close text-gray-500 hover:text-gray-700 hover:bg-white/50 rounded-full p-2 transition-all duration-200">';
            html += '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
            html += '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>';
            html += '</svg>';
            html += '</button>';
            html += '</div>';

            // 内容区域
            html += '<div class="p-3 sm:p-4">';

            // 搜索框
            if (config.showSearch) {
                html += '<div class="mb-4">';
                html += '<label class="block text-xs font-medium text-gray-600 mb-1">搜索图标</label>';
                html += '<input type="text" class="xun-icon-search-input w-full px-3 py-2 border border-white/30 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-400/50 text-sm bg-white/50 backdrop-blur-sm" placeholder="' + config.messages.searchPlaceholder + '">';
                html += '</div>';
            }

            // 图标网格
            html += '<div class="mb-4">';
            html += '<label class="block text-xs sm:text-sm font-semibold text-gray-700 mb-1 sm:mb-2">选择图标</label>';
            html += '<div class="xun-icon-grid grid grid-cols-6 sm:grid-cols-8 gap-2 max-h-64 overflow-y-auto border border-white/20 rounded-lg p-2 bg-white/30">';

            // 渲染图标列表
            config.availableIcons.forEach(function(iconName) {
                var selectedClass = (currentIcon === iconName) ? ' selected' : '';
                html += '<button type="button" class="xun-icon-item flex flex-col items-center justify-center p-2 rounded-lg border border-white/30 hover:bg-white/50 hover:scale-105 transition-all duration-200 shadow-sm' + selectedClass + '" data-icon="' + iconName + '" title="' + iconName + '">';
                html += '<span class="dashicons dashicons-' + iconName + ' text-xl text-gray-700"></span>';
                html += '<span class="text-xs text-gray-600 mt-1 truncate w-full text-center">' + iconName + '</span>';
                html += '</button>';
            });

            html += '</div>';
            html += '</div>';

            html += '</div>';

            // 底部按钮 - 与color字段保持一致的样式
            html += '<div class="flex items-center justify-end gap-2 sm:gap-3 p-3 sm:p-4 border-t border-white/20">';
            html += '<button type="button" class="xun-icon-picker-cancel px-3 py-2 sm:px-4 text-xs sm:text-sm font-medium text-gray-700 bg-white/50 backdrop-blur-sm border border-white/30 rounded-lg hover:bg-white/70 focus:outline-none focus:ring-2 focus:ring-blue-400/50 transition-all duration-200">' + config.messages.cancel + '</button>';
            html += '<button type="button" class="xun-icon-picker-confirm px-3 py-2 sm:px-4 text-xs sm:text-sm font-medium text-white bg-gradient-to-r from-blue-500 to-blue-600 border border-transparent rounded-lg hover:from-blue-600 hover:to-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400/50 shadow-lg transition-all duration-200">' + config.messages.confirm + '</button>';
            html += '</div>';

            html += '</div>';
            html += '</div>';

            return html;
        },

        /**
         * 绑定模态框事件 - 与color字段保持一致的事件处理逻辑
         */
        bindModalEvents: function(fieldId) {
            var self = this;
            var $modal = $('.xun-icon-picker-modal');

            // 关闭按钮
            $modal.on('click', '.xun-icon-picker-close, .xun-icon-picker-cancel', function() {
                self.closeIconPicker();
            });

            // 点击背景关闭
            $modal.on('click', function(e) {
                if (e.target === this) {
                    self.closeIconPicker();
                }
            });

            // 确定按钮
            $modal.on('click', '.xun-icon-picker-confirm', function() {
                var selectedIcon = $modal.find('.xun-icon-item.selected').data('icon');

                if (selectedIcon) {
                    self.setIconValue(fieldId, selectedIcon);
                }
                self.closeIconPicker();
            });

            // 图标选择
            $modal.on('click', '.xun-icon-item', function() {
                var $item = $(this);
                var iconName = $item.data('icon');

                // 更新选中状态
                $item.siblings().removeClass('selected');
                $item.addClass('selected');
            });

            // 搜索功能
            $modal.on('input', '.xun-icon-search-input', function() {
                var keyword = $(this).val().toLowerCase();
                self.filterIcons($modal, keyword);
            });

            // 聚焦搜索框
            setTimeout(function() {
                $modal.find('.xun-icon-search-input').focus();
            }, 100);
        },

        /**
         * 设置图标值 - 与color字段的setColorValue方法保持一致
         */
        setIconValue: function(fieldId, iconName) {
            var instance = this.instances[fieldId];
            if (!instance) return;

            // 更新输入框
            instance.input.val(iconName).trigger('change');

            // 更新显示
            this.updateIconDisplay(fieldId, iconName);

            // 更新当前图标
            instance.currentIcon = iconName;
        },

        /**
         * 更新图标显示 - 修复按钮布局问题，确保按钮始终在正确位置
         */
        updateIconDisplay: function(fieldId, iconName) {
            var instance = this.instances[fieldId];
            if (!instance) return;

            var $current = instance.field.find('.xun-icon-current');
            var placeholder = instance.config.placeholder || '选择图标...';

            if (iconName) {
                // 显示选中的图标 - 只更新图标内容部分，保持按钮位置
                var iconHtml = '<div class="xun-icon-preview">';
                iconHtml += '<span class="dashicons dashicons-' + iconName + ' w-6 h-6"></span>';
                iconHtml += '<span class="xun-icon-name">' + iconName + '</span>';
                iconHtml += '</div>';
                iconHtml += '<button type="button" class="xun-icon-select-btn">选择图标</button>';
                iconHtml += '<button type="button" class="xun-icon-clear-btn">清除</button>';

                $current.html(iconHtml);

                // 更新实时预览区域
                this.updateLivePreview(fieldId, iconName);
            } else {
                // 显示占位符 - 保持选择按钮，隐藏清除按钮
                var placeholderHtml = '<div class="xun-icon-placeholder">' + placeholder + '</div>';
                placeholderHtml += '<button type="button" class="xun-icon-select-btn">选择图标</button>';

                $current.html(placeholderHtml);

                // 隐藏实时预览区域
                this.updateLivePreview(fieldId, '');
            }
        },

        /**
         * 更新实时预览区域
         */
        updateLivePreview: function(fieldId, iconName) {
            var instance = this.instances[fieldId];
            if (!instance) return;

            // 检查是否启用预览功能
            if (!instance.config.showPreview) {
                return;
            }

            var $field = instance.field;
            var $preview = $field.find('.xun-icon-live-preview');

            if (iconName) {
                // 如果预览区域不存在，创建它
                if ($preview.length === 0) {
                    var previewHtml = '<div class="xun-icon-live-preview">';
                    previewHtml += '<h4>预览效果：</h4>';
                    previewHtml += '<div class="xun-icon-preview-sizes"></div>';
                    previewHtml += '</div>';

                    $field.append(previewHtml);
                    $preview = $field.find('.xun-icon-live-preview');
                }

                // 更新预览内容
                var $previewSizes = $preview.find('.xun-icon-preview-sizes');
                var previewSizes = ['16', '20', '24'];
                var sizesHtml = '';

                previewSizes.forEach(function(size) {
                    sizesHtml += '<div class="xun-icon-preview-item">';
                    sizesHtml += '<span class="xun-icon-preview-label">' + size + 'px:</span>';
                    sizesHtml += '<span class="dashicons dashicons-' + iconName + ' inline-block" style="font-size: ' + size + 'px;"></span>';
                    sizesHtml += '</div>';
                });

                $previewSizes.html(sizesHtml);
                $preview.show();
            } else {
                // 隐藏预览区域
                $preview.hide();
            }
        },

        /**
         * 清除图标 - 通过fieldId操作，与color字段保持一致
         */
        clearIcon: function(fieldId) {
            this.setIconValue(fieldId, '');
        },
        
        /**
         * 过滤图标 - 与color字段的搜索逻辑保持一致
         */
        filterIcons: function($modal, keyword) {
            var $items = $modal.find('.xun-icon-item');
            var hasResults = false;

            if (keyword === '') {
                // 显示所有图标
                $items.show();
                hasResults = true;
            } else {
                // 根据关键词过滤
                $items.each(function() {
                    var $item = $(this);
                    var iconName = $item.data('icon').toLowerCase();

                    if (iconName.indexOf(keyword) !== -1) {
                        $item.show();
                        hasResults = true;
                    } else {
                        $item.hide();
                    }
                });
            }

            // 显示/隐藏无结果提示
            var $noResults = $modal.find('.xun-icon-no-results');
            if (!hasResults) {
                if ($noResults.length === 0) {
                    $modal.find('.xun-icon-grid').append('<div class="xun-icon-no-results col-span-full text-center text-gray-500 py-4">未找到匹配的图标</div>');
                }
            } else {
                $noResults.remove();
            }
        }
    };

    // 初始化图标选择器
    XunIconPicker.init();

    // 全局暴露
    window.XunIconPicker = XunIconPicker;

})(jQuery);
