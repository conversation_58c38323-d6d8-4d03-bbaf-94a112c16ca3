<?php
/**
 * Xun Framework 文档系统 - 页面头部
 *
 * 包含HTML头部信息、导航栏和主要的页面结构开始部分。
 * 支持响应式设计和主题切换功能。
 *
 * @package Xun Framework Docs
 * <AUTHOR>
 * @since   1.0
 */

// 防止直接访问
if (!defined('ABSPATH')) {
    exit;
}

$current_page = xun_docs_get_current_page();
$site_title = xun_docs_get_config('site_title');
$site_description = xun_docs_get_config('site_description');
$enable_theme_switch = xun_docs_get_config('enable_theme_switch');
$default_theme = xun_docs_get_config('default_theme');

// 页面标题
$page_title = $site_title;
if (!empty($page_title_override)) {
    $page_title = $page_title_override . ' - ' . $site_title;
}
?>
<!DOCTYPE html>
<html lang="zh-CN" data-theme="<?php echo xun_docs_esc_attr($default_theme); ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <!-- 页面信息 -->
    <title><?php echo xun_docs_esc_html($page_title); ?></title>
    <meta name="description" content="<?php echo xun_docs_esc_attr($site_description); ?>">
    <meta name="keywords" content="<?php echo xun_docs_esc_attr(xun_docs_get_config('site_keywords')); ?>">
    <meta name="author" content="<?php echo xun_docs_esc_attr(xun_docs_get_config('author')); ?>">
    
    <!-- Open Graph -->
    <meta property="og:title" content="<?php echo xun_docs_esc_attr($page_title); ?>">
    <meta property="og:description" content="<?php echo xun_docs_esc_attr($site_description); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo xun_docs_esc_url(xun_docs_get_current_url()); ?>">
    
    <!-- Twitter Card -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="<?php echo xun_docs_esc_attr($page_title); ?>">
    <meta name="twitter:description" content="<?php echo xun_docs_esc_attr($site_description); ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo xun_docs_get_asset_url('images/favicon.ico'); ?>">
    <link rel="apple-touch-icon" href="<?php echo xun_docs_get_asset_url('images/apple-touch-icon.png'); ?>">
    
    <!-- 字体 -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500;600&display=swap" rel="stylesheet">
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="<?php echo xun_docs_get_asset_url('css/docs.css'); ?>">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="<?php echo xun_docs_get_asset_url('js/docs.js'); ?>" as="script">
    
    <!-- 主题切换脚本（避免闪烁） -->
    <script>
        (function() {
            const theme = localStorage.getItem('xun-docs-theme') || '<?php echo $default_theme; ?>';
            document.documentElement.setAttribute('data-theme', theme);
        })();
    </script>
</head>
<body class="bg-gray-50 text-gray-900 transition-colors duration-300 dark:bg-gray-900 dark:text-gray-100">
    <!-- 跳转到主内容的链接（无障碍访问） -->
    <a href="#main-content" class="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-blue-600 text-white px-4 py-2 rounded-md z-50">
        跳转到主内容
    </a>

    <!-- 页面容器 -->
    <div class="min-h-screen flex flex-col">
        <!-- 顶部导航栏 -->
        <header class="sticky top-0 z-40 bg-white/95 backdrop-blur-sm border-b border-gray-200 dark:bg-gray-900/95 dark:border-gray-700">
            <div class="max-w-8xl mx-auto">
                <div class="flex items-center justify-between h-16 px-4 sm:px-6 lg:px-8">
                    <!-- Logo和标题 -->
                    <div class="flex items-center space-x-4">
                        <!-- 移动端菜单按钮 -->
                        <button 
                            type="button" 
                            class="lg:hidden p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-800 transition-colors"
                            id="mobile-menu-button"
                            aria-label="打开导航菜单"
                        >
                            <?php echo xun_docs_get_icon('bars-3', 'outline', 'w-6 h-6'); ?>
                        </button>
                        
                        <!-- Logo -->
                        <a href="<?php echo xun_docs_get_page_url(); ?>" class="flex items-center space-x-3 group">
                            <div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center group-hover:from-blue-600 group-hover:to-blue-700 transition-all duration-200">
                                <span class="text-white font-bold text-sm">X</span>
                            </div>
                            <div class="hidden sm:block">
                                <h1 class="text-xl font-bold text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                                    <?php echo xun_docs_esc_html($site_title); ?>
                                </h1>
                                <p class="text-xs text-gray-500 dark:text-gray-400 -mt-1">
                                    <?php echo xun_docs_esc_html($site_description); ?>
                                </p>
                            </div>
                        </a>
                    </div>

                    <!-- 中间搜索框（桌面端） -->
                    <?php if (xun_docs_get_config('enable_search')): ?>
                    <div class="hidden md:flex flex-1 max-w-lg mx-8">
                        <div class="relative w-full">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <?php echo xun_docs_get_icon('search', 'outline', 'w-5 h-5 text-gray-400'); ?>
                            </div>
                            <input 
                                type="search" 
                                placeholder="搜索文档..." 
                                class="block w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white dark:placeholder-gray-400 dark:focus:ring-blue-400 transition-colors"
                                id="search-input"
                                autocomplete="off"
                            >
                            <!-- 搜索结果下拉框 -->
                            <div id="search-results" class="absolute top-full left-0 right-0 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg hidden max-h-96 overflow-y-auto z-50">
                                <!-- 搜索结果将通过JavaScript动态填充 -->
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <!-- 右侧工具栏 -->
                    <div class="flex items-center space-x-2">
                        <!-- 搜索按钮（移动端） -->
                        <?php if (xun_docs_get_config('enable_search')): ?>
                        <button 
                            type="button" 
                            class="md:hidden p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-800 transition-colors"
                            id="mobile-search-button"
                            aria-label="搜索"
                        >
                            <?php echo xun_docs_get_icon('search', 'outline', 'w-5 h-5'); ?>
                        </button>
                        <?php endif; ?>

                        <!-- 主题切换按钮 -->
                        <?php if ($enable_theme_switch): ?>
                        <button 
                            type="button" 
                            class="p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-800 transition-colors"
                            id="theme-toggle"
                            aria-label="切换主题"
                        >
                            <span class="dark:hidden">
                                <?php echo xun_docs_get_icon('moon', 'outline', 'w-5 h-5'); ?>
                            </span>
                            <span class="hidden dark:block">
                                <?php echo xun_docs_get_icon('sun', 'outline', 'w-5 h-5'); ?>
                            </span>
                        </button>
                        <?php endif; ?>

                        <!-- GitHub链接 -->
                        <?php if ($github_url = xun_docs_get_config('social_links')['github'] ?? ''): ?>
                        <a 
                            href="<?php echo xun_docs_esc_url($github_url); ?>" 
                            target="_blank" 
                            rel="noopener noreferrer"
                            class="p-2 rounded-md text-gray-500 hover:text-gray-700 hover:bg-gray-100 dark:text-gray-400 dark:hover:text-gray-300 dark:hover:bg-gray-800 transition-colors"
                            aria-label="GitHub仓库"
                        >
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
                                <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                            </svg>
                        </a>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </header>

        <!-- 移动端搜索栏 -->
        <?php if (xun_docs_get_config('enable_search')): ?>
        <div id="mobile-search" class="md:hidden bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 px-4 py-3 hidden">
            <div class="relative">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <?php echo xun_docs_get_icon('search', 'outline', 'w-5 h-5 text-gray-400'); ?>
                </div>
                <input 
                    type="search" 
                    placeholder="搜索文档..." 
                    class="block w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-800 dark:border-gray-600 dark:text-white dark:placeholder-gray-400 dark:focus:ring-blue-400 transition-colors"
                    id="mobile-search-input"
                    autocomplete="off"
                >
            </div>
        </div>
        <?php endif; ?>

        <!-- 主要内容区域 -->
        <div class="flex-1 flex">
            <!-- 侧边栏导航 -->
            <?php include XUN_DOCS_PATH . '/includes/sidebar.php'; ?>

            <!-- 主内容区域 -->
            <main id="main-content" class="flex-1 min-w-0">
                <!-- 面包屑导航 -->
                <?php if ($current_page !== 'home' && $current_page !== ''): ?>
                <div class="bg-gray-50 dark:bg-gray-800/50 border-b border-gray-200 dark:border-gray-700">
                    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-3">
                        <nav class="flex" aria-label="面包屑">
                            <ol class="flex items-center space-x-2 text-sm">
                                <?php 
                                $breadcrumbs = xun_docs_get_breadcrumbs($current_page);
                                foreach ($breadcrumbs as $index => $crumb): 
                                    $is_last = $index === count($breadcrumbs) - 1;
                                ?>
                                <li class="flex items-center">
                                    <?php if ($index > 0): ?>
                                        <?php echo xun_docs_get_icon('chevron-right', 'outline', 'w-4 h-4 text-gray-400 mx-2'); ?>
                                    <?php endif; ?>
                                    
                                    <?php if ($is_last): ?>
                                        <span class="text-gray-500 dark:text-gray-400 font-medium">
                                            <?php echo xun_docs_esc_html($crumb['title']); ?>
                                        </span>
                                    <?php else: ?>
                                        <a 
                                            href="<?php echo xun_docs_get_page_url($crumb['url']); ?>" 
                                            class="text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300 transition-colors"
                                        >
                                            <?php echo xun_docs_esc_html($crumb['title']); ?>
                                        </a>
                                    <?php endif; ?>
                                </li>
                                <?php endforeach; ?>
                            </ol>
                        </nav>
                    </div>
                </div>
                <?php endif; ?>

                <!-- 页面内容容器 -->
                <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
